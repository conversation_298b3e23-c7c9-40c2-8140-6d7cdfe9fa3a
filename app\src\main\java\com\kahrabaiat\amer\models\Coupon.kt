package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.*

@Parcelize
data class Coupon(
    val id: String = "",
    val code: String = "",
    val title: String = "",
    val description: String = "",
    val type: CouponType = CouponType.PERCENTAGE,
    val value: Double = 0.0, // نسبة الخصم أو المبلغ الثابت
    val minimumOrderAmount: Double = 0.0, // الحد الأدنى للطلب
    val maximumDiscountAmount: Double = 0.0, // الحد الأقصى للخصم (للنسبة المئوية)
    val usageLimit: Int = 0, // عدد مرات الاستخدام المسموح (0 = غير محدود)
    val usedCount: Int = 0, // عدد مرات الاستخدام الحالي
    val userUsageLimit: Int = 1, // عدد مرات الاستخدام لكل مستخدم
    val applicableCategories: List<String> = emptyList(), // الفئات المطبقة (فارغة = جميع الفئات)
    val applicableProducts: List<String> = emptyList(), // المنتجات المطبقة (فارغة = جميع المنتجات)
    val isActive: Boolean = true,
    val isFirstTimeOnly: Boolean = false, // للعملاء الجدد فقط
    val createdAt: Long = System.currentTimeMillis(),
    val validFrom: Long = System.currentTimeMillis(),
    val validUntil: Long = 0L,
    val createdBy: String = "", // معرف المدير الذي أنشأ الكوبون
    val imageUrl: String = "", // صورة الكوبون
    val termsAndConditions: String = "", // الشروط والأحكام
    val metadata: Map<String, String> = emptyMap() // بيانات إضافية
) : Parcelable {

    /**
     * التحقق من صحة الكوبون
     */
    fun isValid(): Boolean {
        val currentTime = System.currentTimeMillis()
        return isActive && 
               currentTime >= validFrom && 
               (validUntil == 0L || currentTime <= validUntil) &&
               (usageLimit == 0 || usedCount < usageLimit)
    }

    /**
     * التحقق من انتهاء صلاحية الكوبون
     */
    fun isExpired(): Boolean {
        return validUntil > 0L && System.currentTimeMillis() > validUntil
    }

    /**
     * التحقق من استنفاد عدد مرات الاستخدام
     */
    fun isUsageLimitReached(): Boolean {
        return usageLimit > 0 && usedCount >= usageLimit
    }

    /**
     * حساب قيمة الخصم
     */
    fun calculateDiscount(orderAmount: Double): Double {
        if (!isValid() || orderAmount < minimumOrderAmount) {
            return 0.0
        }

        return when (type) {
            CouponType.PERCENTAGE -> {
                val discount = orderAmount * (value / 100.0)
                if (maximumDiscountAmount > 0) {
                    minOf(discount, maximumDiscountAmount)
                } else {
                    discount
                }
            }
            CouponType.FIXED_AMOUNT -> {
                minOf(value, orderAmount) // لا يمكن أن يكون الخصم أكبر من قيمة الطلب
            }
            CouponType.FREE_SHIPPING -> {
                // سيتم تطبيق هذا في حساب الشحن
                0.0
            }
        }
    }

    /**
     * التحقق من إمكانية تطبيق الكوبون على منتج معين
     */
    fun isApplicableToProduct(productId: String, categoryId: String): Boolean {
        // إذا كانت القوائم فارغة، فالكوبون ينطبق على جميع المنتجات
        val categoryApplicable = applicableCategories.isEmpty() || applicableCategories.contains(categoryId)
        val productApplicable = applicableProducts.isEmpty() || applicableProducts.contains(productId)
        
        return categoryApplicable && productApplicable
    }

    /**
     * تنسيق تاريخ الصلاحية
     */
    fun getFormattedValidUntil(): String {
        return if (validUntil > 0) {
            val date = Date(validUntil)
            val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("ar"))
            formatter.format(date)
        } else {
            "غير محدود"
        }
    }

    /**
     * تنسيق قيمة الخصم
     */
    fun getFormattedValue(): String {
        return when (type) {
            CouponType.PERCENTAGE -> "${value.toInt()}%"
            CouponType.FIXED_AMOUNT -> "${value.toInt()} دينار"
            CouponType.FREE_SHIPPING -> "شحن مجاني"
        }
    }

    /**
     * الحصول على وصف الكوبون
     */
    fun getDisplayDescription(): String {
        return when (type) {
            CouponType.PERCENTAGE -> {
                val maxText = if (maximumDiscountAmount > 0) " (حد أقصى ${maximumDiscountAmount.toInt()} دينار)" else ""
                "خصم ${value.toInt()}%$maxText"
            }
            CouponType.FIXED_AMOUNT -> "خصم ${value.toInt()} دينار"
            CouponType.FREE_SHIPPING -> "شحن مجاني"
        }
    }

    /**
     * الحصول على حالة الكوبون
     */
    fun getStatus(): CouponStatus {
        return when {
            !isActive -> CouponStatus.INACTIVE
            isExpired() -> CouponStatus.EXPIRED
            isUsageLimitReached() -> CouponStatus.USED_UP
            System.currentTimeMillis() < validFrom -> CouponStatus.NOT_STARTED
            else -> CouponStatus.ACTIVE
        }
    }

    /**
     * الحصول على نص حالة الكوبون
     */
    fun getStatusText(): String {
        return when (getStatus()) {
            CouponStatus.ACTIVE -> "نشط"
            CouponStatus.INACTIVE -> "غير نشط"
            CouponStatus.EXPIRED -> "منتهي الصلاحية"
            CouponStatus.USED_UP -> "استُنفد"
            CouponStatus.NOT_STARTED -> "لم يبدأ بعد"
        }
    }

    /**
     * الحصول على لون حالة الكوبون
     */
    fun getStatusColor(): String {
        return when (getStatus()) {
            CouponStatus.ACTIVE -> "#4CAF50"
            CouponStatus.INACTIVE -> "#9E9E9E"
            CouponStatus.EXPIRED -> "#F44336"
            CouponStatus.USED_UP -> "#FF9800"
            CouponStatus.NOT_STARTED -> "#2196F3"
        }
    }

    /**
     * الحصول على أيقونة نوع الكوبون
     */
    fun getTypeIcon(): String {
        return when (type) {
            CouponType.PERCENTAGE -> "🎯"
            CouponType.FIXED_AMOUNT -> "💰"
            CouponType.FREE_SHIPPING -> "🚚"
        }
    }

    /**
     * التحقق من أن الكوبون جديد (أقل من 7 أيام)
     */
    fun isNew(): Boolean {
        val sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L)
        return createdAt > sevenDaysAgo
    }

    /**
     * الحصول على عدد الأيام المتبقية
     */
    fun getDaysRemaining(): Int {
        if (validUntil == 0L) return -1 // غير محدود
        
        val currentTime = System.currentTimeMillis()
        val timeRemaining = validUntil - currentTime
        
        return if (timeRemaining > 0) {
            (timeRemaining / (24 * 60 * 60 * 1000L)).toInt()
        } else {
            0
        }
    }

    companion object {
        /**
         * إنشاء كوبون ترحيبي للعملاء الجدد
         */
        fun createWelcomeCoupon(customerPhone: String): Coupon {
            return Coupon(
                id = "welcome_${customerPhone}_${System.currentTimeMillis()}",
                code = "WELCOME${customerPhone.takeLast(4)}",
                title = "مرحباً بك في كهربائيات عامر!",
                description = "خصم ترحيبي للعملاء الجدد",
                type = CouponType.PERCENTAGE,
                value = 10.0,
                minimumOrderAmount = 50.0,
                maximumDiscountAmount = 20.0,
                usageLimit = 1,
                userUsageLimit = 1,
                isFirstTimeOnly = true,
                validUntil = System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L), // 30 يوم
                termsAndConditions = "صالح للعملاء الجدد فقط، حد أدنى للطلب 50 دينار"
            )
        }

        /**
         * إنشاء كوبون خصم موسمي
         */
        fun createSeasonalCoupon(
            title: String,
            discountPercentage: Double,
            validDays: Int
        ): Coupon {
            val code = "SEASON${System.currentTimeMillis().toString().takeLast(6)}"
            return Coupon(
                id = "seasonal_${System.currentTimeMillis()}",
                code = code,
                title = title,
                description = "عرض موسمي محدود",
                type = CouponType.PERCENTAGE,
                value = discountPercentage,
                minimumOrderAmount = 100.0,
                maximumDiscountAmount = 50.0,
                validUntil = System.currentTimeMillis() + (validDays * 24 * 60 * 60 * 1000L),
                termsAndConditions = "عرض محدود لفترة $validDays يوم"
            )
        }

        /**
         * إنشاء كوبون شحن مجاني
         */
        fun createFreeShippingCoupon(minimumAmount: Double): Coupon {
            return Coupon(
                id = "freeship_${System.currentTimeMillis()}",
                code = "FREESHIP",
                title = "شحن مجاني",
                description = "احصل على شحن مجاني",
                type = CouponType.FREE_SHIPPING,
                value = 0.0,
                minimumOrderAmount = minimumAmount,
                validUntil = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L), // أسبوع
                termsAndConditions = "حد أدنى للطلب ${minimumAmount.toInt()} دينار"
            )
        }
    }
}

/**
 * أنواع الكوبونات
 */
enum class CouponType {
    PERCENTAGE,     // نسبة مئوية
    FIXED_AMOUNT,   // مبلغ ثابت
    FREE_SHIPPING   // شحن مجاني
}

/**
 * حالات الكوبون
 */
enum class CouponStatus {
    ACTIVE,         // نشط
    INACTIVE,       // غير نشط
    EXPIRED,        // منتهي الصلاحية
    USED_UP,        // استُنفد
    NOT_STARTED     // لم يبدأ بعد
}

/**
 * نتيجة تطبيق الكوبون
 */
@Parcelize
data class CouponApplication(
    val isValid: Boolean = false,
    val discountAmount: Double = 0.0,
    val freeShipping: Boolean = false,
    val errorMessage: String = "",
    val coupon: Coupon? = null
) : Parcelable {
    
    fun getSuccessMessage(): String {
        return when {
            freeShipping -> "تم تطبيق كوبون الشحن المجاني!"
            discountAmount > 0 -> "تم تطبيق خصم ${discountAmount.toInt()} دينار!"
            else -> "تم تطبيق الكوبون بنجاح!"
        }
    }
}
