<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:orientation="vertical"
    tools:context=".EditOrderActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_background"
        android:elevation="8dp"
        app:title="تعديل الطلب"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white"
        app:menu="@menu/edit_order_menu" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="16dp"
        android:visibility="gone" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Edit Warning -->
            <TextView
                android:id="@+id/tvEditWarning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:background="@drawable/warning_background"
                android:padding="12dp"
                android:textColor="@color/warning_yellow"
                android:textSize="14sp"
                android:visibility="gone"
                tools:text="⚠️ لا يمكن تعديل الطلبات المشحونة أو المسلمة"
                tools:visibility="visible" />

            <!-- Order Header Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات الطلب"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp"
                        android:drawableStart="@drawable/ic_receipt"
                        android:drawablePadding="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvOrderNumber"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textColor="@color/primary_blue"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            tools:text="طلب #123" />

                        <TextView
                            android:id="@+id/tvOrderStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/success_green"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="معلق" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvOrderDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="15/01/2025 14:30" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Customer Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات العميل"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_person"
                        android:drawablePadding="8dp" />

                    <!-- Customer Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="اسم العميل"
                        app:startIconDrawable="@drawable/ic_person">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCustomerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPersonName" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Customer Phone -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="رقم الهاتف"
                        app:startIconDrawable="@drawable/ic_phone">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCustomerPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="phone" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Customer Address -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:hint="العنوان"
                        app:startIconDrawable="@drawable/ic_location">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCustomerAddress"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textPostalAddress"
                            android:maxLines="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Order Notes -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="ملاحظات الطلب"
                        app:startIconDrawable="@drawable/ic_note">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etOrderNotes"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Items Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="منتجات الطلب"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:drawableStart="@drawable/ic_shopping_cart"
                            android:drawablePadding="8dp" />

                        <Button
                            android:id="@+id/btnAddProduct"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="إضافة منتج"
                            android:textSize="12sp"
                            android:drawableStart="@drawable/ic_add"
                            android:drawablePadding="4dp" />

                    </LinearLayout>

                    <!-- Order Items RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvOrderItems"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_edit_order" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Summary Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ملخص الطلب"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_calculator"
                        android:drawablePadding="8dp" />

                    <!-- Items Count -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عدد المنتجات:"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvItemsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="3 منتجات" />

                    </LinearLayout>

                    <!-- Total Quantity -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="إجمالي الكمية:"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvTotalQuantity"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="5 قطع" />

                    </LinearLayout>

                    <!-- Subtotal -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="المجموع الفرعي:"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="75000 د.ع" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="8dp"
                        android:background="@color/divider_color" />

                    <!-- Total Amount -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="المبلغ الإجمالي:"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvTotalAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primary_blue"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="75000 د.ع" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/white"
        android:elevation="8dp">

        <Button
            android:id="@+id/btnCancelEdit"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="إلغاء"
            android:textColor="@color/text_secondary" />

        <Button
            android:id="@+id/btnSaveChanges"
            style="@style/Widget.Material3.Button"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="💾 حفظ التغييرات"
            android:backgroundTint="@color/primary_blue" />

    </LinearLayout>

</LinearLayout>
