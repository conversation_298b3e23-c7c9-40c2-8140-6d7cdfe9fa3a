package com.kahrabaiat.amer.cache

import android.content.Context
import android.util.Log
import android.util.LruCache
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable

class CacheManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "CacheManager"
        private const val CACHE_DIR_NAME = "app_cache"
        private const val MAX_MEMORY_CACHE_SIZE = 10 * 1024 * 1024 // 10MB
        private const val MAX_DISK_CACHE_SIZE = 50 * 1024 * 1024L // 50MB
        private const val CACHE_EXPIRY_TIME = 24 * 60 * 60 * 1000L // 24 ساعة

        @Volatile
        private var INSTANCE: CacheManager? = null

        fun getInstance(context: Context): CacheManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CacheManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val memoryCache: LruCache<String, CacheEntry>
    private val cacheDir: File
    private val cacheMutex = Mutex()

    init {
        // إعداد ذاكرة التخزين المؤقت في الذاكرة
        val maxMemory = (Runtime.getRuntime().maxMemory() / 1024).toInt()
        val cacheSize = maxMemory / 8 // استخدام 1/8 من الذاكرة المتاحة

        memoryCache = object : LruCache<String, CacheEntry>(cacheSize.coerceAtMost(MAX_MEMORY_CACHE_SIZE / 1024)) {
            override fun sizeOf(key: String, value: CacheEntry): Int {
                return value.size / 1024 // الحجم بالكيلوبايت
            }

            override fun entryRemoved(evicted: Boolean, key: String, oldValue: CacheEntry, newValue: CacheEntry?) {
                if (evicted) {
                    Log.d(TAG, "Memory cache entry evicted: $key")
                }
            }
        }

        // إعداد مجلد التخزين المؤقت على القرص
        cacheDir = File(context.cacheDir, CACHE_DIR_NAME)
        if (!cacheDir.exists()) {
            cacheDir.mkdirs()
        }

        // تنظيف التخزين المؤقت المنتهي الصلاحية
        cleanupExpiredCache()

        Log.d(TAG, "Cache manager initialized - Memory: ${cacheSize}KB, Disk: ${cacheDir.absolutePath}")
    }

    /**
     * حفظ البيانات في التخزين المؤقت
     */
    suspend fun put(key: String, data: Serializable, cacheType: CacheType = CacheType.MEMORY_AND_DISK) {
        cacheMutex.withLock {
            try {
                val entry = CacheEntry(
                    data = data,
                    timestamp = System.currentTimeMillis(),
                    size = estimateSize(data)
                )

                when (cacheType) {
                    CacheType.MEMORY_ONLY -> {
                        memoryCache.put(key, entry)
                    }
                    CacheType.DISK_ONLY -> {
                        saveToDisk(key, entry)
                    }
                    CacheType.MEMORY_AND_DISK -> {
                        memoryCache.put(key, entry)
                        saveToDisk(key, entry)
                    }
                }

                Log.d(TAG, "Cached data: $key (${entry.size} bytes)")

            } catch (e: Exception) {
                Log.e(TAG, "Error caching data: $key", e)
            }
        }
    }

    /**
     * استرجاع البيانات من التخزين المؤقت
     */
    suspend fun get(key: String): Serializable? {
        return cacheMutex.withLock {
            try {
                // البحث في ذاكرة التخزين المؤقت أولاً
                var entry = memoryCache.get(key)

                // إذا لم توجد، البحث في القرص
                if (entry == null) {
                    entry = loadFromDisk(key)

                    // إضافة إلى ذاكرة التخزين المؤقت إذا وجدت
                    if (entry != null) {
                        memoryCache.put(key, entry)
                    }
                }

                // التحقق من انتهاء الصلاحية
                if (entry != null && !isExpired(entry)) {
                    Log.d(TAG, "Cache hit: $key")
                    entry.data
                } else {
                    if (entry != null) {
                        Log.d(TAG, "Cache expired: $key")
                        remove(key)
                    } else {
                        Log.d(TAG, "Cache miss: $key")
                    }
                    null
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error retrieving cached data: $key", e)
                null
            }
        }
    }

    /**
     * إزالة البيانات من التخزين المؤقت
     */
    suspend fun remove(key: String) {
        cacheMutex.withLock {
            try {
                memoryCache.remove(key)

                val diskFile = File(cacheDir, key)
                if (diskFile.exists()) {
                    diskFile.delete()
                }

                Log.d(TAG, "Removed from cache: $key")

            } catch (e: Exception) {
                Log.e(TAG, "Error removing cached data: $key", e)
            }
        }
    }

    /**
     * التحقق من وجود البيانات في التخزين المؤقت
     */
    suspend fun contains(key: String): Boolean {
        return get(key) != null
    }

    /**
     * مسح جميع البيانات المخزنة مؤقتاً
     */
    suspend fun clear() {
        cacheMutex.withLock {
            try {
                memoryCache.evictAll()

                cacheDir.listFiles()?.forEach { file ->
                    file.delete()
                }

                Log.d(TAG, "Cache cleared")

            } catch (e: Exception) {
                Log.e(TAG, "Error clearing cache", e)
            }
        }
    }

    /**
     * حفظ البيانات على القرص
     */
    private fun saveToDisk(key: String, entry: CacheEntry) {
        try {
            val file = File(cacheDir, key)
            ObjectOutputStream(FileOutputStream(file)).use { oos ->
                oos.writeObject(entry)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error saving to disk: $key", e)
        }
    }

    /**
     * تحميل البيانات من القرص
     */
    private fun loadFromDisk(key: String): CacheEntry? {
        return try {
            val file = File(cacheDir, key)
            if (file.exists()) {
                ObjectInputStream(FileInputStream(file)).use { ois ->
                    ois.readObject() as CacheEntry
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading from disk: $key", e)
            null
        }
    }

    /**
     * التحقق من انتهاء صلاحية البيانات
     */
    private fun isExpired(entry: CacheEntry): Boolean {
        val currentTime = System.currentTimeMillis()
        return (currentTime - entry.timestamp) > CACHE_EXPIRY_TIME
    }

    /**
     * تقدير حجم البيانات
     */
    private fun estimateSize(data: Serializable): Int {
        return try {
            val baos = java.io.ByteArrayOutputStream()
            ObjectOutputStream(baos).use { oos ->
                oos.writeObject(data)
            }
            baos.size()
        } catch (e: Exception) {
            1024 // حجم افتراضي
        }
    }

    /**
     * تنظيف التخزين المؤقت المنتهي الصلاحية
     */
    private fun cleanupExpiredCache() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                val currentTime = System.currentTimeMillis()

                cacheDir.listFiles()?.forEach { file ->
                    try {
                        val entry = ObjectInputStream(FileInputStream(file)).use { ois ->
                            ois.readObject() as CacheEntry
                        }

                        if ((currentTime - entry.timestamp) > CACHE_EXPIRY_TIME) {
                            file.delete()
                            Log.d(TAG, "Expired cache file deleted: ${file.name}")
                        }
                    } catch (e: Exception) {
                        // ملف تالف، احذفه
                        file.delete()
                        Log.w(TAG, "Corrupted cache file deleted: ${file.name}")
                    }
                }

                // تنظيف ذاكرة التخزين المؤقت إذا تجاوزت الحد الأقصى
                cleanupOversizedCache()

            } catch (e: Exception) {
                Log.e(TAG, "Error during cache cleanup", e)
            }
        }
    }

    /**
     * تنظيف التخزين المؤقت الكبير الحجم
     */
    private fun cleanupOversizedCache() {
        try {
            val totalSize = cacheDir.listFiles()?.sumOf { it.length() } ?: 0

            if (totalSize > MAX_DISK_CACHE_SIZE) {
                // ترتيب الملفات حسب تاريخ آخر تعديل
                val files = cacheDir.listFiles()?.sortedBy { it.lastModified() } ?: return

                var currentSize = totalSize
                for (file in files) {
                    if (currentSize <= MAX_DISK_CACHE_SIZE * 0.8) break // اترك 20% مساحة

                    currentSize -= file.length()
                    file.delete()
                    Log.d(TAG, "Cache file deleted due to size limit: ${file.name}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error during oversized cache cleanup", e)
        }
    }

    /**
     * الحصول على إحصائيات التخزين المؤقت
     */
    fun getCacheStats(): CacheStats {
        return try {
            val memorySize = memoryCache.size()
            val memoryHitCount = memoryCache.hitCount()
            val memoryMissCount = memoryCache.missCount()
            val memoryEvictionCount = memoryCache.evictionCount()

            val diskFiles = cacheDir.listFiles()?.size ?: 0
            val diskSize = cacheDir.listFiles()?.sumOf { it.length() } ?: 0

            CacheStats(
                memoryEntries = memorySize,
                memoryHitCount = memoryHitCount.toInt(),
                memoryMissCount = memoryMissCount.toInt(),
                memoryEvictionCount = memoryEvictionCount.toInt(),
                diskEntries = diskFiles,
                diskSizeBytes = diskSize,
                hitRatio = if (memoryHitCount + memoryMissCount > 0) {
                    (memoryHitCount.toDouble() / (memoryHitCount + memoryMissCount) * 100)
                } else 0.0
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting cache stats", e)
            CacheStats(0, 0, 0, 0, 0, 0, 0.0)
        }
    }

    /**
     * تحسين التخزين المؤقت
     */
    fun optimizeCache() {
        CoroutineScope(Dispatchers.IO).launch {
            try {
                Log.d(TAG, "Starting cache optimization")

                // تنظيف البيانات المنتهية الصلاحية
                cleanupExpiredCache()

                // ضغط ذاكرة التخزين المؤقت إذا كانت ممتلئة
                val stats = getCacheStats()
                if (stats.hitRatio < 50.0) { // إذا كانت نسبة النجاح منخفضة
                    memoryCache.trimToSize(memoryCache.maxSize() / 2)
                    Log.d(TAG, "Memory cache trimmed due to low hit ratio")
                }

                Log.d(TAG, "Cache optimization completed")

            } catch (e: Exception) {
                Log.e(TAG, "Error during cache optimization", e)
            }
        }
    }

    /**
     * تصدير تقرير التخزين المؤقت
     */
    fun exportCacheReport(): String {
        val stats = getCacheStats()

        return buildString {
            appendLine("=== تقرير التخزين المؤقت ===")
            appendLine("التاريخ: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date())}")
            appendLine()
            appendLine("ذاكرة التخزين المؤقت:")
            appendLine("- العناصر: ${stats.memoryEntries}")
            appendLine("- النجاحات: ${stats.memoryHitCount}")
            appendLine("- الإخفاقات: ${stats.memoryMissCount}")
            appendLine("- الطرد: ${stats.memoryEvictionCount}")
            appendLine("- نسبة النجاح: ${"%.1f".format(stats.hitRatio)}%")
            appendLine()
            appendLine("تخزين القرص:")
            appendLine("- العناصر: ${stats.diskEntries}")
            appendLine("- الحجم: ${formatBytes(stats.diskSizeBytes)}")
            appendLine("- الحد الأقصى: ${formatBytes(MAX_DISK_CACHE_SIZE)}")
            appendLine("- الاستخدام: ${"%.1f".format(stats.diskSizeBytes.toDouble() / MAX_DISK_CACHE_SIZE * 100)}%")
        }
    }

    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 -> "${"%.1f".format(bytes / (1024.0 * 1024.0))} MB"
            bytes >= 1024 -> "${"%.1f".format(bytes / 1024.0)} KB"
            else -> "$bytes bytes"
        }
    }

    // Data classes and enums
    data class CacheEntry(
        val data: Serializable,
        val timestamp: Long,
        val size: Int
    ) : Serializable

    data class CacheStats(
        val memoryEntries: Int,
        val memoryHitCount: Int,
        val memoryMissCount: Int,
        val memoryEvictionCount: Int,
        val diskEntries: Int,
        val diskSizeBytes: Long,
        val hitRatio: Double
    )

    enum class CacheType {
        MEMORY_ONLY,
        DISK_ONLY,
        MEMORY_AND_DISK
    }
}
