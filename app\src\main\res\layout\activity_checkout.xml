<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".CheckoutActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="@color/white"
            app:title="@string/checkout"
            app:titleTextColor="@color/white" />

        <!-- Customer Information Form -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="@string/customer_info"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- Customer Name -->
                <com.google.android.material.textfield.TextInputLayout
                    style="@style/InputField"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/customer_name">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCustomerName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPersonName"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Customer Phone -->
                <com.google.android.material.textfield.TextInputLayout
                    style="@style/InputField"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/customer_phone">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCustomerPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- Customer Address -->
                <com.google.android.material.textfield.TextInputLayout
                    style="@style/InputField"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/customer_address">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCustomerAddress"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLines="3"
                        android:minLines="2" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Order Summary -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="ملخص الطلب"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <!-- Order Items RecyclerView -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvOrderItems"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:nestedScrollingEnabled="false"
                    tools:listitem="@layout/item_order_summary" />

                <!-- Divider -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:layout_marginVertical="16dp"
                    android:background="@color/light_gray" />

                <!-- Total Amount -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="المجموع الكلي:"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTotalAmount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/primary_blue"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="2500 دينار عراقي" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Place Order Button -->
        <Button
            android:id="@+id/btnPlaceOrder"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="@string/place_order" />

    </LinearLayout>

</ScrollView>
