<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background_main"
    tools:context=".SecuritySettingsActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_header_background"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Security Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="حالة الأمان"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_security"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:id="@+id/tvAccountStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الحساب نشط"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvCurrentAttempts"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="المحاولات الحالية: 0/5"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Security Statistics Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إحصائيات الأمان"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_analytics"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:id="@+id/tvTotalEvents"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إجمالي الأحداث: 0"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvRecentEvents"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="أحداث آخر 24 ساعة: 0"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvFailedLogins"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="محاولات دخول فاشلة: 0"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvSuspiciousActivities"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="أنشطة مشبوهة: 0"
                        android:textSize="14sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Security Actions Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إجراءات الأمان"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_settings"
                        android:drawablePadding="8dp" />

                    <Button
                        android:id="@+id/btnCheckIntegrity"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="فحص سلامة التطبيق"
                        android:background="@drawable/button_primary_enhanced"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:padding="16dp"
                        android:drawableStart="@drawable/ic_security"
                        android:drawablePadding="8dp" />

                    <Button
                        android:id="@+id/btnChangePassword"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="تغيير كلمة المرور"
                        android:backgroundTint="@color/warning_yellow"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="16dp"
                        android:drawableStart="@drawable/ic_lock"
                        android:drawableTint="@color/white"
                        android:drawablePadding="8dp"
                        android:elevation="4dp" />

                    <Button
                        android:id="@+id/btnUnblockAccount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="إلغاء حظر الحساب"
                        android:background="@drawable/button_warning_enhanced"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:padding="16dp"
                        android:drawableStart="@drawable/ic_unlock"
                        android:drawablePadding="8dp"
                        android:enabled="false" />

                    <Button
                        android:id="@+id/btnClearSecurityLog"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="مسح سجل الأمان"
                        android:background="@drawable/gradient_error_enhanced"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:padding="16dp"
                        android:drawableStart="@drawable/ic_delete"
                        android:drawablePadding="8dp" />

                    <Button
                        android:id="@+id/btnExportReport"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تصدير تقرير الأمان"
                        android:textColor="@color/primary_blue"
                        android:textSize="16sp"
                        android:padding="16dp"
                        android:drawableStart="@drawable/ic_export"
                        android:drawablePadding="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Password Management Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إدارة كلمة المرور"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_lock"
                        android:drawableTint="@color/warning_yellow"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="قم بتغيير كلمة المرور بانتظام لضمان أمان حسابك"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:layout_marginBottom="16dp" />

                    <Button
                        android:id="@+id/btnChangePasswordMain"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🔐 تغيير كلمة المرور"
                        android:backgroundTint="@color/primary_blue"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:padding="20dp"
                        android:elevation="6dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="آخر تغيير: لم يتم تغييرها من قبل"
                        android:textSize="12sp"
                        android:textColor="@color/text_tertiary"
                        android:gravity="center" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Security Events Log Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="سجل الأحداث الأمنية"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:drawableStart="@drawable/ic_history"
                            android:drawablePadding="8dp" />

                        <Button
                            android:id="@+id/btnRefreshData"
                            style="@style/Widget.Material3.Button.IconButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:src="@drawable/ic_refresh"
                            android:contentDescription="تحديث البيانات" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvSecurityEvents"
                        android:layout_width="match_parent"
                        android:layout_height="300dp"
                        android:scrollbars="vertical" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
