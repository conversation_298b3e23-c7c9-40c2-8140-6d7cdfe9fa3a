package com.kahrabaiat.amer.ui

import android.view.View
import android.view.ViewGroup
import android.widget.ProgressBar
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.R

class LoadingStateManager(
    private val recyclerView: RecyclerView,
    private val progressBar: ProgressBar,
    private val emptyView: View? = null
) {
    
    private var isLoading = false
    private var isEmpty = false
    
    /**
     * عرض حالة التحميل
     */
    fun showLoading() {
        isLoading = true
        isEmpty = false
        updateViews()
    }
    
    /**
     * عرض المحتوى
     */
    fun showContent() {
        isLoading = false
        isEmpty = false
        updateViews()
    }
    
    /**
     * عرض حالة فارغة
     */
    fun showEmpty() {
        isLoading = false
        isEmpty = true
        updateViews()
    }
    
    /**
     * إخفاء التحميل
     */
    fun hideLoading() {
        isLoading = false
        updateViews()
    }
    
    private fun updateViews() {
        when {
            isLoading -> {
                progressBar.visibility = View.VISIBLE
                recyclerView.visibility = View.GONE
                emptyView?.visibility = View.GONE
            }
            isEmpty -> {
                progressBar.visibility = View.GONE
                recyclerView.visibility = View.GONE
                emptyView?.visibility = View.VISIBLE
            }
            else -> {
                progressBar.visibility = View.GONE
                recyclerView.visibility = View.VISIBLE
                emptyView?.visibility = View.GONE
            }
        }
    }
    
    /**
     * تحديث حالة القائمة بناءً على البيانات
     */
    fun updateState(itemCount: Int) {
        when {
            isLoading -> showLoading()
            itemCount == 0 -> showEmpty()
            else -> showContent()
        }
    }
}
