package com.kahrabaiat.amer.adapters

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.databinding.ItemReviewBinding
import com.kahrabaiat.amer.models.Review

class ReviewAdapter(
    private val onHelpfulClick: (Review) -> Unit = {},
    private val onReportClick: (Review) -> Unit = {},
    private val showAdminActions: Boolean = false,
    private val onApproveClick: (Review) -> Unit = {},
    private val onDeleteClick: (Review) -> Unit = {}
) : ListAdapter<Review, ReviewAdapter.ReviewViewHolder>(ReviewDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ReviewViewHolder {
        val binding = ItemReviewBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ReviewViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ReviewViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ReviewViewHolder(
        private val binding: ItemReviewBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(review: Review) {
            binding.apply {
                // معلومات العميل
                tvCustomerName.text = review.customerName
                tvReviewDate.text = review.getFormattedDate()

                // التقييم
                ratingBar.rating = review.rating
                tvRatingText.text = review.getRatingText()
                tvRatingValue.text = review.rating.toString()

                // تلوين التقييم
                try {
                    val color = Color.parseColor(review.getRatingColor())
                    tvRatingText.setTextColor(color)
                    tvRatingValue.setTextColor(color)
                } catch (e: Exception) {
                    // استخدام اللون الافتراضي في حالة الخطأ
                }

                // نص المراجعة
                tvComment.text = review.comment

                // شارات إضافية
                chipVerifiedPurchase.visibility = if (review.isVerifiedPurchase) {
                    View.VISIBLE
                } else {
                    View.GONE
                }

                chipRecent.visibility = if (review.isRecent()) {
                    View.VISIBLE
                } else {
                    View.GONE
                }

                chipHelpful.visibility = if (review.isHelpful()) {
                    View.VISIBLE
                } else {
                    View.GONE
                }

                // عدد الإعجابات
                if (review.helpfulCount > 0) {
                    tvHelpfulCount.text = "${review.helpfulCount} شخص وجد هذه المراجعة مفيدة"
                    tvHelpfulCount.visibility = View.VISIBLE
                } else {
                    tvHelpfulCount.visibility = View.GONE
                }

                // أزرار التفاعل
                btnHelpful.setOnClickListener {
                    onHelpfulClick(review)
                }

                btnReport.setOnClickListener {
                    onReportClick(review)
                }

                // أزرار الإدارة (للمدير فقط)
                if (showAdminActions) {
                    layoutAdminActions.visibility = View.VISIBLE
                    
                    // حالة الموافقة
                    if (review.isApproved) {
                        btnApprove.text = "مُوافق عليها"
                        btnApprove.isEnabled = false
                        btnApprove.alpha = 0.6f
                    } else {
                        btnApprove.text = "موافقة"
                        btnApprove.isEnabled = true
                        btnApprove.alpha = 1.0f
                    }

                    btnApprove.setOnClickListener {
                        onApproveClick(review)
                    }

                    btnDelete.setOnClickListener {
                        onDeleteClick(review)
                    }

                    // عرض معلومات إضافية للمدير
                    if (review.reportCount > 0) {
                        tvReportCount.text = "تم الإبلاغ عنها ${review.reportCount} مرة"
                        tvReportCount.visibility = View.VISIBLE
                        tvReportCount.setTextColor(Color.parseColor("#F44336"))
                    } else {
                        tvReportCount.visibility = View.GONE
                    }

                    tvCustomerPhone.text = "الهاتف: ${review.customerPhone}"
                    tvCustomerPhone.visibility = View.VISIBLE
                } else {
                    layoutAdminActions.visibility = View.GONE
                    tvReportCount.visibility = View.GONE
                    tvCustomerPhone.visibility = View.GONE
                }

                // تأثير بصري للمراجعات غير المُوافق عليها (للمدير فقط)
                if (showAdminActions && !review.isApproved) {
                    cardReview.alpha = 0.7f
                    cardReview.setCardBackgroundColor(Color.parseColor("#FFEBEE"))
                } else {
                    cardReview.alpha = 1.0f
                    cardReview.setCardBackgroundColor(Color.parseColor("#FFFFFF"))
                }

                // انيميشن ظهور المراجعة
                com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(cardReview, adapterPosition * 100L)
            }
        }
    }

    class ReviewDiffCallback : DiffUtil.ItemCallback<Review>() {
        override fun areItemsTheSame(oldItem: Review, newItem: Review): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Review, newItem: Review): Boolean {
            return oldItem == newItem
        }
    }
}

/**
 * محول مبسط لعرض المراجعات في تفاصيل المنتج
 */
class SimpleReviewAdapter : ListAdapter<Review, SimpleReviewAdapter.SimpleReviewViewHolder>(ReviewDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SimpleReviewViewHolder {
        val binding = ItemReviewBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SimpleReviewViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SimpleReviewViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SimpleReviewViewHolder(
        private val binding: ItemReviewBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(review: Review) {
            binding.apply {
                // إخفاء العناصر غير المطلوبة في العرض المبسط
                layoutAdminActions.visibility = View.GONE
                btnHelpful.visibility = View.GONE
                btnReport.visibility = View.GONE
                tvReportCount.visibility = View.GONE
                tvCustomerPhone.visibility = View.GONE
                tvHelpfulCount.visibility = View.GONE

                // عرض المعلومات الأساسية فقط
                tvCustomerName.text = review.customerName
                tvReviewDate.text = review.getFormattedDate()
                ratingBar.rating = review.rating
                tvRatingText.text = review.getRatingText()
                tvRatingValue.text = review.rating.toString()
                
                // عرض ملخص المراجعة بدلاً من النص الكامل
                tvComment.text = review.getSummary()

                // شارة الشراء المُتحقق منه فقط
                chipVerifiedPurchase.visibility = if (review.isVerifiedPurchase) {
                    View.VISIBLE
                } else {
                    View.GONE
                }

                chipRecent.visibility = View.GONE
                chipHelpful.visibility = View.GONE

                // تلوين التقييم
                try {
                    val color = Color.parseColor(review.getRatingColor())
                    tvRatingText.setTextColor(color)
                    tvRatingValue.setTextColor(color)
                } catch (e: Exception) {
                    // استخدام اللون الافتراضي
                }
            }
        }
    }

    class ReviewDiffCallback : DiffUtil.ItemCallback<Review>() {
        override fun areItemsTheSame(oldItem: Review, newItem: Review): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Review, newItem: Review): Boolean {
            return oldItem == newItem
        }
    }
}
