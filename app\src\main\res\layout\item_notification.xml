<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardNotification"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:strokeWidth="2dp"
    app:strokeColor="@color/primary_blue">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header: Icon, Title, and Time -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <!-- Notification Icon -->
            <TextView
                android:id="@+id/tvNotificationIcon"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:text="📢"
                android:textSize="20sp"
                android:gravity="center"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/primary_light"
                android:layout_marginEnd="12dp" />

            <!-- Title and Time -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvNotificationTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="عنوان الإشعار"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tvNotificationTime"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="منذ 5 دقائق"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    android:fontFamily="@font/cairo_regular"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <!-- Unread Indicator -->
            <View
                android:id="@+id/indicatorUnread"
                android:layout_width="8dp"
                android:layout_height="8dp"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/primary_blue"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- Notification Message -->
        <TextView
            android:id="@+id/tvNotificationMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="نص الإشعار يظهر هنا مع التفاصيل الكاملة للإشعار"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:fontFamily="@font/cairo_regular"
            android:lineSpacingExtra="2dp"
            android:layout_marginBottom="12dp"
            android:maxLines="3"
            android:ellipsize="end" />

        <!-- Notification Image (Optional) -->
        <ImageView
            android:id="@+id/ivNotificationImage"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:background="@drawable/rounded_background"
            android:layout_marginBottom="12dp"
            android:visibility="gone"
            tools:src="@drawable/ic_product_placeholder" />

        <!-- Badges -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chipNotificationType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="منتج جديد"
                android:textSize="10sp"
                android:textColor="@color/primary_blue"
                app:chipBackgroundColor="@color/primary_light"
                app:chipStrokeColor="@color/primary_blue"
                app:chipStrokeWidth="1dp"
                android:layout_marginEnd="4dp" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipNew"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🆕 جديد"
                android:textSize="10sp"
                android:textColor="@color/success_green"
                app:chipBackgroundColor="@color/success_light"
                app:chipStrokeColor="@color/success_green"
                app:chipStrokeWidth="1dp"
                android:layout_marginEnd="4dp"
                android:visibility="gone" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipExpired"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="⏰ منتهي"
                android:textSize="10sp"
                android:textColor="@color/error_red"
                app:chipBackgroundColor="@color/error_background"
                app:chipStrokeColor="@color/error_red"
                app:chipStrokeWidth="1dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Admin Actions (Admin Only) -->
        <LinearLayout
            android:id="@+id/layoutAdminActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:padding="8dp"
            android:background="@drawable/rounded_background"
            android:visibility="gone">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnMarkAsRead"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="✓ تحديد كمقروء"
                android:textColor="@color/success_green"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginEnd="8dp"
                app:cornerRadius="18dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="🗑️ حذف"
                android:textColor="@color/error_red"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                app:cornerRadius="18dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
