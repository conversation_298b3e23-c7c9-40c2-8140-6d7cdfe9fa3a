package com.kahrabaiat.amer

import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.kahrabaiat.amer.databinding.ActivityAddReviewBinding
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.models.Review
import com.kahrabaiat.amer.utils.ReviewManager
import kotlinx.coroutines.launch

class AddReviewActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAddReviewBinding
    private lateinit var reviewManager: ReviewManager
    private var product: Product? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAddReviewBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        getProductFromIntent()
        setupToolbar()
        setupClickListeners()
        setupRatingBar()
    }

    private fun initializeComponents() {
        reviewManager = ReviewManager.getInstance(this)
    }

    private fun getProductFromIntent() {
        product = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("product", Product::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra("product")
        }

        product?.let { product ->
            binding.tvProductName.text = product.name
            binding.tvProductPrice.text = product.getFormattedPrice()
        } ?: run {
            showToast("خطأ في تحميل بيانات المنتج")
            finish()
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "إضافة مراجعة"
        }
    }

    private fun setupClickListeners() {
        binding.btnSubmitReview.setOnClickListener {
            submitReview()
        }

        binding.btnCancel.setOnClickListener {
            finish()
        }
    }

    private fun setupRatingBar() {
        binding.ratingBar.setOnRatingBarChangeListener { _, rating, _ ->
            updateRatingText(rating)
        }
        
        // تعيين تقييم افتراضي
        binding.ratingBar.rating = 5f
        updateRatingText(5f)
    }

    private fun updateRatingText(rating: Float) {
        val ratingText = when {
            rating >= 4.5f -> "ممتاز"
            rating >= 3.5f -> "جيد جداً"
            rating >= 2.5f -> "جيد"
            rating >= 1.5f -> "مقبول"
            else -> "ضعيف"
        }
        
        binding.tvRatingText.text = "$ratingText ($rating)"
        
        // تغيير لون النص حسب التقييم
        val colorRes = when {
            rating >= 4.0f -> R.color.success_green
            rating >= 3.0f -> R.color.accent_orange
            rating >= 2.0f -> R.color.warning_yellow
            else -> R.color.error_red
        }
        
        binding.tvRatingText.setTextColor(getColor(colorRes))
    }

    private fun submitReview() {
        if (!validateInput()) {
            return
        }

        val product = this.product ?: return

        val customerName = binding.etCustomerName.text.toString().trim()
        val customerPhone = binding.etCustomerPhone.text.toString().trim()
        val rating = binding.ratingBar.rating
        val comment = binding.etComment.text.toString().trim()

        // تعطيل الزر أثناء الإرسال
        binding.btnSubmitReview.isEnabled = false
        binding.btnSubmitReview.text = "جاري الإرسال..."

        lifecycleScope.launch {
            try {
                // التحقق من وجود مراجعة سابقة
                val hasReviewed = reviewManager.hasCustomerReviewedProduct(product.id, customerPhone)
                
                if (hasReviewed) {
                    showToast("لقد قمت بمراجعة هذا المنتج من قبل")
                    resetSubmitButton()
                    return@launch
                }

                // إنشاء المراجعة
                val review = Review.create(
                    productId = product.id,
                    customerName = customerName,
                    customerPhone = customerPhone,
                    rating = rating,
                    comment = comment,
                    isVerifiedPurchase = false // يمكن تحسينه لاحقاً للتحقق من الشراء الفعلي
                )

                // إرسال المراجعة
                val success = reviewManager.addReview(review)

                if (success) {
                    showToast("تم إرسال مراجعتك بنجاح! شكراً لك.")
                    setResult(RESULT_OK)
                    finish()
                } else {
                    showToast("فشل في إرسال المراجعة. حاول مرة أخرى.")
                    resetSubmitButton()
                }

            } catch (e: Exception) {
                showToast("حدث خطأ: ${e.message}")
                resetSubmitButton()
            }
        }
    }

    private fun validateInput(): Boolean {
        var isValid = true

        // التحقق من الاسم
        val customerName = binding.etCustomerName.text.toString().trim()
        if (customerName.isEmpty()) {
            binding.etCustomerName.error = "الاسم مطلوب"
            isValid = false
        } else if (customerName.length < 2) {
            binding.etCustomerName.error = "الاسم قصير جداً"
            isValid = false
        }

        // التحقق من رقم الهاتف
        val customerPhone = binding.etCustomerPhone.text.toString().trim()
        if (customerPhone.isEmpty()) {
            binding.etCustomerPhone.error = "رقم الهاتف مطلوب"
            isValid = false
        } else if (!isValidPhoneNumber(customerPhone)) {
            binding.etCustomerPhone.error = "رقم الهاتف غير صحيح"
            isValid = false
        }

        // التحقق من التقييم
        if (binding.ratingBar.rating == 0f) {
            showToast("يرجى اختيار تقييم")
            isValid = false
        }

        // التحقق من التعليق
        val comment = binding.etComment.text.toString().trim()
        if (comment.isEmpty()) {
            binding.etComment.error = "التعليق مطلوب"
            isValid = false
        } else if (comment.length < 10) {
            binding.etComment.error = "التعليق قصير جداً (10 أحرف على الأقل)"
            isValid = false
        } else if (comment.length > 500) {
            binding.etComment.error = "التعليق طويل جداً (500 حرف كحد أقصى)"
            isValid = false
        }

        return isValid
    }

    private fun isValidPhoneNumber(phone: String): Boolean {
        // التحقق من أرقام الهواتف العراقية
        val phonePattern = Regex("^(\\+964|0)?7[0-9]{9}$")
        return phonePattern.matches(phone)
    }

    private fun resetSubmitButton() {
        binding.btnSubmitReview.isEnabled = true
        binding.btnSubmitReview.text = "إرسال المراجعة"
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
