package com.kahrabaiat.amer.services

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
// import com.google.firebase.messaging.FirebaseMessagingService
// import com.google.firebase.messaging.RemoteMessage
import com.kahrabaiat.amer.AdminActivity
import com.kahrabaiat.amer.MainActivity
import com.kahrabaiat.amer.R

// class MyFirebaseMessagingService : FirebaseMessagingService() {
class MyFirebaseMessagingService : Service() { // تم تعطيل Firebase مؤقتاً

    companion object {
        const val CHANNEL_ID = "fcm_notifications"
        const val NOTIFICATION_ID = 2001
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    /*
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)

        // Handle FCM messages here
        remoteMessage.notification?.let { notification ->
            showNotification(
                notification.title ?: "إشعار جديد",
                notification.body ?: ""
            )
        }

        // Handle data payload
        if (remoteMessage.data.isNotEmpty()) {
            handleDataMessage(remoteMessage.data)
        }
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        // Send token to your server
        sendTokenToServer(token)
    }
    */

    private fun handleDataMessage(data: Map<String, String>) {
        val type = data["type"]
        when (type) {
            "new_order" -> {
                val customerName = data["customer_name"] ?: ""
                val orderNumber = data["order_number"] ?: ""
                val amount = data["amount"] ?: ""

                showOrderNotification(customerName, orderNumber, amount)
            }
        }
    }

    private fun showNotification(title: String, body: String) {
        createNotificationChannel()

        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_shopping_cart)
            .setContentTitle(title)
            .setContentText(body)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    private fun showOrderNotification(customerName: String, orderNumber: String, amount: String) {
        createNotificationChannel()

        val intent = Intent(this, AdminActivity::class.java).apply {
            putExtra("show_orders", true)
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_shopping_cart)
            .setContentTitle("طلب جديد!")
            .setContentText("طلب من $customerName - $amount")
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("طلب جديد من $customerName\nرقم الطلب: $orderNumber\nالمبلغ: $amount")
            )
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()

        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID + orderNumber.hashCode(), notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "إشعارات التطبيق"
            val descriptionText = "إشعارات الطلبات والتحديثات"
            val importance = NotificationManager.IMPORTANCE_HIGH
            val channel = NotificationChannel(CHANNEL_ID, name, importance).apply {
                description = descriptionText
            }

            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun sendTokenToServer(token: String) {
        // Send the token to your server for targeted notifications
        // This would typically involve an API call to your backend
    }
}
