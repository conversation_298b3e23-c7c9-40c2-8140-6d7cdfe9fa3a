package com.kahrabaiat.amer

import android.app.DatePickerDialog
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.*
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.formatter.PercentFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import com.kahrabaiat.amer.databinding.ActivityCustomReportsBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.ExportHelper
import com.kahrabaiat.amer.utils.FirebaseHelper
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class CustomReportsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCustomReportsBinding
    private lateinit var firebaseHelper: FirebaseHelper
    private lateinit var exportHelper: ExportHelper

    private var startDate: Long = 0
    private var endDate: Long = 0
    private var filteredOrders = mutableListOf<Order>()
    private var allProducts = mutableListOf<Product>()

    private val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("ar"))
    private val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityCustomReportsBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupDefaultDateRange()
            setupClickListeners()
            loadData()
        } catch (e: Exception) {
            android.util.Log.e("CustomReportsActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة التقارير: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        firebaseHelper = FirebaseHelper()
        exportHelper = ExportHelper(this)
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "التقارير المخصصة"
            }
        } catch (e: Exception) {
            android.util.Log.e("CustomReportsActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupDefaultDateRange() {
        // Set default range to last 30 days
        val calendar = Calendar.getInstance()
        endDate = calendar.timeInMillis

        calendar.add(Calendar.DAY_OF_MONTH, -30)
        startDate = calendar.timeInMillis

        updateDateRangeDisplay()
    }

    private fun setupClickListeners() {
        // Date Range Selection
        binding.btnSelectStartDate.setOnClickListener {
            showDatePicker(true)
        }

        binding.btnSelectEndDate.setOnClickListener {
            showDatePicker(false)
        }

        // Quick Date Ranges
        binding.chipToday.setOnClickListener {
            setQuickDateRange(0)
        }

        binding.chipYesterday.setOnClickListener {
            setQuickDateRange(1)
        }

        binding.chipLast7Days.setOnClickListener {
            setQuickDateRange(7)
        }

        binding.chipLast30Days.setOnClickListener {
            setQuickDateRange(30)
        }

        binding.chipThisMonth.setOnClickListener {
            setCurrentMonth()
        }

        binding.chipLastMonth.setOnClickListener {
            setLastMonth()
        }

        // Generate Report
        binding.btnGenerateReport.setOnClickListener {
            generateReport()
        }

        // Export Options
        binding.btnExportPdf.setOnClickListener {
            exportReportToPdf()
        }

        binding.btnExportExcel.setOnClickListener {
            exportReportToExcel()
        }

        // Refresh
        binding.swipeRefresh.setOnRefreshListener {
            loadData()
        }
    }

    private fun showDatePicker(isStartDate: Boolean) {
        val calendar = Calendar.getInstance()
        val currentDate = if (isStartDate) startDate else endDate
        calendar.timeInMillis = currentDate

        val datePickerDialog = DatePickerDialog(
            this,
            { _, year, month, dayOfMonth ->
                val selectedCalendar = Calendar.getInstance()
                selectedCalendar.set(year, month, dayOfMonth)

                if (isStartDate) {
                    selectedCalendar.set(Calendar.HOUR_OF_DAY, 0)
                    selectedCalendar.set(Calendar.MINUTE, 0)
                    selectedCalendar.set(Calendar.SECOND, 0)
                    selectedCalendar.set(Calendar.MILLISECOND, 0)
                    startDate = selectedCalendar.timeInMillis
                } else {
                    selectedCalendar.set(Calendar.HOUR_OF_DAY, 23)
                    selectedCalendar.set(Calendar.MINUTE, 59)
                    selectedCalendar.set(Calendar.SECOND, 59)
                    selectedCalendar.set(Calendar.MILLISECOND, 999)
                    endDate = selectedCalendar.timeInMillis
                }

                updateDateRangeDisplay()
                generateReport()
            },
            calendar.get(Calendar.YEAR),
            calendar.get(Calendar.MONTH),
            calendar.get(Calendar.DAY_OF_MONTH)
        )

        datePickerDialog.show()
    }

    private fun setQuickDateRange(daysAgo: Int) {
        val calendar = Calendar.getInstance()

        if (daysAgo == 0) {
            // Today
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            startDate = calendar.timeInMillis

            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            endDate = calendar.timeInMillis
        } else {
            // End date is today
            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            endDate = calendar.timeInMillis

            // Start date is daysAgo days before
            calendar.add(Calendar.DAY_OF_MONTH, -daysAgo + 1)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            startDate = calendar.timeInMillis
        }

        updateDateRangeDisplay()
        generateReport()
    }

    private fun setCurrentMonth() {
        val calendar = Calendar.getInstance()

        // End of current month
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        endDate = calendar.timeInMillis

        // Start of current month
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        startDate = calendar.timeInMillis

        updateDateRangeDisplay()
        generateReport()
    }

    private fun setLastMonth() {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.MONTH, -1)

        // End of last month
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        endDate = calendar.timeInMillis

        // Start of last month
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        startDate = calendar.timeInMillis

        updateDateRangeDisplay()
        generateReport()
    }

    private fun updateDateRangeDisplay() {
        binding.tvStartDate.text = dateFormat.format(Date(startDate))
        binding.tvEndDate.text = dateFormat.format(Date(endDate))

        val daysDiff = ((endDate - startDate) / (1000 * 60 * 60 * 24)).toInt() + 1
        binding.tvDateRangeSummary.text = "الفترة المحددة: $daysDiff يوم"
    }

    private fun loadData() {
        lifecycleScope.launch {
            try {
                binding.swipeRefresh.isRefreshing = true
                binding.progressBar.visibility = View.VISIBLE

                val orders = firebaseHelper.getAllOrders()
                val products = firebaseHelper.getAllProducts()

                allProducts.clear()
                allProducts.addAll(products)

                // Filter orders by date range
                filteredOrders.clear()
                filteredOrders.addAll(orders.filter { order ->
                    order.orderDate in startDate..endDate
                })

                generateReport()

            } catch (e: Exception) {
                android.util.Log.e("CustomReportsActivity", "Error loading data", e)
                showToast("خطأ في تحميل البيانات: ${e.message}")
            } finally {
                binding.swipeRefresh.isRefreshing = false
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun generateReport() {
        try {
            calculateSummaryStatistics()
            setupDailyRevenueChart()
            setupOrderStatusChart()
            setupTopProductsChart()
            showReportSection()
        } catch (e: Exception) {
            android.util.Log.e("CustomReportsActivity", "Error generating report", e)
            showToast("خطأ في إنشاء التقرير: ${e.message}")
        }
    }

    private fun calculateSummaryStatistics() {
        val totalOrders = filteredOrders.size
        val completedOrders = filteredOrders.count { it.status == "delivered" }
        val pendingOrders = filteredOrders.count {
            it.status == "pending" || it.status == "confirmed" || it.status == "processing"
        }
        val cancelledOrders = filteredOrders.count { it.status == "cancelled" }

        val totalRevenue = filteredOrders.filter { it.status == "delivered" }.sumOf { it.total }
        val pendingRevenue = filteredOrders.filter {
            it.status != "cancelled" && it.status != "delivered"
        }.sumOf { it.total }

        val averageOrderValue = if (completedOrders > 0) totalRevenue / completedOrders else 0.0

        // Update UI
        binding.tvTotalOrders.text = numberFormat.format(totalOrders)
        binding.tvCompletedOrders.text = numberFormat.format(completedOrders)
        binding.tvPendingOrders.text = numberFormat.format(pendingOrders)
        binding.tvCancelledOrders.text = numberFormat.format(cancelledOrders)

        binding.tvTotalRevenue.text = "${numberFormat.format(totalRevenue.toInt())} د.ع"
        binding.tvPendingRevenue.text = "${numberFormat.format(pendingRevenue.toInt())} د.ع"
        binding.tvAverageOrderValue.text = "${numberFormat.format(averageOrderValue.toInt())} د.ع"

        // Calculate completion rate
        val completionRate = if (totalOrders > 0) (completedOrders.toDouble() / totalOrders * 100) else 0.0
        binding.tvCompletionRate.text = "${String.format("%.1f", completionRate)}%"
    }

    private fun setupDailyRevenueChart() {
        val lineChart = binding.lineChartDailyRevenue

        // Group orders by day
        val dailyRevenue = mutableMapOf<String, Double>()
        val calendar = Calendar.getInstance()

        // Initialize all days in range with 0
        calendar.timeInMillis = startDate
        while (calendar.timeInMillis <= endDate) {
            val dayKey = SimpleDateFormat("dd/MM", Locale("ar")).format(calendar.time)
            dailyRevenue[dayKey] = 0.0
            calendar.add(Calendar.DAY_OF_MONTH, 1)
        }

        // Calculate actual revenue per day
        filteredOrders.filter { it.status == "delivered" }.forEach { order ->
            calendar.timeInMillis = order.orderDate
            val dayKey = SimpleDateFormat("dd/MM", Locale("ar")).format(calendar.time)
            dailyRevenue[dayKey] = dailyRevenue.getOrDefault(dayKey, 0.0) + order.total
        }

        val entries = mutableListOf<Entry>()
        val labels = mutableListOf<String>()

        var index = 0f
        dailyRevenue.toSortedMap().forEach { (day, revenue) ->
            entries.add(Entry(index, revenue.toFloat()))
            labels.add(day)
            index++
        }

        if (entries.isEmpty()) {
            lineChart.visibility = View.GONE
            binding.tvNoDailyData.visibility = View.VISIBLE
            return
        } else {
            lineChart.visibility = View.VISIBLE
            binding.tvNoDailyData.visibility = View.GONE
        }

        val dataSet = LineDataSet(entries, "الإيرادات اليومية")
        dataSet.apply {
            color = Color.parseColor("#2196F3")
            setCircleColor(Color.parseColor("#2196F3"))
            lineWidth = 3f
            circleRadius = 4f
            setDrawCircleHole(false)
            valueTextSize = 10f
            setDrawFilled(true)
            fillColor = Color.parseColor("#E3F2FD")
        }

        val data = LineData(dataSet)

        lineChart.apply {
            this.data = data
            description.isEnabled = false

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = IndexAxisValueFormatter(labels)
                granularity = 1f
                setDrawGridLines(false)
                labelRotationAngle = -45f
            }

            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }

            axisRight.isEnabled = false
            legend.isEnabled = true

            animateX(1000)
            invalidate()
        }
    }

    private fun setupOrderStatusChart() {
        val pieChart = binding.pieChartOrderStatus

        val statusCounts = mapOf(
            "مكتمل" to filteredOrders.count { it.status == "delivered" },
            "معلق" to filteredOrders.count { it.status == "pending" },
            "مؤكد" to filteredOrders.count { it.status == "confirmed" },
            "قيد المعالجة" to filteredOrders.count { it.status == "processing" },
            "مشحون" to filteredOrders.count { it.status == "shipped" },
            "ملغي" to filteredOrders.count { it.status == "cancelled" }
        )

        val entries = mutableListOf<PieEntry>()
        statusCounts.forEach { (status, count) ->
            if (count > 0) {
                entries.add(PieEntry(count.toFloat(), status))
            }
        }

        if (entries.isEmpty()) {
            pieChart.visibility = View.GONE
            binding.tvNoStatusData.visibility = View.VISIBLE
            return
        } else {
            pieChart.visibility = View.VISIBLE
            binding.tvNoStatusData.visibility = View.GONE
        }

        val dataSet = PieDataSet(entries, "توزيع حالات الطلبات")
        dataSet.colors = listOf(
            Color.parseColor("#4CAF50"), // Green for completed
            Color.parseColor("#FF9800"), // Orange for pending
            Color.parseColor("#2196F3"), // Blue for confirmed
            Color.parseColor("#9C27B0"), // Purple for processing
            Color.parseColor("#00BCD4"), // Cyan for shipped
            Color.parseColor("#F44336")  // Red for cancelled
        )
        dataSet.valueTextSize = 12f
        dataSet.valueTextColor = Color.WHITE

        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())

        pieChart.apply {
            this.data = data
            description.isEnabled = false
            isRotationEnabled = true
            setUsePercentValues(true)
            setEntryLabelColor(Color.BLACK)
            setEntryLabelTextSize(10f)
            animateY(1000, Easing.EaseInOutQuad)
            invalidate()
        }
    }

    private fun setupTopProductsChart() {
        val barChart = binding.barChartTopProducts

        // Count product sales from order items
        val productSales = mutableMapOf<String, Int>()

        filteredOrders.filter { it.status == "delivered" }.forEach { order ->
            order.items.forEach { item ->
                val productName = item.productName
                productSales[productName] = productSales.getOrDefault(productName, 0) + item.quantity
            }
        }

        // Get top 5 products
        val topProducts = productSales.toList().sortedByDescending { it.second }.take(5)

        if (topProducts.isEmpty()) {
            barChart.visibility = View.GONE
            binding.tvNoProductData.visibility = View.VISIBLE
            return
        } else {
            barChart.visibility = View.VISIBLE
            binding.tvNoProductData.visibility = View.GONE
        }

        val entries = mutableListOf<BarEntry>()
        val labels = mutableListOf<String>()

        topProducts.forEachIndexed { index, (productName, quantity) ->
            entries.add(BarEntry(index.toFloat(), quantity.toFloat()))
            labels.add(if (productName.length > 10) "${productName.take(10)}..." else productName)
        }

        val dataSet = BarDataSet(entries, "أكثر المنتجات مبيعاً")
        dataSet.colors = ColorTemplate.MATERIAL_COLORS.toList()
        dataSet.valueTextSize = 12f

        val data = BarData(dataSet)
        data.barWidth = 0.8f

        barChart.apply {
            this.data = data
            description.isEnabled = false
            setFitBars(true)

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = IndexAxisValueFormatter(labels)
                granularity = 1f
                setDrawGridLines(false)
                labelRotationAngle = -45f
            }

            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }

            axisRight.isEnabled = false
            legend.isEnabled = true

            animateY(1000)
            invalidate()
        }
    }

    private fun showReportSection() {
        binding.cardReportSummary.visibility = View.VISIBLE
        binding.cardCharts.visibility = View.VISIBLE
        binding.cardExportOptions.visibility = View.VISIBLE
    }

    private fun exportReportToPdf() {
        lifecycleScope.launch {
            try {
                binding.btnExportPdf.isEnabled = false
                binding.btnExportPdf.text = "جاري التصدير..."

                val result = exportHelper.exportOrdersToCSV(filteredOrders)

                if (result.isSuccess) {
                    val file = result.getOrNull()!!
                    showToast("تم تصدير التقرير بنجاح")
                    openFile(file)
                } else {
                    showToast("فشل في تصدير التقرير: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                showToast("خطأ في تصدير التقرير: ${e.message}")
            } finally {
                binding.btnExportPdf.isEnabled = true
                binding.btnExportPdf.text = "تصدير PDF"
            }
        }
    }

    private fun exportReportToExcel() {
        lifecycleScope.launch {
            try {
                binding.btnExportExcel.isEnabled = false
                binding.btnExportExcel.text = "جاري التصدير..."

                val result = exportHelper.exportOrdersToCSV(filteredOrders)

                if (result.isSuccess) {
                    val file = result.getOrNull()!!
                    showToast("تم تصدير التقرير بنجاح")
                    openFile(file)
                } else {
                    showToast("فشل في تصدير التقرير: ${result.exceptionOrNull()?.message}")
                }
            } catch (e: Exception) {
                showToast("خطأ في تصدير التقرير: ${e.message}")
            } finally {
                binding.btnExportExcel.isEnabled = true
                binding.btnExportExcel.text = "تصدير Excel"
            }
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun openFile(file: java.io.File) {
        try {
            val intent = android.content.Intent(android.content.Intent.ACTION_VIEW)
            val uri = androidx.core.content.FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )
            intent.setDataAndType(uri, "text/csv")
            intent.addFlags(android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION)
            startActivity(intent)
        } catch (e: Exception) {
            showToast("لا يمكن فتح الملف")
        }
    }
}
