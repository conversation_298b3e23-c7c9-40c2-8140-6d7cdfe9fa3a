<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Product Image -->
        <ImageView
            android:id="@+id/ivProductImage"
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="12dp"
            android:background="@color/light_gray"
            android:scaleType="centerCrop"
            tools:src="@drawable/ic_product_placeholder" />

        <!-- Product Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Product Name -->
            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="ثلاجة سامسونج 18 قدم" />

            <!-- Price -->
            <TextView
                android:id="@+id/tvProductPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/primary_blue"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="1200 دينار عراقي" />

            <!-- Quantity Controls -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- Decrease Button -->
                <ImageButton
                    android:id="@+id/btnDecrease"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/quantity_button_background"
                    android:src="@drawable/ic_remove"
                    android:tint="@color/primary_blue" />

                <!-- Quantity -->
                <TextView
                    android:id="@+id/tvQuantity"
                    android:layout_width="48dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="2" />

                <!-- Increase Button -->
                <ImageButton
                    android:id="@+id/btnIncrease"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="@drawable/quantity_button_background"
                    android:src="@drawable/ic_add"
                    android:tint="@color/primary_blue" />

                <!-- Spacer -->
                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_weight="1" />

                <!-- Remove Button -->
                <ImageButton
                    android:id="@+id/btnRemove"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_delete"
                    android:tint="@color/error_red" />

            </LinearLayout>

            <!-- Total Price for this item -->
            <TextView
                android:id="@+id/tvTotalPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="المجموع: 2400 دينار عراقي" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
