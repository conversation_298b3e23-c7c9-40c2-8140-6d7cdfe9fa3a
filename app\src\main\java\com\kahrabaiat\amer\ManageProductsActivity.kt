package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.ProductManagementAdapter
import com.kahrabaiat.amer.databinding.ActivityManageProductsBinding
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.utils.DataManager
import kotlinx.coroutines.launch

class ManageProductsActivity : BaseAdminActivity() {

    private lateinit var binding: ActivityManageProductsBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var dataManager: DataManager
    private lateinit var productAdapter: ProductManagementAdapter
    private var allProducts = mutableListOf<Product>()
    private var filteredProducts = mutableListOf<Product>()
    private var currentSearchQuery = ""
    private var currentCategoryFilter = "ALL"

    companion object {
        private const val REQUEST_ADD_PRODUCT = 1001
        private const val REQUEST_EDIT_PRODUCT = 1002
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityManageProductsBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupRecyclerView()
            setupClickListeners()
            loadProducts()

            // التحقق من فلاتر المخزون من الإشعارات
            handleInventoryFilters()
        } catch (e: Exception) {
            android.util.Log.e("ManageProductsActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة إدارة المنتجات: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        databaseHelper = DatabaseHelper.getInstance(this)
        dataManager = DataManager.getInstance(this)
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "إدارة المنتجات"
            }
        } catch (e: Exception) {
            android.util.Log.e("ManageProductsActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        try {
            productAdapter = ProductManagementAdapter(
                onEditClick = { product -> editProduct(product) },
                onDeleteClick = { product -> showDeleteConfirmation(product) },
                onToggleAvailability = { product -> toggleProductAvailability(product) }
            )

            binding.rvProducts.apply {
                layoutManager = LinearLayoutManager(this@ManageProductsActivity)
                adapter = productAdapter
            }
        } catch (e: Exception) {
            android.util.Log.e("ManageProductsActivity", "Error setting up RecyclerView", e)
            showToast("خطأ في إعداد قائمة المنتجات")
        }
    }

    private fun setupClickListeners() {
        binding.fabAddProduct.setOnClickListener {
            val intent = Intent(this, AddProductActivity::class.java)
            startActivityForResult(intent, REQUEST_ADD_PRODUCT)
        }

        binding.swipeRefresh.setOnRefreshListener {
            loadProducts()
        }
    }

    private fun loadProducts() {
        lifecycleScope.launch {
            try {
                binding.swipeRefresh.isRefreshing = true
                binding.progressBar.visibility = View.VISIBLE

                val products = dataManager.getAllProducts()
                allProducts.clear()
                allProducts.addAll(products)

                android.util.Log.d("ManageProductsActivity", "Loaded ${products.size} products")

                applyFilters()

            } catch (e: Exception) {
                android.util.Log.e("ManageProductsActivity", "Error loading products", e)
                showToast("خطأ في تحميل المنتجات: ${e.message}")
            } finally {
                binding.swipeRefresh.isRefreshing = false
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun applyFilters() {
        filteredProducts.clear()

        var products = allProducts.toList()

        // Apply search filter
        if (currentSearchQuery.isNotEmpty()) {
            products = products.filter { product ->
                product.name.contains(currentSearchQuery, ignoreCase = true) ||
                product.description.contains(currentSearchQuery, ignoreCase = true) ||
                getCategoryDisplayName(product.category).contains(currentSearchQuery, ignoreCase = true)
            }
        }

        // Apply category filter
        if (currentCategoryFilter != "ALL") {
            products = products.filter { it.category == currentCategoryFilter }
        }

        filteredProducts.addAll(products)
        updateUI()
    }

    private fun updateUI() {
        if (filteredProducts.isEmpty()) {
            binding.rvProducts.visibility = View.GONE
            binding.tvEmptyState.visibility = View.VISIBLE

            binding.tvEmptyState.text = if (currentSearchQuery.isNotEmpty() || currentCategoryFilter != "ALL") {
                "لم يتم العثور على منتجات مطابقة\nجرب تغيير معايير البحث"
            } else {
                "لا توجد منتجات حتى الآن\nاضغط على + لإضافة منتج جديد"
            }
        } else {
            binding.rvProducts.visibility = View.VISIBLE
            binding.tvEmptyState.visibility = View.GONE
            productAdapter.updateProducts(filteredProducts)
        }

        // Update toolbar subtitle with count
        val totalText = if (currentSearchQuery.isNotEmpty() || currentCategoryFilter != "ALL") {
            "${filteredProducts.size} من ${allProducts.size} منتج"
        } else {
            "${allProducts.size} منتج"
        }
        supportActionBar?.subtitle = totalText
    }

    private fun getCategoryDisplayName(category: String): String {
        return when (category) {
            com.kahrabaiat.amer.models.CategoryConstants.HOME_APPLIANCES -> "الأجهزة المنزلية"
            com.kahrabaiat.amer.models.CategoryConstants.ELECTRICAL_APPLIANCES -> "الأجهزة الكهربائية"
            com.kahrabaiat.amer.models.CategoryConstants.HAND_TOOLS -> "العدد اليدوية"
            else -> category
        }
    }

    private fun editProduct(product: Product) {
        val intent = Intent(this, AddProductActivity::class.java)
        intent.putExtra("product", product)
        startActivityForResult(intent, REQUEST_EDIT_PRODUCT)
    }

    private fun showDeleteConfirmation(product: Product) {
        AlertDialog.Builder(this)
            .setTitle("حذف المنتج")
            .setMessage("هل أنت متأكد من حذف المنتج \"${product.name}\"؟\nلا يمكن التراجع عن هذا الإجراء.")
            .setPositiveButton("حذف") { _, _ ->
                deleteProduct(product)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun deleteProduct(product: Product) {
        lifecycleScope.launch {
            try {
                val success = databaseHelper.deleteProduct(product.id)
                if (success) {
                    showToast("تم حذف المنتج بنجاح")
                    loadProducts() // Refresh the list
                } else {
                    showToast("فشل في حذف المنتج")
                }
            } catch (e: Exception) {
                android.util.Log.e("ManageProductsActivity", "Error deleting product", e)
                showToast("خطأ في حذف المنتج: ${e.message}")
            }
        }
    }

    private fun toggleProductAvailability(product: Product) {
        lifecycleScope.launch {
            try {
                val newAvailability = !product.available
                val success = databaseHelper.updateProductAvailability(product.id, newAvailability)

                if (success) {
                    val status = if (newAvailability) "متاح" else "غير متاح"
                    showToast("تم تغيير حالة المنتج إلى: $status")

                    // تحديث المنتج في القائمة المحلية
                    val updatedProduct = product.copy(available = newAvailability)
                    val index = allProducts.indexOfFirst { it.id == product.id }
                    if (index != -1) {
                        allProducts[index] = updatedProduct
                        applyFilters() // إعادة تطبيق الفلاتر وتحديث العرض
                    }
                } else {
                    showToast("فشل في تحديث حالة المنتج")
                }
            } catch (e: Exception) {
                android.util.Log.e("ManageProductsActivity", "Error toggling product availability", e)
                showToast("خطأ في تحديث حالة المنتج: ${e.message}")
            }
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.manage_products_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_search -> {
                showSearchDialog()
                true
            }
            R.id.action_filter -> {
                showFilterDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onResume() {
        super.onResume()
        // تحديث قائمة المنتجات عند العودة للنشاط
        loadProducts()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_ADD_PRODUCT, REQUEST_EDIT_PRODUCT -> {
                    loadProducts() // Refresh the list
                }
            }
        }
    }

    private fun showSearchDialog() {
        val editText = android.widget.EditText(this)
        editText.setText(currentSearchQuery)
        editText.hint = "ابحث في المنتجات..."
        editText.layoutDirection = android.view.View.LAYOUT_DIRECTION_RTL

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("بحث في المنتجات")
            .setMessage("يمكنك البحث في اسم المنتج، الوصف، أو الفئة")
            .setView(editText)
            .setPositiveButton("بحث") { _, _ ->
                currentSearchQuery = editText.text.toString().trim()
                applyFilters()
            }
            .setNegativeButton("مسح") { _, _ ->
                currentSearchQuery = ""
                applyFilters()
            }
            .setNeutralButton("إلغاء", null)
            .show()
    }

    private fun showFilterDialog() {
        val categories = arrayOf(
            "جميع الفئات",
            "الأجهزة المنزلية",
            "الأجهزة الكهربائية",
            "العدد اليدوية"
        )

        val categoryValues = arrayOf(
            "ALL",
            com.kahrabaiat.amer.models.CategoryConstants.HOME_APPLIANCES,
            com.kahrabaiat.amer.models.CategoryConstants.ELECTRICAL_APPLIANCES,
            com.kahrabaiat.amer.models.CategoryConstants.HAND_TOOLS
        )

        val currentIndex = categoryValues.indexOf(currentCategoryFilter)

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("فلترة حسب الفئة")
            .setSingleChoiceItems(categories, currentIndex) { dialog, which ->
                currentCategoryFilter = categoryValues[which]
                applyFilters()
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    /**
     * معالجة فلاتر المخزون من الإشعارات
     */
    private fun handleInventoryFilters() {
        val filterOutOfStock = intent.getBooleanExtra("filter_out_of_stock", false)
        val filterLowStock = intent.getBooleanExtra("filter_low_stock", false)

        if (filterOutOfStock || filterLowStock) {
            // انتظار تحميل المنتجات ثم تطبيق الفلتر
            lifecycleScope.launch {
                // انتظار قصير لضمان تحميل المنتجات
                kotlinx.coroutines.delay(1000)

                when {
                    filterOutOfStock -> filterByStockStatus(StockFilter.OUT_OF_STOCK)
                    filterLowStock -> filterByStockStatus(StockFilter.LOW_STOCK)
                }
            }
        }
    }

    /**
     * فلترة حسب حالة المخزون
     */
    private fun filterByStockStatus(filter: StockFilter) {
        val inventoryManager = com.kahrabaiat.amer.utils.InventoryManager.getInstance(this)

        filteredProducts.clear()

        allProducts.forEach { product ->
            val stockStatus = inventoryManager.checkSingleProduct(product)

            val shouldInclude = when (filter) {
                StockFilter.OUT_OF_STOCK -> stockStatus == com.kahrabaiat.amer.utils.InventoryManager.StockStatus.OUT_OF_STOCK
                StockFilter.LOW_STOCK -> stockStatus == com.kahrabaiat.amer.utils.InventoryManager.StockStatus.LOW ||
                                       stockStatus == com.kahrabaiat.amer.utils.InventoryManager.StockStatus.CRITICAL
                StockFilter.ALL -> true
            }

            if (shouldInclude) {
                filteredProducts.add(product)
            }
        }

        productAdapter.updateProducts(filteredProducts)
        updateEmptyState()

        // عرض رسالة توضيحية
        val message = when (filter) {
            StockFilter.OUT_OF_STOCK -> "عرض المنتجات التي نفد مخزونها (${filteredProducts.size})"
            StockFilter.LOW_STOCK -> "عرض المنتجات ذات المخزون المنخفض (${filteredProducts.size})"
            StockFilter.ALL -> "جميع المنتجات (${filteredProducts.size})"
        }

        showToast(message)
    }

    private fun updateEmptyState() {
        // يمكن إضافة منطق لعرض رسالة عندما تكون القائمة فارغة
        // مثل إخفاء/إظهار TextView للحالة الفارغة
    }



    enum class StockFilter {
        ALL,
        LOW_STOCK,
        OUT_OF_STOCK
    }
}
