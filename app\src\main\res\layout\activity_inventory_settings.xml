<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background_main"
    tools:context=".InventorySettingsActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_header_background"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Current Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                android:background="@drawable/card_gradient_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="حالة المخزون الحالية"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <TextView
                        android:id="@+id/tvLowStockCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="منتجات مخزون منخفض: 0"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_inventory"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:id="@+id/tvOutOfStockCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="منتجات نفد مخزونها: 0"
                        android:textSize="16sp"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_warning"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:id="@+id/tvLastCheck"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="آخر فحص: لم يتم بعد"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary"
                        android:drawableStart="@drawable/ic_schedule"
                        android:drawablePadding="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Settings Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="4dp"
                android:background="@drawable/card_gradient_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إعدادات التنبيهات"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp" />

                    <!-- Low Stock Threshold -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="حد المخزون المنخفض"
                        app:startIconDrawable="@drawable/ic_inventory"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etLowStockThreshold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Critical Stock Threshold -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="حد المخزون الحرج"
                        app:startIconDrawable="@drawable/ic_warning"
                        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCriticalStockThreshold"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Notifications Switch -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="16dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:src="@drawable/ic_notifications"
                            android:layout_marginEnd="12dp"
                            android:tint="@color/primary_blue" />

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="تفعيل إشعارات المخزون"
                            android:textSize="16sp"
                            android:textColor="@color/text_primary" />

                        <androidx.appcompat.widget.SwitchCompat
                            android:id="@+id/switchNotifications"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                    <!-- Help Text -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• الحد المنخفض: عندما يصل المخزون لهذا الرقم سيتم إرسال تنبيه\n• الحد الحرج: عندما يصل المخزون لهذا الرقم سيتم إرسال تحذير عاجل\n• يجب أن يكون الحد الحرج أقل من الحد المنخفض"
                        android:textSize="12sp"
                        android:textColor="@color/text_secondary"
                        android:background="@drawable/info_background"
                        android:padding="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <Button
                    android:id="@+id/btnSaveSettings"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="حفظ الإعدادات"
                    android:background="@drawable/button_primary_gradient"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:padding="16dp" />

                <Button
                    android:id="@+id/btnCheckNow"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:text="فحص المخزون الآن"
                    android:background="@drawable/button_success_gradient"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:padding="16dp" />

                <Button
                    android:id="@+id/btnTestNotification"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اختبار الإشعارات"
                    android:textColor="@color/primary_blue"
                    android:textSize="16sp"
                    android:padding="16dp" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
