package com.kahrabaiat.amer.network

import android.content.Context
import android.net.ConnectivityManager
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.os.Build
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue

class NetworkManager private constructor(private val context: Context) {

    companion object {
        private const val TAG = "NetworkManager"
        private const val PING_INTERVAL = 30000L // 30 ثانية
        private const val MAX_RETRY_ATTEMPTS = 3
        private const val RETRY_DELAY = 2000L // 2 ثانية

        @Volatile
        private var INSTANCE: NetworkManager? = null

        fun getInstance(context: Context): NetworkManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: NetworkManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
    private val networkListeners = mutableListOf<NetworkListener>()
    private val requestQueue = ConcurrentLinkedQueue<NetworkRequest>()

    private var isMonitoring = false
    private var monitoringJob: Job? = null
    private var currentNetworkState = NetworkState.UNKNOWN

    private val networkCallback = object : ConnectivityManager.NetworkCallback() {
        override fun onAvailable(network: Network) {
            super.onAvailable(network)
            Log.d(TAG, "Network available: $network")
            updateNetworkState(NetworkState.CONNECTED)
            processQueuedRequests()
        }

        override fun onLost(network: Network) {
            super.onLost(network)
            Log.d(TAG, "Network lost: $network")
            updateNetworkState(NetworkState.DISCONNECTED)
        }

        override fun onCapabilitiesChanged(network: Network, networkCapabilities: NetworkCapabilities) {
            super.onCapabilitiesChanged(network, networkCapabilities)
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val hasValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

            if (hasInternet && hasValidated) {
                updateNetworkState(NetworkState.CONNECTED)
            } else {
                updateNetworkState(NetworkState.LIMITED)
            }
        }
    }

    /**
     * بدء مراقبة الشبكة
     */
    fun startMonitoring() {
        if (isMonitoring) return

        isMonitoring = true

        // تسجيل callback للشبكة
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            connectivityManager.registerDefaultNetworkCallback(networkCallback)
        } else {
            val networkRequest = android.net.NetworkRequest.Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .build()
            connectivityManager.registerNetworkCallback(networkRequest, networkCallback)
        }

        // بدء مراقبة دورية
        startPeriodicMonitoring()

        // فحص الحالة الأولية
        updateNetworkState(getCurrentNetworkState())

        Log.d(TAG, "Network monitoring started")
    }

    /**
     * إيقاف مراقبة الشبكة
     */
    fun stopMonitoring() {
        if (!isMonitoring) return

        isMonitoring = false
        monitoringJob?.cancel()

        try {
            connectivityManager.unregisterNetworkCallback(networkCallback)
        } catch (e: Exception) {
            Log.e(TAG, "Error unregistering network callback", e)
        }

        Log.d(TAG, "Network monitoring stopped")
    }

    /**
     * بدء المراقبة الدورية
     */
    private fun startPeriodicMonitoring() {
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            while (isMonitoring) {
                try {
                    val networkState = getCurrentNetworkState()
                    updateNetworkState(networkState)

                    // فحص جودة الاتصال
                    if (networkState == NetworkState.CONNECTED) {
                        checkNetworkQuality()
                    }

                    delay(PING_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error in periodic network monitoring", e)
                }
            }
        }
    }

    /**
     * الحصول على حالة الشبكة الحالية
     */
    private fun getCurrentNetworkState(): NetworkState {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val activeNetwork = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)

                when {
                    networkCapabilities == null -> NetworkState.DISCONNECTED
                    networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                    networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED) -> NetworkState.CONNECTED
                    networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) -> NetworkState.LIMITED
                    else -> NetworkState.DISCONNECTED
                }
            } else {
                @Suppress("DEPRECATION")
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                if (activeNetworkInfo?.isConnected == true) {
                    NetworkState.CONNECTED
                } else {
                    NetworkState.DISCONNECTED
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting network state", e)
            NetworkState.UNKNOWN
        }
    }

    /**
     * تحديث حالة الشبكة
     */
    private fun updateNetworkState(newState: NetworkState) {
        if (currentNetworkState != newState) {
            val oldState = currentNetworkState
            currentNetworkState = newState

            Log.d(TAG, "Network state changed: $oldState -> $newState")

            // إشعار المستمعين
            notifyNetworkStateChanged(oldState, newState)
        }
    }

    /**
     * فحص جودة الشبكة
     */
    private suspend fun checkNetworkQuality() {
        try {
            val startTime = System.currentTimeMillis()

            // محاولة ping بسيط
            val process = Runtime.getRuntime().exec("ping -c 1 8.8.8.8")
            val exitCode = process.waitFor()

            val endTime = System.currentTimeMillis()
            val latency = endTime - startTime

            val quality = when {
                exitCode != 0 -> NetworkQuality.POOR
                latency < 100 -> NetworkQuality.EXCELLENT
                latency < 300 -> NetworkQuality.GOOD
                latency < 1000 -> NetworkQuality.FAIR
                else -> NetworkQuality.POOR
            }

            notifyNetworkQualityChanged(quality, latency)

        } catch (e: Exception) {
            Log.e(TAG, "Error checking network quality", e)
        }
    }

    /**
     * إشعار المستمعين بتغيير حالة الشبكة
     */
    private fun notifyNetworkStateChanged(oldState: NetworkState, newState: NetworkState) {
        networkListeners.forEach { listener ->
            try {
                listener.onNetworkStateChanged(oldState, newState)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying network listener", e)
            }
        }
    }

    /**
     * إشعار المستمعين بتغيير جودة الشبكة
     */
    private fun notifyNetworkQualityChanged(quality: NetworkQuality, latency: Long) {
        networkListeners.forEach { listener ->
            try {
                listener.onNetworkQualityChanged(quality, latency)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying network quality listener", e)
            }
        }
    }

    /**
     * إضافة مستمع للشبكة
     */
    fun addNetworkListener(listener: NetworkListener) {
        networkListeners.add(listener)
    }

    /**
     * إزالة مستمع الشبكة
     */
    fun removeNetworkListener(listener: NetworkListener) {
        networkListeners.remove(listener)
    }

    /**
     * التحقق من توفر الاتصال
     */
    fun isConnected(): Boolean {
        return currentNetworkState == NetworkState.CONNECTED
    }

    /**
     * التحقق من نوع الشبكة
     */
    fun getNetworkType(): NetworkType {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val activeNetwork = connectivityManager.activeNetwork
                val networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork)

                when {
                    networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) == true -> NetworkType.WIFI
                    networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) == true -> NetworkType.MOBILE
                    networkCapabilities?.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) == true -> NetworkType.ETHERNET
                    else -> NetworkType.UNKNOWN
                }
            } else {
                @Suppress("DEPRECATION")
                val activeNetworkInfo = connectivityManager.activeNetworkInfo
                when (activeNetworkInfo?.type) {
                    ConnectivityManager.TYPE_WIFI -> NetworkType.WIFI
                    ConnectivityManager.TYPE_MOBILE -> NetworkType.MOBILE
                    ConnectivityManager.TYPE_ETHERNET -> NetworkType.ETHERNET
                    else -> NetworkType.UNKNOWN
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting network type", e)
            NetworkType.UNKNOWN
        }
    }

    /**
     * إضافة طلب للطابور
     */
    fun queueRequest(request: NetworkRequest) {
        requestQueue.offer(request)

        if (isConnected()) {
            processQueuedRequests()
        }
    }

    /**
     * معالجة الطلبات المؤجلة
     */
    private fun processQueuedRequests() {
        CoroutineScope(Dispatchers.IO).launch {
            while (requestQueue.isNotEmpty() && isConnected()) {
                val request = requestQueue.poll()
                request?.let { executeRequest(it) }
            }
        }
    }

    /**
     * تنفيذ طلب شبكة مع إعادة المحاولة
     */
    private suspend fun executeRequest(request: NetworkRequest) {
        var attempts = 0

        while (attempts < MAX_RETRY_ATTEMPTS && isConnected()) {
            try {
                request.execute()
                return // نجح الطلب
            } catch (e: Exception) {
                attempts++
                Log.w(TAG, "Network request failed, attempt $attempts/$MAX_RETRY_ATTEMPTS", e)

                if (attempts < MAX_RETRY_ATTEMPTS) {
                    delay(RETRY_DELAY * attempts) // تأخير متزايد
                }
            }
        }

        // فشل الطلب نهائياً
        request.onFailure(Exception("Network request failed after $MAX_RETRY_ATTEMPTS attempts"))
    }

    /**
     * الحصول على معلومات الشبكة
     */
    fun getNetworkInfo(): NetworkInfo {
        return NetworkInfo(
            state = currentNetworkState,
            type = getNetworkType(),
            isMetered = isMeteredConnection(),
            signalStrength = getSignalStrength()
        )
    }

    /**
     * التحقق من الاتصال المحدود
     */
    private fun isMeteredConnection(): Boolean {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                connectivityManager.isActiveNetworkMetered
            } else {
                getNetworkType() == NetworkType.MOBILE
            }
        } catch (e: Exception) {
            false
        }
    }

    /**
     * الحصول على قوة الإشارة
     */
    private fun getSignalStrength(): Int {
        return try {
            // هذا تقدير بسيط، يمكن تحسينه
            when (getNetworkType()) {
                NetworkType.WIFI -> 80 // افتراضي للواي فاي
                NetworkType.MOBILE -> 60 // افتراضي للموبايل
                else -> 50
            }
        } catch (e: Exception) {
            0
        }
    }

    /**
     * مسح طابور الطلبات
     */
    fun clearRequestQueue() {
        requestQueue.clear()
        Log.d(TAG, "Request queue cleared")
    }

    /**
     * الحصول على إحصائيات الشبكة
     */
    fun getNetworkStats(): NetworkStats {
        return NetworkStats(
            queuedRequests = requestQueue.size,
            currentState = currentNetworkState,
            networkType = getNetworkType(),
            isMetered = isMeteredConnection()
        )
    }

    // Data classes and enums
    enum class NetworkState {
        CONNECTED,
        DISCONNECTED,
        LIMITED,
        UNKNOWN
    }

    enum class NetworkType {
        WIFI,
        MOBILE,
        ETHERNET,
        UNKNOWN
    }

    enum class NetworkQuality {
        EXCELLENT,
        GOOD,
        FAIR,
        POOR
    }

    data class NetworkInfo(
        val state: NetworkState,
        val type: NetworkType,
        val isMetered: Boolean,
        val signalStrength: Int
    )

    data class NetworkStats(
        val queuedRequests: Int,
        val currentState: NetworkState,
        val networkType: NetworkType,
        val isMetered: Boolean
    )

    abstract class NetworkRequest {
        abstract suspend fun execute()
        abstract fun onFailure(exception: Exception)
    }

    interface NetworkListener {
        fun onNetworkStateChanged(oldState: NetworkState, newState: NetworkState)
        fun onNetworkQualityChanged(quality: NetworkQuality, latency: Long)
    }
}
