<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".StatisticsActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="32dp"
                    android:visibility="gone" />

                <!-- Overview Cards -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="نظرة عامة"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <!-- Products Overview -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_inventory"
                            android:drawablePadding="8dp"
                            android:text="إحصائيات المنتجات"
                            android:textColor="@color/primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <!-- Total Products -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalProducts"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="25" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي المنتجات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvProductsGrowth"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    tools:text="+12%" />

                            </LinearLayout>

                            <!-- Available Products -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvAvailableProducts"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="20" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="متاح"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <!-- Out of Stock -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvOutOfStockProducts"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/error_red"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="3" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="نفد المخزون"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <!-- Discounted Products -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvDiscountedProducts"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/accent_orange"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="5" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="مخفض"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Orders Overview -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_receipt"
                            android:drawablePadding="8dp"
                            android:text="إحصائيات الطلبات"
                            android:textColor="@color/primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <!-- Total Orders -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="150" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي الطلبات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvOrdersGrowth"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    tools:text="+8%" />

                            </LinearLayout>

                            <!-- Pending Orders -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvPendingOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/warning_yellow"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="12" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="معلق"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <!-- Completed Orders -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvCompletedOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="120" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="مكتمل"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <!-- Cancelled Orders -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvCancelledOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/error_red"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="18" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="ملغي"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Revenue Overview -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="24dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_money"
                            android:drawablePadding="8dp"
                            android:text="إحصائيات الإيرادات"
                            android:textColor="@color/primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <!-- Total Revenue -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalRevenue"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    tools:text="2,500,000 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي الإيرادات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvRevenueGrowth"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    tools:text="+15%" />

                            </LinearLayout>

                            <!-- Pending Revenue -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvPendingRevenue"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/warning_yellow"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="300,000 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إيرادات معلقة"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <!-- Average Order Value -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvAverageOrderValue"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="20,833 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="متوسط قيمة الطلب"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Charts Section -->
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="المخططات البيانية"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <!-- Products Status Chart -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="توزيع حالة المنتجات"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/pieChartProducts"
                            android:layout_width="match_parent"
                            android:layout_height="300dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Orders Status Chart -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="توزيع حالات الطلبات"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/barChartOrders"
                            android:layout_width="match_parent"
                            android:layout_height="300dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Revenue Trend Chart -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="اتجاه الإيرادات الشهرية"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/lineChartRevenue"
                            android:layout_width="match_parent"
                            android:layout_height="300dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Category Distribution Chart -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="توزيع المنتجات حسب الفئة"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/pieChartCategories"
                            android:layout_width="match_parent"
                            android:layout_height="300dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
