rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Product images
    // قراءة للجميع، كتابة للمدير فقط
    match /products/{allPaths=**} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // السماح بالكتابة للمدير فقط
      // في الإنتاج، يجب استخدام Firebase Authentication
      allow write: if true; // مؤقتاً للتطوير
    }
    
    // Category images
    match /categories/{allPaths=**} {
      allow read: if true;
      allow write: if true; // مؤقتاً للتطوير
    }
    
    // App assets (logos, banners, etc.)
    match /assets/{allPaths=**} {
      allow read: if true;
      allow write: if true; // مؤقتاً للتطوير
    }
    
    // User uploads (للمستقبل)
    match /users/{userId}/{allPaths=**} {
      allow read, write: if true; // مؤقتاً للتطوير
    }
    
    // Temporary uploads
    match /temp/{allPaths=**} {
      allow read, write: if true;
    }
  }
}

// ملاحظات للإنتاج:
// 1. استبدال "if true" بقواعد أمان حقيقية
// 2. إضافة قيود على حجم الملفات
// 3. إضافة قيود على نوع الملفات
// 4. استخدام Firebase Authentication

// مثال على قواعد أمان متقدمة (للمستقبل):
/*
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /products/{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null && 
                   request.auth.token.admin == true &&
                   resource.size < 5 * 1024 * 1024 && // 5MB max
                   resource.contentType.matches('image/.*');
    }
  }
}
*/
