<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- User Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:orientation="horizontal">

            <!-- User Avatar -->
            <ImageView
                android:id="@+id/ivUserAvatar"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/circle_background_primary"
                android:padding="12dp"
                android:src="@drawable/ic_person"
                android:tint="@color/white" />

            <!-- User Info -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvUserName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="أحمد محمد" />

                <TextView
                    android:id="@+id/tvUserEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="<EMAIL>" />

                <TextView
                    android:id="@+id/tvUserPhone"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="07901234567" />

            </LinearLayout>

            <!-- Role Badge -->
            <TextView
                android:id="@+id/tvUserRole"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:background="@drawable/rounded_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="مدير" />

        </LinearLayout>

        <!-- User Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvUserStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="نشط" />

                <TextView
                    android:id="@+id/tvPermissionsCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="الصلاحيات: 15" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCreatedAt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="تاريخ الإنشاء: 15/01/2025" />

                <TextView
                    android:id="@+id/tvLastLogin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="آخر دخول: 15/01/2025 14:30" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEdit"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="تعديل"
                app:icon="@drawable/ic_edit"
                app:iconGravity="textStart"
                app:iconSize="16dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnToggleStatus"
                style="@style/Widget.Material3.Button.TonalButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="4dp"
                android:layout_weight="1"
                android:text="تفعيل"
                app:icon="@drawable/ic_toggle"
                app:iconGravity="textStart"
                app:iconSize="16dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="حذف"
                android:textColor="@color/error_red"
                app:icon="@drawable/ic_delete"
                app:iconGravity="textStart"
                app:iconSize="16dp"
                app:iconTint="@color/error_red"
                app:strokeColor="@color/error_red" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
