<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="120dp"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Category Icon -->
        <ImageView
            android:id="@+id/ivCategoryIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginBottom="8dp"
            android:tint="@color/primary_blue"
            tools:src="@drawable/ic_electrical" />

        <!-- Category Name -->
        <TextView
            android:id="@+id/tvCategoryName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="الأجهزة الكهربائية" />

        <!-- Product Count -->
        <TextView
            android:id="@+id/tvProductCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            tools:text="25 منتج" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
