<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:id="@+id/tvTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اختبار اتصال Firebase"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:gravity="center"
                    android:fontFamily="@font/cairo_bold" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="تحقق من اتصال التطبيق بقاعدة البيانات السحابية"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:fontFamily="@font/cairo_regular" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Status Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="حالة الاتصال"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="end"
                    android:fontFamily="@font/cairo_bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="جاري الاختبار..."
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:fontFamily="@font/cairo_regular"
                    android:padding="12dp"
                    android:background="@drawable/rounded_background"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/tvDetails"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text=""
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:gravity="center"
                    android:fontFamily="@font/cairo_regular" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Test Buttons -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="اختبارات Firebase"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="end"
                    android:fontFamily="@font/cairo_bold"
                    android:layout_marginBottom="16dp" />

                <!-- Test Connection Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnTestConnection"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="🔄 اختبار الاتصال"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:fontFamily="@font/cairo_bold"
                    android:background="@drawable/button_primary_gradient"
                    android:layout_marginBottom="12dp"
                    app:cornerRadius="12dp" />

                <!-- Add Test Product Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAddTestProduct"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="➕ إضافة منتج تجريبي"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:fontFamily="@font/cairo_bold"
                    android:background="@drawable/button_success_gradient"
                    android:layout_marginBottom="12dp"
                    app:cornerRadius="12dp" />

                <!-- Load Products Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnLoadProducts"
                    android:layout_width="match_parent"
                    android:layout_height="56dp"
                    android:text="📦 تحميل المنتجات"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:fontFamily="@font/cairo_bold"
                    android:background="@drawable/button_secondary_enhanced"
                    android:layout_marginBottom="12dp"
                    app:cornerRadius="12dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Instructions Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="تعليمات الاختبار"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="end"
                    android:fontFamily="@font/cairo_bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="1. اختبر الاتصال أولاً\n2. أضف منتج تجريبي\n3. حمّل المنتجات للتأكد\n4. تحقق من Firebase Console"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:gravity="end"
                    android:fontFamily="@font/cairo_regular"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Back Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnBack"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="🔙 العودة"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:fontFamily="@font/cairo_bold"
            android:background="@drawable/rounded_background"
            app:cornerRadius="12dp"
            app:strokeColor="@color/primary_blue"
            app:strokeWidth="2dp" />

    </LinearLayout>

</ScrollView>
