package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class CartItem(
    val product: Product,
    var quantity: Int = 1
) : Parcelable {
    
    fun getTotalPrice(): Double {
        return product.getDiscountedPrice() * quantity
    }
    
    fun getFormattedTotalPrice(): String {
        return "${getTotalPrice().toInt()} دينار عراقي"
    }
}
