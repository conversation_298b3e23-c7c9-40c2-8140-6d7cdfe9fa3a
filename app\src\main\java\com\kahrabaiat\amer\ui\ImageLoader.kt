package com.kahrabaiat.amer.ui

import android.content.Context
import android.graphics.drawable.Drawable
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.kahrabaiat.amer.R

object ImageLoader {

    /**
     * تحميل صورة منتج مع تحسينات الأداء
     */
    fun loadProductImage(
        context: Context,
        imageView: ImageView,
        imageUrl: String?,
        onLoadComplete: (() -> Unit)? = null,
        onLoadError: (() -> Unit)? = null
    ) {
        val requestOptions = RequestOptions()
            .placeholder(R.drawable.placeholder_product)
            .error(R.drawable.error_image)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transform(CenterCrop(), RoundedCorners(16))
            .override(300, 300) // تحسين الذاكرة

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)

            .into(imageView)
    }

    /**
     * تحميل صورة دائرية للمستخدم
     */
    fun loadCircularImage(
        context: Context,
        imageView: ImageView,
        imageUrl: String?
    ) {
        val requestOptions = RequestOptions()
            .placeholder(R.drawable.ic_person)
            .error(R.drawable.ic_person)
            .circleCrop()
            .diskCacheStrategy(DiskCacheStrategy.ALL)

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * تحميل صورة مع تأثير blur للخلفية
     */
    fun loadBlurredBackground(
        context: Context,
        imageView: ImageView,
        imageUrl: String?
    ) {
        val requestOptions = RequestOptions()
            .placeholder(R.drawable.gradient_background_main)
            .error(R.drawable.gradient_background_main)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transform(CenterCrop())

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * تحميل صورة مصغرة للمعاينة السريعة
     */
    fun loadThumbnail(
        context: Context,
        imageView: ImageView,
        imageUrl: String?
    ) {
        val requestOptions = RequestOptions()
            .placeholder(R.drawable.placeholder_product)
            .error(R.drawable.error_image)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transform(CenterCrop(), com.bumptech.glide.load.resource.bitmap.RoundedCorners(8))
            .override(150, 150) // صورة مصغرة

        Glide.with(context)
            .load(imageUrl)
            .thumbnail(0.1f) // تحميل نسخة مصغرة أولاً
            .apply(requestOptions)
            .into(imageView)
    }

    /**
     * تحميل صورة مع انيميشن fade in
     */
    fun loadWithFadeIn(
        context: Context,
        imageView: ImageView,
        imageUrl: String?
    ) {
        imageView.alpha = 0f

        val requestOptions = RequestOptions()
            .placeholder(R.drawable.placeholder_product)
            .error(R.drawable.error_image)
            .diskCacheStrategy(DiskCacheStrategy.ALL)
            .transform(CenterCrop(), RoundedCorners(16))

        Glide.with(context)
            .load(imageUrl)
            .apply(requestOptions)

            .into(imageView)
    }

    /**
     * مسح ذاكرة التخزين المؤقت
     */
    fun clearCache(context: Context) {
        Glide.get(context).clearMemory()
        Thread {
            Glide.get(context).clearDiskCache()
        }.start()
    }

    /**
     * إيقاف جميع طلبات التحميل
     */
    fun pauseRequests(context: Context) {
        Glide.with(context).pauseRequests()
    }

    /**
     * استئناف طلبات التحميل
     */
    fun resumeRequests(context: Context) {
        Glide.with(context).resumeRequests()
    }
}
