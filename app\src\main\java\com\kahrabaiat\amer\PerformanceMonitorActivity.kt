package com.kahrabaiat.amer

import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.kahrabaiat.amer.cache.CacheManager
import com.kahrabaiat.amer.databinding.ActivityPerformanceMonitorBinding
import com.kahrabaiat.amer.network.NetworkManager
import com.kahrabaiat.amer.performance.CrashReporter
import com.kahrabaiat.amer.performance.PerformanceMonitor
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class PerformanceMonitorActivity : BaseAdminActivity() {

    private lateinit var binding: ActivityPerformanceMonitorBinding
    private lateinit var performanceMonitor: PerformanceMonitor
    private lateinit var networkManager: NetworkManager
    private lateinit var cacheManager: CacheManager
    private lateinit var crashReporter: CrashReporter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPerformanceMonitorBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        setupToolbar()
        setupClickListeners()
        startRealTimeUpdates()
    }

    private fun initializeComponents() {
        performanceMonitor = PerformanceMonitor.getInstance(this)
        networkManager = NetworkManager.getInstance(this)
        cacheManager = CacheManager.getInstance(this)
        crashReporter = CrashReporter.getInstance(this)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "مراقبة الأداء"
        }
    }

    private fun setupClickListeners() {
        binding.btnOptimizePerformance.setOnClickListener {
            optimizePerformance()
        }

        binding.btnClearCache.setOnClickListener {
            clearCache()
        }

        binding.btnExportReport.setOnClickListener {
            exportPerformanceReport()
        }

        binding.btnRefreshData.setOnClickListener {
            refreshData()
        }

        binding.btnMemoryCleanup.setOnClickListener {
            performMemoryCleanup()
        }

        binding.btnViewCrashReports.setOnClickListener {
            viewCrashReports()
        }
    }

    private fun startRealTimeUpdates() {
        lifecycleScope.launch {
            while (true) {
                try {
                    updatePerformanceData()
                    delay(5000) // تحديث كل 5 ثوان
                } catch (e: Exception) {
                    break
                }
            }
        }
    }

    private fun updatePerformanceData() {
        try {
            // معلومات الأداء
            val currentMetrics = performanceMonitor.getCurrentMetrics()
            if (currentMetrics != null) {
                displayPerformanceMetrics(currentMetrics)
            }

            // معلومات الشبكة
            val networkInfo = networkManager.getNetworkInfo()
            displayNetworkInfo(networkInfo)

            // معلومات التخزين المؤقت
            val cacheStats = cacheManager.getCacheStats()
            displayCacheStats(cacheStats)

            // معلومات الأخطاء
            val crashCount = crashReporter.getCrashCount()
            val lastCrashTime = crashReporter.getLastCrashTime()
            displayCrashInfo(crashCount, lastCrashTime)

        } catch (e: Exception) {
            showToast("خطأ في تحديث بيانات الأداء: ${e.message}")
        }
    }

    private fun displayPerformanceMetrics(metrics: PerformanceMonitor.PerformanceMetrics) {
        binding.apply {
            // الذاكرة
            tvMemoryUsage.text = "استخدام الذاكرة: ${metrics.memoryUsagePercent}%"
            tvMemoryDetails.text = "${formatBytes(metrics.usedMemory)} / ${formatBytes(metrics.maxMemory)}"
            
            progressMemory.progress = metrics.memoryUsagePercent
            
            // تلوين شريط التقدم حسب الاستخدام
            val memoryColor = when {
                metrics.memoryUsagePercent > 85 -> getColor(R.color.error_red)
                metrics.memoryUsagePercent > 70 -> getColor(R.color.warning_yellow)
                else -> getColor(R.color.success_green)
            }
            progressMemory.progressTintList = android.content.res.ColorStateList.valueOf(memoryColor)

            // CPU
            tvCpuUsage.text = "استخدام المعالج: ${"%.1f".format(metrics.cpuUsage)}%"
            progressCpu.progress = metrics.cpuUsage.toInt()

            // التخزين
            tvStorageUsage.text = "استخدام التخزين: ${metrics.storageInfo.usagePercent}%"
            tvStorageDetails.text = "${formatBytes(metrics.storageInfo.usedSpace)} / ${formatBytes(metrics.storageInfo.totalSpace)}"
            progressStorage.progress = metrics.storageInfo.usagePercent

            // حالة الذاكرة المنخفضة
            tvLowMemoryStatus.text = if (metrics.isLowMemory) "تحذير: ذاكرة منخفضة" else "الذاكرة طبيعية"
            tvLowMemoryStatus.setTextColor(
                if (metrics.isLowMemory) getColor(R.color.error_red) else getColor(R.color.success_green)
            )
        }
    }

    private fun displayNetworkInfo(networkInfo: NetworkManager.NetworkInfo) {
        binding.apply {
            tvNetworkStatus.text = when (networkInfo.state) {
                NetworkManager.NetworkState.CONNECTED -> "متصل"
                NetworkManager.NetworkState.DISCONNECTED -> "غير متصل"
                NetworkManager.NetworkState.LIMITED -> "اتصال محدود"
                NetworkManager.NetworkState.UNKNOWN -> "غير معروف"
            }

            tvNetworkType.text = "نوع الشبكة: ${getNetworkTypeText(networkInfo.type)}"
            tvNetworkMetered.text = if (networkInfo.isMetered) "شبكة محدودة البيانات" else "شبكة غير محدودة"

            val networkColor = when (networkInfo.state) {
                NetworkManager.NetworkState.CONNECTED -> getColor(R.color.success_green)
                NetworkManager.NetworkState.LIMITED -> getColor(R.color.warning_yellow)
                else -> getColor(R.color.error_red)
            }
            tvNetworkStatus.setTextColor(networkColor)
        }
    }

    private fun displayCacheStats(cacheStats: CacheManager.CacheStats) {
        binding.apply {
            tvCacheMemoryEntries.text = "عناصر الذاكرة: ${cacheStats.memoryEntries}"
            tvCacheDiskEntries.text = "عناصر القرص: ${cacheStats.diskEntries}"
            tvCacheHitRatio.text = "نسبة النجاح: ${"%.1f".format(cacheStats.hitRatio)}%"
            tvCacheDiskSize.text = "حجم القرص: ${formatBytes(cacheStats.diskSizeBytes)}"

            progressCacheHitRatio.progress = cacheStats.hitRatio.toInt()

            val hitRatioColor = when {
                cacheStats.hitRatio > 80 -> getColor(R.color.success_green)
                cacheStats.hitRatio > 60 -> getColor(R.color.warning_yellow)
                else -> getColor(R.color.error_red)
            }
            progressCacheHitRatio.progressTintList = android.content.res.ColorStateList.valueOf(hitRatioColor)
        }
    }

    private fun displayCrashInfo(crashCount: Int, lastCrashTime: Long) {
        binding.apply {
            // عرض عدد الأخطاء مع رموز تعبيرية
            tvCrashCount.text = when {
                crashCount == 0 -> "🎉 عدد الأخطاء: $crashCount (ممتاز!)"
                crashCount < 5 -> "⚠️ عدد الأخطاء: $crashCount (مقبول)"
                else -> "🚨 عدد الأخطاء: $crashCount (يحتاج انتباه)"
            }

            // عرض آخر خطأ
            if (lastCrashTime > 0) {
                val dateFormat = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale("ar"))
                tvLastCrash.text = "🕒 آخر خطأ: ${dateFormat.format(java.util.Date(lastCrashTime))}"
            } else {
                tvLastCrash.text = "✅ لا توجد أخطاء مسجلة"
            }

            // تلوين النص حسب عدد الأخطاء
            val crashColor = when {
                crashCount == 0 -> getColor(R.color.success_green)
                crashCount < 5 -> getColor(R.color.warning_yellow)
                else -> getColor(R.color.error_red)
            }
            tvCrashCount.setTextColor(crashColor)

            // تحديث لون آخر خطأ
            val lastCrashColor = if (lastCrashTime > 0) {
                getColor(R.color.text_secondary)
            } else {
                getColor(R.color.success_green)
            }
            tvLastCrash.setTextColor(lastCrashColor)
        }
    }

    private fun optimizePerformance() {
        lifecycleScope.launch {
            try {
                binding.btnOptimizePerformance.isEnabled = false
                binding.btnOptimizePerformance.text = "جاري التحسين..."

                // تحسين الأداء
                performanceMonitor.optimizePerformance()
                
                // تحسين التخزين المؤقت
                cacheManager.optimizeCache()
                
                // تنظيف الذاكرة
                performanceMonitor.performMemoryCleanup()

                delay(2000) // انتظار قصير

                showToast("تم تحسين الأداء بنجاح")
                refreshData()

            } catch (e: Exception) {
                showToast("خطأ في تحسين الأداء: ${e.message}")
            } finally {
                binding.btnOptimizePerformance.isEnabled = true
                binding.btnOptimizePerformance.text = "تحسين الأداء"
            }
        }
    }

    private fun clearCache() {
        lifecycleScope.launch {
            try {
                androidx.appcompat.app.AlertDialog.Builder(this@PerformanceMonitorActivity)
                    .setTitle("مسح التخزين المؤقت")
                    .setMessage("هل أنت متأكد من مسح جميع البيانات المخزنة مؤقتاً؟")
                    .setPositiveButton("نعم") { _, _ ->
                        lifecycleScope.launch {
                            cacheManager.clear()
                            showToast("تم مسح التخزين المؤقت")
                            refreshData()
                        }
                    }
                    .setNegativeButton("إلغاء", null)
                    .show()

            } catch (e: Exception) {
                showToast("خطأ في مسح التخزين المؤقت: ${e.message}")
            }
        }
    }

    private fun performMemoryCleanup() {
        lifecycleScope.launch {
            try {
                binding.btnMemoryCleanup.isEnabled = false
                binding.btnMemoryCleanup.text = "جاري التنظيف..."

                performanceMonitor.performMemoryCleanup()
                
                delay(1000)
                
                showToast("تم تنظيف الذاكرة")
                refreshData()

            } catch (e: Exception) {
                showToast("خطأ في تنظيف الذاكرة: ${e.message}")
            } finally {
                binding.btnMemoryCleanup.isEnabled = true
                binding.btnMemoryCleanup.text = "تنظيف الذاكرة"
            }
        }
    }

    private fun exportPerformanceReport() {
        lifecycleScope.launch {
            try {
                val performanceReport = performanceMonitor.getPerformanceReport()
                val cacheReport = cacheManager.exportCacheReport()
                val crashReport = crashReporter.exportCrashReport()
                val networkStats = networkManager.getNetworkStats()

                val fullReport = buildString {
                    appendLine("=== تقرير الأداء الشامل ===")
                    appendLine("التاريخ: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date())}")
                    appendLine()
                    
                    appendLine("=== ملخص الأداء ===")
                    appendLine("العينات: ${performanceReport.totalSamples}")
                    appendLine("متوسط استخدام الذاكرة: ${performanceReport.averageMemoryUsage}%")
                    appendLine("أقصى استخدام ذاكرة: ${performanceReport.maxMemoryUsage}%")
                    appendLine("متوسط استخدام المعالج: ${"%.1f".format(performanceReport.averageCpuUsage)}%")
                    appendLine("أحداث ذاكرة منخفضة: ${performanceReport.lowMemoryEvents}")
                    appendLine("مشاكل الشبكة: ${performanceReport.networkIssues}")
                    appendLine()
                    
                    appendLine("=== حالة الشبكة ===")
                    appendLine("الحالة: ${networkStats.currentState}")
                    appendLine("النوع: ${networkStats.networkType}")
                    appendLine("محدودة البيانات: ${networkStats.isMetered}")
                    appendLine("الطلبات المؤجلة: ${networkStats.queuedRequests}")
                    appendLine()
                    
                    appendLine(cacheReport)
                    appendLine()
                    appendLine(crashReport)
                }

                shareReport(fullReport)

            } catch (e: Exception) {
                showToast("خطأ في تصدير التقرير: ${e.message}")
            }
        }
    }

    private fun shareReport(report: String) {
        val shareIntent = android.content.Intent().apply {
            action = android.content.Intent.ACTION_SEND
            type = "text/plain"
            putExtra(android.content.Intent.EXTRA_TEXT, report)
            putExtra(android.content.Intent.EXTRA_SUBJECT, "تقرير الأداء - كهربائيات عامر")
        }
        startActivity(android.content.Intent.createChooser(shareIntent, "مشاركة تقرير الأداء"))
    }

    private fun viewCrashReports() {
        try {
            val crashReports = crashReporter.getAllCrashReports()

            if (crashReports.isEmpty()) {
                androidx.appcompat.app.AlertDialog.Builder(this)
                    .setTitle("تقارير الأخطاء")
                    .setMessage("🎉 ممتاز! لا توجد أخطاء مسجلة في التطبيق.\n\nهذا يعني أن التطبيق يعمل بشكل مستقر وآمن.")
                    .setPositiveButton("موافق", null)
                    .show()
                return
            }

            val reportsList = crashReports.mapIndexed { index, report ->
                "${index + 1}. ${report.formattedTime}: ${report.exceptionType}"
            }.toTypedArray()

            androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("🐛 تقارير الأخطاء (${crashReports.size})")
                .setMessage("اضغط على أي تقرير لعرض التفاصيل:")
                .setItems(reportsList) { _, which ->
                    showCrashReportDetails(crashReports[which])
                }
                .setNeutralButton("🗑️ مسح الكل") { _, _ ->
                    showClearAllConfirmation()
                }
                .setNegativeButton("إغلاق", null)
                .show()
        } catch (e: Exception) {
            showToast("خطأ في تحميل تقارير الأخطاء: ${e.message}")
        }
    }

    private fun showClearAllConfirmation() {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("تأكيد المسح")
            .setMessage("هل أنت متأكد من مسح جميع تقارير الأخطاء؟\n\nهذا الإجراء لا يمكن التراجع عنه.")
            .setPositiveButton("نعم، امسح الكل") { _, _ ->
                try {
                    crashReporter.clearAllReports()
                    showToast("✅ تم مسح جميع تقارير الأخطاء")
                    refreshData()
                } catch (e: Exception) {
                    showToast("خطأ في مسح التقارير: ${e.message}")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showCrashReportDetails(crashReport: CrashReporter.CrashReport) {
        val details = buildString {
            appendLine("🕒 الوقت: ${crashReport.formattedTime}")
            appendLine("🐛 نوع الخطأ: ${crashReport.exceptionType}")
            appendLine("💬 الرسالة: ${crashReport.exceptionMessage}")
            appendLine("🧵 الخيط: ${crashReport.threadName}")
            appendLine("📱 الجهاز: ${crashReport.deviceInfo.manufacturer} ${crashReport.deviceInfo.model}")
            appendLine("🤖 Android: ${crashReport.deviceInfo.androidVersion}")
            appendLine("📦 إصدار التطبيق: ${crashReport.appInfo.versionName}")
            appendLine()
            appendLine("📋 معلومات إضافية:")
            appendLine("• معرف الخطأ: ${crashReport.hashCode()}")
            appendLine("• تاريخ الحدوث: ${java.text.SimpleDateFormat("EEEE", java.util.Locale("ar")).format(java.util.Date(crashReport.timestamp))}")
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("🔍 تفاصيل تقرير الخطأ")
            .setMessage(details)
            .setPositiveButton("موافق", null)
            .setNeutralButton("📤 مشاركة") { _, _ ->
                shareErrorReport(crashReport, details)
            }
            .show()
    }

    private fun shareErrorReport(crashReport: CrashReporter.CrashReport, details: String) {
        val shareIntent = android.content.Intent().apply {
            action = android.content.Intent.ACTION_SEND
            type = "text/plain"
            putExtra(android.content.Intent.EXTRA_TEXT, details)
            putExtra(android.content.Intent.EXTRA_SUBJECT, "تقرير خطأ - كهربائيات عامر")
        }
        startActivity(android.content.Intent.createChooser(shareIntent, "مشاركة تقرير الخطأ"))
    }

    private fun refreshData() {
        lifecycleScope.launch {
            try {
                // تعطيل الزر مؤقتاً
                binding.btnRefreshData.isEnabled = false
                binding.btnRefreshData.text = "🔄 جاري التحديث..."

                // تحديث البيانات
                updatePerformanceData()

                // انتظار قصير للتأثير البصري
                delay(1000)

                showToast("✅ تم تحديث جميع البيانات بنجاح")

            } catch (e: Exception) {
                showToast("❌ خطأ في تحديث البيانات: ${e.message}")
            } finally {
                // إعادة تفعيل الزر
                binding.btnRefreshData.isEnabled = true
                binding.btnRefreshData.text = "🔄 تحديث البيانات"
            }
        }
    }

    private fun formatBytes(bytes: Long): String {
        return when {
            bytes >= 1024 * 1024 * 1024 -> "${"%.1f".format(bytes / (1024.0 * 1024.0 * 1024.0))} GB"
            bytes >= 1024 * 1024 -> "${"%.1f".format(bytes / (1024.0 * 1024.0))} MB"
            bytes >= 1024 -> "${"%.1f".format(bytes / 1024.0)} KB"
            else -> "$bytes B"
        }
    }

    private fun getNetworkTypeText(type: NetworkManager.NetworkType): String {
        return when (type) {
            NetworkManager.NetworkType.WIFI -> "واي فاي"
            NetworkManager.NetworkType.MOBILE -> "بيانات الجوال"
            NetworkManager.NetworkType.ETHERNET -> "إيثرنت"
            NetworkManager.NetworkType.UNKNOWN -> "غير معروف"
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
