<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".OrdersActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white"
            app:subtitleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Filter Chips -->
            <HorizontalScrollView
                android:id="@+id/scrollViewFilters"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:scrollbars="none"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chipGroupFilters"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="8dp"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipAll"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="الكل (0)"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/primary_light" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipPending"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلقة (0)"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/warning_yellow" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipConfirmed"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="مؤكدة (0)"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/primary_light" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipShipped"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="مشحونة (0)"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/accent_teal" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipDelivered"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="مسلمة (0)"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/success_green" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipCancelled"
                        style="@style/Widget.Material3.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="ملغية (0)"
                        android:textColor="@color/text_primary"
                        app:chipBackgroundColor="@color/error_red" />

                </com.google.android.material.chip.ChipGroup>

            </HorizontalScrollView>

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scrollViewFilters" />

            <!-- Orders RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvOrders"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:clipToPadding="false"
                android:padding="8dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scrollViewFilters"
                tools:listitem="@layout/item_order" />

            <!-- Empty State -->
            <TextView
                android:id="@+id/tvEmptyState"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableTop="@drawable/ic_receipt"
                android:drawablePadding="16dp"
                android:gravity="center"
                android:text="لا توجد طلبات حتى الآن"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/scrollViewFilters" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
