package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class User(
    val id: String = "",
    val name: String = "",
    val email: String = "",
    val phone: String = "",
    val role: UserRole = UserRole.EMPLOYEE,
    val permissions: List<Permission> = emptyList(),
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val lastLoginAt: Long = 0,
    val profileImageUrl: String = ""
) : Parcelable

enum class UserRole(val displayName: String) {
    ADMIN("مدير عام"),
    MANAGER("مدير"),
    EMPLOYEE("موظف"),
    VIEWER("مشاهد")
}

enum class Permission(val displayName: String, val description: String) {
    // Product permissions
    VIEW_PRODUCTS("عرض المنتجات", "يمكن عرض قائمة المنتجات"),
    ADD_PRODUCTS("إضافة المنتجات", "يمكن إضافة منتجات جديدة"),
    EDIT_PRODUCTS("تعديل المنتجات", "يمكن تعديل بيانات المنتجات"),
    DELETE_PRODUCTS("حذف المنتجات", "يمكن حذف المنتجات"),
    
    // Order permissions
    VIEW_ORDERS("عرض الطلبات", "يمكن عرض قائمة الطلبات"),
    CREATE_ORDERS("إنشاء الطلبات", "يمكن إنشاء طلبات جديدة"),
    EDIT_ORDERS("تعديل الطلبات", "يمكن تعديل بيانات الطلبات"),
    DELETE_ORDERS("حذف الطلبات", "يمكن حذف الطلبات"),
    CHANGE_ORDER_STATUS("تغيير حالة الطلبات", "يمكن تغيير حالة الطلبات"),
    
    // Analytics permissions
    VIEW_ANALYTICS("عرض التحليلات", "يمكن عرض التحليلات والإحصائيات"),
    VIEW_REPORTS("عرض التقارير", "يمكن عرض التقارير المختلفة"),
    EXPORT_DATA("تصدير البيانات", "يمكن تصدير البيانات"),
    
    // User management permissions
    VIEW_USERS("عرض المستخدمين", "يمكن عرض قائمة المستخدمين"),
    ADD_USERS("إضافة المستخدمين", "يمكن إضافة مستخدمين جدد"),
    EDIT_USERS("تعديل المستخدمين", "يمكن تعديل بيانات المستخدمين"),
    DELETE_USERS("حذف المستخدمين", "يمكن حذف المستخدمين"),
    MANAGE_PERMISSIONS("إدارة الصلاحيات", "يمكن تعديل صلاحيات المستخدمين"),
    
    // System permissions
    MANAGE_SETTINGS("إدارة الإعدادات", "يمكن تعديل إعدادات النظام"),
    VIEW_LOGS("عرض السجلات", "يمكن عرض سجلات النظام"),
    BACKUP_DATA("نسخ احتياطي", "يمكن إنشاء نسخ احتياطية"),
    RESTORE_DATA("استعادة البيانات", "يمكن استعادة البيانات")
}

object UserRolePermissions {
    fun getDefaultPermissions(role: UserRole): List<Permission> {
        return when (role) {
            UserRole.ADMIN -> Permission.values().toList() // All permissions
            
            UserRole.MANAGER -> listOf(
                Permission.VIEW_PRODUCTS,
                Permission.ADD_PRODUCTS,
                Permission.EDIT_PRODUCTS,
                Permission.VIEW_ORDERS,
                Permission.CREATE_ORDERS,
                Permission.EDIT_ORDERS,
                Permission.CHANGE_ORDER_STATUS,
                Permission.VIEW_ANALYTICS,
                Permission.VIEW_REPORTS,
                Permission.EXPORT_DATA,
                Permission.VIEW_USERS
            )
            
            UserRole.EMPLOYEE -> listOf(
                Permission.VIEW_PRODUCTS,
                Permission.ADD_PRODUCTS,
                Permission.EDIT_PRODUCTS,
                Permission.VIEW_ORDERS,
                Permission.CREATE_ORDERS,
                Permission.EDIT_ORDERS,
                Permission.CHANGE_ORDER_STATUS
            )
            
            UserRole.VIEWER -> listOf(
                Permission.VIEW_PRODUCTS,
                Permission.VIEW_ORDERS,
                Permission.VIEW_ANALYTICS,
                Permission.VIEW_REPORTS
            )
        }
    }
}
