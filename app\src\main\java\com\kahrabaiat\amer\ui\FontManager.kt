package com.kahrabaiat.amer.ui

import android.content.Context
import android.graphics.Typeface
import android.widget.TextView
import androidx.core.content.res.ResourcesCompat
import com.kahrabaiat.amer.R

object FontManager {
    
    private var cairoBold: Typeface? = null
    private var cairoRegular: Typeface? = null
    private var cairoLight: Typeface? = null
    
    /**
     * تهيئة الخطوط
     */
    fun initialize(context: Context) {
        try {
            cairoBold = ResourcesCompat.getFont(context, R.font.cairo_bold)
            cairoRegular = ResourcesCompat.getFont(context, R.font.cairo_regular)
            cairoLight = ResourcesCompat.getFont(context, R.font.cairo_light)
        } catch (e: Exception) {
            // استخدام الخطوط الافتراضية في حالة الخطأ
            cairoBold = Typeface.DEFAULT_BOLD
            cairoRegular = Typeface.DEFAULT
            cairoLight = Typeface.DEFAULT
        }
    }
    
    /**
     * تطبيق خط Cairo Bold
     */
    fun applyBoldFont(textView: TextView) {
        cairoBold?.let { textView.typeface = it }
    }
    
    /**
     * تطبيق خط Cairo Regular
     */
    fun applyRegularFont(textView: TextView) {
        cairoRegular?.let { textView.typeface = it }
    }
    
    /**
     * تطبيق خط Cairo Light
     */
    fun applyLightFont(textView: TextView) {
        cairoLight?.let { textView.typeface = it }
    }
    
    /**
     * تطبيق خط حسب النوع
     */
    fun applyFont(textView: TextView, fontType: FontType) {
        when (fontType) {
            FontType.BOLD -> applyBoldFont(textView)
            FontType.REGULAR -> applyRegularFont(textView)
            FontType.LIGHT -> applyLightFont(textView)
        }
    }
    
    /**
     * الحصول على خط حسب النوع
     */
    fun getFont(fontType: FontType): Typeface? {
        return when (fontType) {
            FontType.BOLD -> cairoBold
            FontType.REGULAR -> cairoRegular
            FontType.LIGHT -> cairoLight
        }
    }
    
    enum class FontType {
        BOLD, REGULAR, LIGHT
    }
}
