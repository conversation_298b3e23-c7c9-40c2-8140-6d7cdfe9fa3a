package com.kahrabaiat.amer

import android.app.Application
import android.util.Log
import com.kahrabaiat.amer.cache.CacheManager
import com.kahrabaiat.amer.network.NetworkManager
import com.kahrabaiat.amer.performance.CrashReporter
import com.kahrabaiat.amer.performance.PerformanceMonitor
import com.kahrabaiat.amer.ui.FontManager
import com.kahrabaiat.amer.ui.PerformanceOptimizer

class KahrabaiatAmerApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        Log.d("KahrabaiatAmerApp", "Application starting...")

        // تهيئة نظام تقارير الأخطاء أولاً
        initializeCrashReporter()

        // تهيئة الخطوط
        FontManager.initialize(this)

        // تهيئة أنظمة الأداء
        initializePerformanceSystems()

        // تحسين الأداء العام
        optimizePerformance()

        // تهيئة مكونات أخرى
        initializeComponents()

        Log.d("KahrabaiatAmerApp", "Application initialized successfully")
    }

    private fun optimizePerformance() {
        // مراقبة استهلاك الذاكرة
        val memoryInfo = PerformanceOptimizer.monitorMemoryUsage(this)

        if (memoryInfo.isLowMemory) {
            // تحسينات للأجهزة ذات الذاكرة المنخفضة
            PerformanceOptimizer.optimizeImageMemory(this)
        }
    }

    private fun initializeComponents() {
        // تهيئة مكونات التطبيق الأساسية
        // يمكن إضافة المزيد هنا حسب الحاجة
    }

    override fun onLowMemory() {
        super.onLowMemory()
        // تنظيف الذاكرة عند انخفاضها
        PerformanceOptimizer.optimizeImageMemory(this)
    }

    /**
     * تهيئة نظام تقارير الأخطاء
     */
    private fun initializeCrashReporter() {
        try {
            val crashReporter = CrashReporter.getInstance(this)
            crashReporter.initialize()

            // إضافة مستمع للأخطاء
            crashReporter.addCrashListener(object : CrashReporter.CrashListener {
                override fun onCrashDetected(crashReport: CrashReporter.CrashReport) {
                    Log.e("KahrabaiatAmerApp", "Crash detected: ${crashReport.exceptionType}")
                    // يمكن إضافة معالجة إضافية هنا
                }
            })

            Log.d("KahrabaiatAmerApp", "Crash reporter initialized")
        } catch (e: Exception) {
            Log.e("KahrabaiatAmerApp", "Error initializing crash reporter", e)
        }
    }

    /**
     * تهيئة أنظمة الأداء
     */
    private fun initializePerformanceSystems() {
        try {
            // تهيئة مراقب الأداء
            val performanceMonitor = PerformanceMonitor.getInstance(this)
            performanceMonitor.addPerformanceListener(object : PerformanceMonitor.PerformanceListener {
                override fun onPerformanceIssuesDetected(
                    metrics: PerformanceMonitor.PerformanceMetrics,
                    issues: List<PerformanceMonitor.PerformanceIssue>
                ) {
                    Log.w("KahrabaiatAmerApp", "Performance issues detected: $issues")

                    // تحسين تلقائي عند اكتشاف مشاكل
                    performanceMonitor.optimizePerformance()
                }
            })
            performanceMonitor.startMonitoring()

            // تهيئة مدير الشبكة
            val networkManager = NetworkManager.getInstance(this)
            networkManager.addNetworkListener(object : NetworkManager.NetworkListener {
                override fun onNetworkStateChanged(
                    oldState: NetworkManager.NetworkState,
                    newState: NetworkManager.NetworkState
                ) {
                    Log.d("KahrabaiatAmerApp", "Network state changed: $oldState -> $newState")

                    if (newState == NetworkManager.NetworkState.CONNECTED) {
                        // معالجة الطلبات المؤجلة
                        Log.d("KahrabaiatAmerApp", "Processing queued network requests")
                    }
                }

                override fun onNetworkQualityChanged(
                    quality: NetworkManager.NetworkQuality,
                    latency: Long
                ) {
                    Log.d("KahrabaiatAmerApp", "Network quality: $quality (${latency}ms)")
                }
            })
            networkManager.startMonitoring()

            // تهيئة مدير التخزين المؤقت
            val cacheManager = CacheManager.getInstance(this)
            cacheManager.optimizeCache()

            Log.d("KahrabaiatAmerApp", "Performance systems initialized")
        } catch (e: Exception) {
            Log.e("KahrabaiatAmerApp", "Error initializing performance systems", e)
        }
    }

    override fun onTrimMemory(level: Int) {
        super.onTrimMemory(level)

        when (level) {
            TRIM_MEMORY_RUNNING_MODERATE,
            TRIM_MEMORY_RUNNING_LOW,
            TRIM_MEMORY_RUNNING_CRITICAL -> {
                // تنظيف الذاكرة تدريجياً
                PerformanceOptimizer.optimizeImageMemory(this)

                // تحسين التخزين المؤقت
                val cacheManager = CacheManager.getInstance(this)
                cacheManager.optimizeCache()

                // تحسين الأداء
                val performanceMonitor = PerformanceMonitor.getInstance(this)
                performanceMonitor.performMemoryCleanup()

                Log.d("KahrabaiatAmerApp", "Memory optimization performed due to trim level: $level")
            }
        }
    }

    override fun onTerminate() {
        super.onTerminate()

        try {
            // إيقاف أنظمة المراقبة
            PerformanceMonitor.getInstance(this).stopMonitoring()
            NetworkManager.getInstance(this).stopMonitoring()

            Log.d("KahrabaiatAmerApp", "Application terminated")
        } catch (e: Exception) {
            Log.e("KahrabaiatAmerApp", "Error during application termination", e)
        }
    }
}
