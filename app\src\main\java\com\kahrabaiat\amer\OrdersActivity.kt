package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.OrdersAdapter
import com.kahrabaiat.amer.databinding.ActivityOrdersBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import com.kahrabaiat.amer.database.DatabaseHelper
import kotlinx.coroutines.launch

class OrdersActivity : AppCompatActivity() {

    private lateinit var binding: ActivityOrdersBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var ordersAdapter: OrdersAdapter
    private var allOrders = mutableListOf<Order>()
    private var filteredOrders = mutableListOf<Order>()
    private var currentFilter = "ALL"
    private var currentSearchQuery = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityOrdersBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupRecyclerView()
            setupClickListeners()
            setupFilterChips()
            loadOrders()
        } catch (e: Exception) {
            android.util.Log.e("OrdersActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة الطلبات: ${e.message}")
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        loadOrders()
    }

    private fun initializeComponents() {
        databaseHelper = DatabaseHelper.getInstance(this)
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "إدارة الطلبات"
            }
        } catch (e: Exception) {
            android.util.Log.e("OrdersActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        try {
            ordersAdapter = OrdersAdapter(
                onOrderClick = { order -> showOrderDetails(order) },
                onStatusChange = { order, newStatus -> updateOrderStatus(order, newStatus) },
                onDeleteOrder = { order -> showDeleteConfirmation(order) }
            )

            binding.rvOrders.apply {
                layoutManager = LinearLayoutManager(this@OrdersActivity)
                adapter = ordersAdapter
            }
        } catch (e: Exception) {
            android.util.Log.e("OrdersActivity", "Error setting up RecyclerView", e)
            showToast("خطأ في إعداد قائمة الطلبات")
        }
    }

    private fun setupClickListeners() {
        binding.swipeRefresh.setOnRefreshListener {
            loadOrders()
        }
    }

    private fun setupFilterChips() {
        binding.chipAll.setOnClickListener { filterOrders("ALL") }
        binding.chipPending.setOnClickListener { filterOrders("PENDING") }
        binding.chipConfirmed.setOnClickListener { filterOrders("CONFIRMED") }
        binding.chipShipped.setOnClickListener { filterOrders("SHIPPED") }
        binding.chipDelivered.setOnClickListener { filterOrders("DELIVERED") }
        binding.chipCancelled.setOnClickListener { filterOrders("CANCELLED") }
    }

    private fun loadOrders() {
        lifecycleScope.launch {
            try {
                binding.swipeRefresh.isRefreshing = true
                binding.progressBar.visibility = View.VISIBLE

                val orders = databaseHelper.getAllOrders()
                allOrders.clear()
                allOrders.addAll(orders)

                filterOrders(currentFilter)
                updateStatistics()

            } catch (e: Exception) {
                android.util.Log.e("OrdersActivity", "Error loading orders", e)
                showToast("خطأ في تحميل الطلبات: ${e.message}")
            } finally {
                binding.swipeRefresh.isRefreshing = false
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun filterOrders(filter: String) {
        currentFilter = filter

        // Update chip selection
        binding.chipAll.isChecked = filter == "ALL"
        binding.chipPending.isChecked = filter == "PENDING"
        binding.chipConfirmed.isChecked = filter == "CONFIRMED"
        binding.chipShipped.isChecked = filter == "SHIPPED"
        binding.chipDelivered.isChecked = filter == "DELIVERED"
        binding.chipCancelled.isChecked = filter == "CANCELLED"

        applyFilters()
    }

    private fun applyFilters() {
        filteredOrders.clear()

        var orders = allOrders.toList()

        // Apply search filter
        if (currentSearchQuery.isNotEmpty()) {
            orders = orders.filter { order ->
                order.id.toString().contains(currentSearchQuery, ignoreCase = true) ||
                order.customerName.contains(currentSearchQuery, ignoreCase = true) ||
                order.customerPhone.contains(currentSearchQuery, ignoreCase = true) ||
                order.customerAddress.contains(currentSearchQuery, ignoreCase = true)
            }
        }

        // Apply status filter
        when (currentFilter) {
            "ALL" -> filteredOrders.addAll(orders)
            "PENDING" -> filteredOrders.addAll(orders.filter { it.status == "pending" })
            "CONFIRMED" -> filteredOrders.addAll(orders.filter { it.status == "confirmed" })
            "PROCESSING" -> filteredOrders.addAll(orders.filter { it.status == "processing" })
            "SHIPPED" -> filteredOrders.addAll(orders.filter { it.status == "shipped" })
            "DELIVERED" -> filteredOrders.addAll(orders.filter { it.status == "delivered" })
            "CANCELLED" -> filteredOrders.addAll(orders.filter { it.status == "cancelled" })
        }

        updateUI()
    }

    private fun updateUI() {
        if (filteredOrders.isEmpty()) {
            binding.rvOrders.visibility = View.GONE
            binding.tvEmptyState.visibility = View.VISIBLE
            binding.tvEmptyState.text = if (currentSearchQuery.isNotEmpty()) {
                "لم يتم العثور على طلبات مطابقة\nجرب تغيير معايير البحث"
            } else {
                when (currentFilter) {
                    "ALL" -> "لا توجد طلبات حتى الآن"
                    "PENDING" -> "لا توجد طلبات معلقة"
                    "CONFIRMED" -> "لا توجد طلبات مؤكدة"
                    "PROCESSING" -> "لا توجد طلبات قيد المعالجة"
                    "SHIPPED" -> "لا توجد طلبات مشحونة"
                    "DELIVERED" -> "لا توجد طلبات مسلمة"
                    "CANCELLED" -> "لا توجد طلبات ملغية"
                    else -> "لا توجد طلبات"
                }
            }
        } else {
            binding.rvOrders.visibility = View.VISIBLE
            binding.tvEmptyState.visibility = View.GONE
            ordersAdapter.updateOrders(filteredOrders)
        }

        // Update toolbar subtitle with count
        val filterText = when (currentFilter) {
            "ALL" -> "جميع الطلبات"
            "PENDING" -> "الطلبات المعلقة"
            "CONFIRMED" -> "الطلبات المؤكدة"
            "PROCESSING" -> "الطلبات قيد المعالجة"
            "SHIPPED" -> "الطلبات المشحونة"
            "DELIVERED" -> "الطلبات المسلمة"
            "CANCELLED" -> "الطلبات الملغية"
            else -> "الطلبات"
        }
        val totalText = if (currentSearchQuery.isNotEmpty()) {
            "$filterText (${filteredOrders.size} من ${allOrders.size})"
        } else {
            "$filterText (${filteredOrders.size})"
        }
        supportActionBar?.subtitle = totalText
    }

    private fun updateStatistics() {
        val pendingCount = allOrders.count { it.status == "pending" }
        val confirmedCount = allOrders.count { it.status == "confirmed" }
        val processingCount = allOrders.count { it.status == "processing" }
        val shippedCount = allOrders.count { it.status == "shipped" }
        val deliveredCount = allOrders.count { it.status == "delivered" }
        val cancelledCount = allOrders.count { it.status == "cancelled" }

        // Update chip badges
        binding.chipPending.text = "معلقة ($pendingCount)"
        binding.chipConfirmed.text = "مؤكدة ($confirmedCount)"
        binding.chipShipped.text = "مشحونة ($shippedCount)"
        binding.chipDelivered.text = "مسلمة ($deliveredCount)"
        binding.chipCancelled.text = "ملغية ($cancelledCount)"
        binding.chipAll.text = "الكل (${allOrders.size})"
    }

    private fun showOrderDetails(order: Order) {
        val intent = Intent(this, OrderDetailsActivity::class.java)
        intent.putExtra(OrderDetailsActivity.EXTRA_ORDER, order)
        startActivity(intent)
    }

    private fun updateOrderStatus(order: Order, newStatus: String) {
        lifecycleScope.launch {
            try {
                val success = databaseHelper.updateOrderStatus(order.id, newStatus)
                if (success) {
                    showToast("تم تحديث حالة الطلب بنجاح")
                    loadOrders() // Refresh the list
                } else {
                    showToast("فشل في تحديث حالة الطلب")
                }
            } catch (e: Exception) {
                android.util.Log.e("OrdersActivity", "Error updating order status", e)
                showToast("خطأ في تحديث حالة الطلب: ${e.message}")
            }
        }
    }

    private fun showDeleteConfirmation(order: Order) {
        AlertDialog.Builder(this)
            .setTitle("حذف الطلب")
            .setMessage("هل أنت متأكد من حذف الطلب رقم ${order.id}؟\nلا يمكن التراجع عن هذا الإجراء.")
            .setPositiveButton("حذف") { _, _ ->
                deleteOrder(order)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun deleteOrder(order: Order) {
        lifecycleScope.launch {
            try {
                val success = databaseHelper.deleteOrder(order.id)
                if (success) {
                    showToast("تم حذف الطلب بنجاح")
                    loadOrders() // Refresh the list
                } else {
                    showToast("فشل في حذف الطلب")
                }
            } catch (e: Exception) {
                android.util.Log.e("OrdersActivity", "Error deleting order", e)
                showToast("خطأ في حذف الطلب: ${e.message}")
            }
        }
    }

    private fun getStatusDisplayName(status: String): String {
        return when (status) {
            "pending" -> "معلق"
            "confirmed" -> "مؤكد"
            "processing" -> "قيد المعالجة"
            "shipped" -> "مشحون"
            "delivered" -> "مسلم"
            "cancelled" -> "ملغي"
            else -> "غير محدد"
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.orders_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_search -> {
                showSearchDialog()
                true
            }
            R.id.action_export -> {
                val intent = Intent(this, ExportActivity::class.java)
                startActivity(intent)
                true
            }
            R.id.action_statistics -> {
                val intent = Intent(this, StatisticsActivity::class.java)
                startActivity(intent)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showSearchDialog() {
        val editText = android.widget.EditText(this)
        editText.setText(currentSearchQuery)
        editText.hint = "ابحث في الطلبات..."
        editText.layoutDirection = android.view.View.LAYOUT_DIRECTION_RTL

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("بحث في الطلبات")
            .setMessage("يمكنك البحث في رقم الطلب، اسم العميل، الهاتف، أو العنوان")
            .setView(editText)
            .setPositiveButton("بحث") { _, _ ->
                currentSearchQuery = editText.text.toString().trim()
                applyFilters()
            }
            .setNegativeButton("مسح") { _, _ ->
                currentSearchQuery = ""
                applyFilters()
            }
            .setNeutralButton("إلغاء", null)
            .show()
    }
}
