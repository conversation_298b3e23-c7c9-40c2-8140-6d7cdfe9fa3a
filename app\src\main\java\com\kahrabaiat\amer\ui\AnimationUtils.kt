package com.kahrabaiat.amer.ui

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.OvershootInterpolator
import androidx.interpolator.view.animation.FastOutSlowInInterpolator

object AnimationUtils {

    /**
     * انيميشن ظهور الكرت
     */
    fun animateCardEntry(view: View, delay: Long = 0) {
        view.alpha = 0f
        view.translationY = 100f
        view.scaleX = 0.8f
        view.scaleY = 0.8f

        val animatorSet = AnimatorSet()

        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)
        val slideUp = ObjectAnimator.ofFloat(view, "translationY", 100f, 0f)
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0.8f, 1f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0.8f, 1f)

        animatorSet.apply {
            playTogether(fadeIn, slideUp, scaleX, scaleY)
            duration = 600
            startDelay = delay
            interpolator = OvershootInterpolator(1.2f)
            start()
        }
    }

    /**
     * انيميشن نقر الكرت
     */
    fun animateCardClick(view: View, onComplete: (() -> Unit)? = null) {
        val scaleDown = AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(view, "scaleX", 1f, 0.95f),
                ObjectAnimator.ofFloat(view, "scaleY", 1f, 0.95f)
            )
            duration = 100
            interpolator = AccelerateDecelerateInterpolator()
        }

        val scaleUp = AnimatorSet().apply {
            playTogether(
                ObjectAnimator.ofFloat(view, "scaleX", 0.95f, 1f),
                ObjectAnimator.ofFloat(view, "scaleY", 0.95f, 1f)
            )
            duration = 100
            interpolator = OvershootInterpolator(1.1f)
        }

        scaleDown.addListener(object : android.animation.AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: android.animation.Animator) {
                scaleUp.start()
                onComplete?.invoke()
            }
        })

        scaleDown.start()
    }

    /**
     * انيميشن تحميل shimmer
     */
    fun animateShimmer(view: View) {
        val shimmer = ObjectAnimator.ofFloat(view, "alpha", 0.3f, 1f, 0.3f)
        shimmer.apply {
            duration = 1500
            repeatCount = ObjectAnimator.INFINITE
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }

    /**
     * انيميشن ظهور من الأسفل
     */
    fun slideUpFromBottom(view: View, delay: Long = 0) {
        view.translationY = view.height.toFloat()
        view.alpha = 0f

        val slideUp = ObjectAnimator.ofFloat(view, "translationY", view.height.toFloat(), 0f)
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)

        AnimatorSet().apply {
            playTogether(slideUp, fadeIn)
            duration = 500
            startDelay = delay
            interpolator = FastOutSlowInInterpolator()
            start()
        }
    }

    /**
     * انيميشن ظهور من اليمين (للعربية)
     */
    fun slideInFromRight(view: View, delay: Long = 0) {
        view.translationX = view.width.toFloat()
        view.alpha = 0f

        val slideIn = ObjectAnimator.ofFloat(view, "translationX", view.width.toFloat(), 0f)
        val fadeIn = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f)

        AnimatorSet().apply {
            playTogether(slideIn, fadeIn)
            duration = 400
            startDelay = delay
            interpolator = FastOutSlowInInterpolator()
            start()
        }
    }

    /**
     * انيميشن نبضة للتنبيهات
     */
    fun pulseBadge(view: View) {
        val pulse = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.2f, 1f)
        val pulseY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.2f, 1f)

        AnimatorSet().apply {
            playTogether(pulse, pulseY)
            duration = 600
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }

    /**
     * انيميشن هز للأخطاء
     */
    fun shakeError(view: View) {
        val shake = ObjectAnimator.ofFloat(view, "translationX", 0f, 25f, -25f, 25f, -25f, 15f, -15f, 6f, -6f, 0f)
        shake.apply {
            duration = 600
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }

    /**
     * انيميشن نجاح
     */
    fun successBounce(view: View) {
        val bounceX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.1f, 0.9f, 1.05f, 1f)
        val bounceY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.1f, 0.9f, 1.05f, 1f)

        AnimatorSet().apply {
            playTogether(bounceX, bounceY)
            duration = 800
            interpolator = OvershootInterpolator(1.5f)
            start()
        }
    }

    /**
     * انيميشن دوران للتحديث
     */
    fun rotateRefresh(view: View) {
        val rotate = ObjectAnimator.ofFloat(view, "rotation", 0f, 360f)
        rotate.apply {
            duration = 1000
            interpolator = AccelerateDecelerateInterpolator()
            start()
        }
    }
}
