package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.CartAdapter
import com.kahrabaiat.amer.databinding.ActivityCartBinding
import com.kahrabaiat.amer.models.CartItem
import com.kahrabaiat.amer.models.Coupon
import com.kahrabaiat.amer.models.CouponApplication
import com.kahrabaiat.amer.utils.CartManager
import com.kahrabaiat.amer.utils.CouponManager
import androidx.lifecycle.lifecycleScope
import kotlinx.coroutines.launch

class CartActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCartBinding
    private lateinit var cartManager: CartManager
    private lateinit var couponManager: CouponManager
    private lateinit var cartAdapter: CartAdapter
    private var appliedCoupon: CouponApplication? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCartBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        updateUI()
    }

    override fun onResume() {
        super.onResume()
        updateUI()
    }

    private fun initializeComponents() {
        cartManager = CartManager.getInstance(this)
        couponManager = CouponManager.getInstance(this)
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        cartAdapter = CartAdapter(
            onQuantityChanged = { productId, newQuantity ->
                cartManager.updateQuantity(productId, newQuantity)
                // Update UI immediately
                updateCartSummary()
                // Update MainActivity cart badge
                setResult(RESULT_OK)
            },
            onRemoveItem = { productId ->
                cartManager.removeFromCart(productId)
                updateUI()
                showRemoveMessage()
                // Update MainActivity cart badge
                setResult(RESULT_OK)
            }
        )

        binding.rvCartItems.apply {
            layoutManager = LinearLayoutManager(this@CartActivity)
            adapter = cartAdapter
        }
    }

    private fun setupClickListeners() {
        binding.btnCheckout.setOnClickListener {
            val intent = Intent(this, CheckoutActivity::class.java)
            startActivity(intent)
        }

        binding.btnStartShopping.setOnClickListener {
            finish()
        }

        binding.btnApplyCoupon.setOnClickListener {
            applyCoupon()
        }

        binding.btnRemoveCoupon.setOnClickListener {
            removeCoupon()
        }

        binding.btnViewCoupons.setOnClickListener {
            val intent = Intent(this, CouponsActivity::class.java)
            startActivity(intent)
        }
    }

    private fun updateUI() {
        val cartItems = cartManager.getCartItems()

        if (cartItems.isEmpty()) {
            showEmptyCart()
        } else {
            showCartItems(cartItems)
        }
    }

    private fun showEmptyCart() {
        binding.layoutEmptyCart.visibility = View.VISIBLE
        binding.rvCartItems.visibility = View.GONE
        binding.layoutCartSummary.visibility = View.GONE
    }

    private fun showCartItems(cartItems: List<CartItem>) {
        binding.layoutEmptyCart.visibility = View.GONE
        binding.rvCartItems.visibility = View.VISIBLE
        binding.layoutCartSummary.visibility = View.VISIBLE

        cartAdapter.submitList(cartItems)
        updateCartSummary()
    }

    private fun updateCartSummary() {
        val subtotal = cartManager.getTotalAmount()
        val discountAmount = appliedCoupon?.discountAmount ?: 0.0
        val finalTotal = subtotal - discountAmount

        binding.tvSubtotal.text = "${subtotal.toInt()} دينار"

        if (appliedCoupon != null) {
            binding.layoutCouponDiscount.visibility = View.VISIBLE
            binding.tvCouponDiscount.text = "-${discountAmount.toInt()} دينار"
            binding.tvCouponName.text = appliedCoupon?.coupon?.title ?: ""
        } else {
            binding.layoutCouponDiscount.visibility = View.GONE
        }

        binding.tvTotalAmount.text = "${finalTotal.toInt()} دينار"
    }

    private fun applyCoupon() {
        val couponCode = binding.etCouponCode.text.toString().trim()

        if (couponCode.isEmpty()) {
            android.widget.Toast.makeText(this, "أدخل كود الكوبون", android.widget.Toast.LENGTH_SHORT).show()
            return
        }

        lifecycleScope.launch {
            try {
                val cartItems = cartManager.getCartItems()
                val products = cartItems.map { it.product }
                val orderAmount = cartManager.getTotalAmount()

                val result = couponManager.applyCoupon(
                    couponCode = couponCode,
                    orderAmount = orderAmount,
                    cartItems = products,
                    customerPhone = "" // يمكن إضافة رقم العميل لاحقاً
                )

                if (result.isValid) {
                    appliedCoupon = result
                    binding.etCouponCode.setText("")
                    binding.layoutCouponInput.visibility = View.GONE
                    binding.layoutAppliedCoupon.visibility = View.VISIBLE
                    updateCartSummary()

                    android.widget.Toast.makeText(
                        this@CartActivity,
                        result.getSuccessMessage(),
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                } else {
                    android.widget.Toast.makeText(
                        this@CartActivity,
                        result.errorMessage,
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception) {
                android.widget.Toast.makeText(
                    this@CartActivity,
                    "خطأ في تطبيق الكوبون",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun removeCoupon() {
        appliedCoupon = null
        binding.layoutCouponInput.visibility = View.VISIBLE
        binding.layoutAppliedCoupon.visibility = View.GONE
        updateCartSummary()

        android.widget.Toast.makeText(
            this,
            "تم إزالة الكوبون",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    private fun showRemoveMessage() {
        android.widget.Toast.makeText(
            this,
            "تم إزالة المنتج من السلة",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
}
