<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    android:orientation="vertical"
    tools:context=".ProductsActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white"
        app:title="المنتجات"
        app:titleTextColor="@color/white" />

    <!-- Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Products RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvProducts"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            tools:listitem="@layout/item_product" />

        <!-- Loading View -->
        <LinearLayout
            android:id="@+id/layoutLoading"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone">

            <ProgressBar
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/loading"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

        </LinearLayout>

        <!-- Empty View -->
        <LinearLayout
            android:id="@+id/layoutEmpty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="16dp"
                android:alpha="0.5"
                android:src="@drawable/ic_product_placeholder"
                android:tint="@color/medium_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="لا توجد منتجات"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="لم يتم العثور على منتجات في هذا التصنيف"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
