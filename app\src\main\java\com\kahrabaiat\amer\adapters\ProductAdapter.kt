package com.kahrabaiat.amer.adapters

import android.graphics.Paint
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemProductBinding
import com.kahrabaiat.amer.models.Product

class ProductAdapter(
    private val onProductClick: (Product) -> Unit,
    private val onAddToCartClick: (Product) -> Unit
) : ListAdapter<Product, ProductAdapter.ProductViewHolder>(ProductDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val binding = ItemProductBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ProductViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ProductViewHolder(
        private val binding: ItemProductBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(product: Product) {
            binding.apply {
                // Product name
                tvProductName.text = product.name

                // Product image with enhanced loader
                com.kahrabaiat.amer.ui.ImageLoader.loadProductImage(
                    itemView.context,
                    ivProductImage,
                    product.imageUrl.takeIf { it.isNotEmpty() },
                    onLoadComplete = {
                        // انيميشن ظهور الصورة
                        com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(ivProductImage, 0)
                    }
                )

                // Price handling
                if (product.discount > 0) {
                    // Show discounted price
                    tvCurrentPrice.text = product.getFormattedPrice()
                    tvOriginalPrice.text = product.getOriginalFormattedPrice()
                    tvOriginalPrice.visibility = View.VISIBLE
                    tvOriginalPrice.paintFlags = tvOriginalPrice.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG

                    // Show discount badge
                    tvDiscount.text = "خصم ${product.discount}%"
                    tvDiscount.visibility = View.VISIBLE
                } else {
                    // Show regular price
                    tvCurrentPrice.text = product.getFormattedPrice()
                    tvOriginalPrice.visibility = View.GONE
                    tvDiscount.visibility = View.GONE
                }

                // Stock status
                if (product.isInStock()) {
                    tvStockStatus.text = itemView.context.getString(R.string.available)
                    tvStockStatus.setBackgroundResource(R.drawable.stock_badge_background)
                    btnAddToCart.isEnabled = true
                    btnAddToCart.alpha = 1.0f
                } else {
                    tvStockStatus.text = itemView.context.getString(R.string.out_of_stock)
                    tvStockStatus.setBackgroundColor(
                        itemView.context.getColor(R.color.out_of_stock_red)
                    )
                    btnAddToCart.isEnabled = false
                    btnAddToCart.alpha = 0.5f
                }

                // Click listeners with animations
                root.setOnClickListener {
                    com.kahrabaiat.amer.ui.AnimationUtils.animateCardClick(it) {
                        onProductClick(product)
                    }
                }

                btnAddToCart.setOnClickListener {
                    if (product.isInStock()) {
                        com.kahrabaiat.amer.ui.AnimationUtils.successBounce(it)
                        onAddToCartClick(product)
                    } else {
                        com.kahrabaiat.amer.ui.AnimationUtils.shakeError(it)
                    }
                }
            }
        }
    }

    private class ProductDiffCallback : DiffUtil.ItemCallback<Product>() {
        override fun areItemsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem == newItem
        }
    }
}
