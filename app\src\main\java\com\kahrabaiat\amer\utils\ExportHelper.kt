package com.kahrabaiat.amer.utils

import android.content.Context
import android.os.Environment
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.Product
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

class ExportHelper(private val context: Context) {

    companion object {
        private const val EXPORT_FOLDER = "KahrabaiiatAmer_Exports"
    }

    private val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
    private val fileNameDateFormat = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault())

    /**
     * تصدير المنتجات إلى ملف CSV
     */
    fun exportProductsToCSV(products: List<Product>, fileName: String? = null): Result<File> {
        return try {
            val actualFileName = fileName ?: "منتجات_${fileNameDateFormat.format(Date())}"
            val file = createExportFile("$actualFileName.csv")
            
            val csvContent = StringBuilder()
            
            // Headers
            csvContent.append("الرقم,اسم المنتج,السعر,الوصف,الفئة,المخزون,تاريخ الإضافة\n")
            
            // Data
            products.forEach { product ->
                csvContent.append("${product.id},")
                csvContent.append("\"${product.name}\",")
                csvContent.append("${product.price},")
                csvContent.append("\"${product.description}\",")
                csvContent.append("\"${product.category}\",")
                csvContent.append("${product.stock},")
                csvContent.append("\"${dateFormat.format(Date())}\"\n")
            }
            
            file.writeText(csvContent.toString(), Charsets.UTF_8)
            Result.success(file)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تصدير الطلبات إلى ملف CSV
     */
    fun exportOrdersToCSV(orders: List<Order>, fileName: String? = null): Result<File> {
        return try {
            val actualFileName = fileName ?: "طلبات_${fileNameDateFormat.format(Date())}"
            val file = createExportFile("$actualFileName.csv")
            
            val csvContent = StringBuilder()
            
            // Headers
            csvContent.append("رقم الطلب,اسم العميل,رقم الهاتف,العنوان,المجموع,الحالة,تاريخ الطلب,تفاصيل المنتجات\n")
            
            // Data
            orders.forEach { order ->
                csvContent.append("${order.id},")
                csvContent.append("\"${order.customerName}\",")
                csvContent.append("\"${order.customerPhone}\",")
                csvContent.append("\"${order.customerAddress}\",")
                csvContent.append("${order.total},")
                csvContent.append("\"${getStatusDisplayName(order.status)}\",")
                csvContent.append("\"${dateFormat.format(Date(order.orderDate))}\",")
                
                // تفاصيل المنتجات
                val itemsDetails = order.items.joinToString("; ") { item ->
                    "${item.productName} (${item.quantity}x${item.price})"
                }
                csvContent.append("\"$itemsDetails\"\n")
            }
            
            file.writeText(csvContent.toString(), Charsets.UTF_8)
            Result.success(file)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تصدير بيانات العملاء إلى ملف CSV
     */
    fun exportCustomersToCSV(customers: List<Map<String, String>>, fileName: String? = null): Result<File> {
        return try {
            val actualFileName = fileName ?: "عملاء_${fileNameDateFormat.format(Date())}"
            val file = createExportFile("$actualFileName.csv")
            
            val csvContent = StringBuilder()
            
            if (customers.isNotEmpty()) {
                // Headers من أول عنصر
                val headers = customers.first().keys.joinToString(",")
                csvContent.append("$headers\n")
                
                // Data
                customers.forEach { customer ->
                    val values = customer.values.joinToString(",") { "\"$it\"" }
                    csvContent.append("$values\n")
                }
            }
            
            file.writeText(csvContent.toString(), Charsets.UTF_8)
            Result.success(file)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تصدير الإحصائيات إلى ملف CSV
     */
    fun exportStatisticsToCSV(statistics: List<Map<String, String>>, fileName: String? = null): Result<File> {
        return try {
            val actualFileName = fileName ?: "إحصائيات_${fileNameDateFormat.format(Date())}"
            val file = createExportFile("$actualFileName.csv")
            
            val csvContent = StringBuilder()
            
            if (statistics.isNotEmpty()) {
                // Headers
                val headers = statistics.first().keys.joinToString(",")
                csvContent.append("$headers\n")
                
                // Data
                statistics.forEach { stat ->
                    val values = stat.values.joinToString(",") { "\"$it\"" }
                    csvContent.append("$values\n")
                }
            }
            
            file.writeText(csvContent.toString(), Charsets.UTF_8)
            Result.success(file)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تصدير تقرير مخصص للطلبات مع فلترة حسب التاريخ
     */
    fun exportCustomOrderReport(
        orders: List<Order>,
        startDate: Long,
        endDate: Long,
        fileName: String? = null
    ): Result<File> {
        return try {
            val filteredOrders = orders.filter { order ->
                order.orderDate >= startDate && order.orderDate <= endDate
            }
            
            val actualFileName = fileName ?: "تقرير_مخصص_${fileNameDateFormat.format(Date())}"
            val file = createExportFile("$actualFileName.csv")
            
            val csvContent = StringBuilder()
            
            // معلومات التقرير
            csvContent.append("تقرير الطلبات المخصص\n")
            csvContent.append("من تاريخ: ${dateFormat.format(Date(startDate))}\n")
            csvContent.append("إلى تاريخ: ${dateFormat.format(Date(endDate))}\n")
            csvContent.append("عدد الطلبات: ${filteredOrders.size}\n")
            csvContent.append("إجمالي المبيعات: ${filteredOrders.sumOf { it.total }} د.ع\n")
            csvContent.append("\n")
            
            // Headers
            csvContent.append("رقم الطلب,اسم العميل,رقم الهاتف,المجموع,الحالة,تاريخ الطلب\n")
            
            // Data
            filteredOrders.forEach { order ->
                csvContent.append("${order.id},")
                csvContent.append("\"${order.customerName}\",")
                csvContent.append("\"${order.customerPhone}\",")
                csvContent.append("${order.total},")
                csvContent.append("\"${getStatusDisplayName(order.status)}\",")
                csvContent.append("\"${dateFormat.format(Date(order.orderDate))}\"\n")
            }
            
            file.writeText(csvContent.toString(), Charsets.UTF_8)
            Result.success(file)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * تصدير تقرير المنتجات الأكثر مبيعاً
     */
    fun exportTopProductsReport(
        orders: List<Order>,
        products: List<Product>,
        fileName: String? = null
    ): Result<File> {
        return try {
            val actualFileName = fileName ?: "أفضل_المنتجات_${fileNameDateFormat.format(Date())}"
            val file = createExportFile("$actualFileName.csv")
            
            // حساب المبيعات لكل منتج
            val productSales = mutableMapOf<String, Int>()
            val productRevenue = mutableMapOf<String, Double>()
            
            orders.forEach { order ->
                order.items.forEach { item ->
                    val productName = item.productName
                    productSales[productName] = (productSales[productName] ?: 0) + item.quantity
                    productRevenue[productName] = (productRevenue[productName] ?: 0.0) + item.total
                }
            }
            
            val csvContent = StringBuilder()
            
            // Headers
            csvContent.append("اسم المنتج,الكمية المباعة,إجمالي الإيرادات,متوسط السعر\n")
            
            // ترتيب المنتجات حسب الكمية المباعة
            productSales.toList()
                .sortedByDescending { it.second }
                .forEach { (productName, quantity) ->
                    val revenue = productRevenue[productName] ?: 0.0
                    val avgPrice = if (quantity > 0) revenue / quantity else 0.0
                    
                    csvContent.append("\"$productName\",")
                    csvContent.append("$quantity,")
                    csvContent.append("${revenue.toInt()},")
                    csvContent.append("${avgPrice.toInt()}\n")
                }
            
            file.writeText(csvContent.toString(), Charsets.UTF_8)
            Result.success(file)
            
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    /**
     * إنشاء ملف في مجلد التصدير
     */
    private fun createExportFile(fileName: String): File {
        val exportDir = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), EXPORT_FOLDER)
        if (!exportDir.exists()) {
            exportDir.mkdirs()
        }
        return File(exportDir, fileName)
    }

    /**
     * الحصول على اسم الحالة باللغة العربية
     */
    private fun getStatusDisplayName(status: String): String {
        return when (status.lowercase()) {
            "pending" -> "معلق"
            "confirmed" -> "مؤكد"
            "processing" -> "قيد المعالجة"
            "shipped" -> "مشحون"
            "delivered" -> "مسلم"
            "cancelled" -> "ملغي"
            else -> status
        }
    }

    /**
     * الحصول على مجلد التصدير
     */
    fun getExportDirectory(): File {
        return File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), EXPORT_FOLDER)
    }

    /**
     * الحصول على قائمة الملفات المصدرة
     */
    fun getExportedFiles(): List<File> {
        val exportDir = getExportDirectory()
        return if (exportDir.exists()) {
            exportDir.listFiles()?.toList() ?: emptyList()
        } else {
            emptyList()
        }
    }

    /**
     * حذف ملف مصدر
     */
    fun deleteExportedFile(file: File): Boolean {
        return try {
            file.delete()
        } catch (e: Exception) {
            false
        }
    }

    /**
     * حذف جميع الملفات المصدرة
     */
    fun clearAllExports(): Boolean {
        return try {
            val exportDir = getExportDirectory()
            if (exportDir.exists()) {
                exportDir.listFiles()?.forEach { it.delete() }
                true
            } else {
                true
            }
        } catch (e: Exception) {
            false
        }
    }
}
