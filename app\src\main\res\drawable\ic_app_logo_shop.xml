<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">

    <!-- Background Circle -->
    <path
        android:fillColor="#1565C0"
        android:pathData="M24,24m-24,0a24,24 0,1 1,48 0a24,24 0,1 1,-48 0" />

    <!-- Shop Building Base -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M12,32h24v8H12z" />
    
    <!-- Shop Roof -->
    <path
        android:fillColor="#37474F"
        android:pathData="M10,32L24,20L38,32z" />
    
    <!-- Shop Door -->
    <path
        android:fillColor="#1976D2"
        android:pathData="M20,32h8v8h-8z" />
    
    <!-- Door Handle -->
    <path
        android:fillColor="#FFD700"
        android:pathData="M26,36m-1,0a1,1 0,1 1,2 0a1,1 0,1 1,-2 0" />
    
    <!-- Windows -->
    <path
        android:fillColor="#81C784"
        android:pathData="M14,34h4v4h-4z" />
    <path
        android:fillColor="#81C784"
        android:pathData="M30,34h4v4h-4z" />
    
    <!-- Electrical Symbol on Roof -->
    <path
        android:fillColor="#FFD700"
        android:pathData="M26,24L22,28h2l-1,4l3,-4h-2z" />
    
    <!-- Letter A on Door -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M24,33L22,37h1l0.3,-1h1.4l0.3,1h1L24,33z M23.2,35.5L24,34l0.8,1.5H23.2z" />
    
    <!-- Electrical Wires -->
    <path
        android:strokeColor="#37474F"
        android:strokeWidth="1"
        android:pathData="M12,20Q16,18 20,20Q24,22 28,20Q32,18 36,20" />

</vector>
