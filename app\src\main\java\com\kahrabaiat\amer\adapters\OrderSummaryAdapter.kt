package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemOrderSummaryBinding
import com.kahrabaiat.amer.models.CartItem

class OrderSummaryAdapter : ListAdapter<CartItem, OrderSummaryAdapter.OrderSummaryViewHolder>(CartDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderSummaryViewHolder {
        val binding = ItemOrderSummaryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OrderSummaryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderSummaryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class OrderSummaryViewHolder(
        private val binding: ItemOrderSummaryBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(cartItem: CartItem) {
            binding.apply {
                val product = cartItem.product

                // Product info
                tvProductName.text = product.name
                tvQuantityPrice.text = "الكمية: ${cartItem.quantity} × ${product.getFormattedPrice()}"
                tvTotalPrice.text = "${cartItem.getTotalPrice().toInt()} د.ع"

                // Product image
                if (product.imageUrl.isNotEmpty()) {
                    Glide.with(itemView.context)
                        .load(product.imageUrl)
                        .placeholder(R.drawable.ic_product_placeholder)
                        .error(R.drawable.ic_product_placeholder)
                        .into(ivProductImage)
                } else {
                    ivProductImage.setImageResource(R.drawable.ic_product_placeholder)
                }
            }
        }
    }

    private class CartDiffCallback : DiffUtil.ItemCallback<CartItem>() {
        override fun areItemsTheSame(oldItem: CartItem, newItem: CartItem): Boolean {
            return oldItem.product.id == newItem.product.id
        }

        override fun areContentsTheSame(oldItem: CartItem, newItem: CartItem): Boolean {
            return oldItem == newItem
        }
    }
}
