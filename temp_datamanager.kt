package com.kahrabaiat.amer.utils

import android.content.Context
import android.util.Log
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.Product
import java.io.File

/**
 * DataManager - نظام موحد لإدارة البيانات
 * يدعم كلاً من Firebase والقاعدة المحلية
 */
class DataManager private constructor(private val context: Context) {
    
    private val firebaseHelper = FirebaseHelper()
    private val databaseHelper = DatabaseHelper.getInstance(context)
    
    companion object {
        private const val TAG = "DataManager"
        
        @Volatile
        private var INSTANCE: DataManager? = null
        
        fun getInstance(context: Context): DataManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DataManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    // Products operations
    suspend fun getAllProducts(): List<Product> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                Log.d(TAG, "Loading products from Firebase")
                val firebaseProducts = firebaseHelper.getAllProducts()
                
                // Sync with local database
                syncProductsToLocal(firebaseProducts)
                
                firebaseProducts
            } else {
                Log.d(TAG, "Loading products from local database")
                databaseHelper.getAllProducts()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading products, falling back to local", e)
            databaseHelper.getAllProducts()
        }
    }

    suspend fun getLatestProducts(limit: Int): List<Product> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firebaseHelper.getLatestProducts(limit)
            } else {
                databaseHelper.getAllProducts().take(limit)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading latest products", e)
            databaseHelper.getAllProducts().take(limit)
        }
    }

    suspend fun getDiscountedProducts(): List<Product> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firebaseHelper.getDiscountedProducts()
            } else {
                databaseHelper.getAllProducts().filter { it.discount > 0 }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading discounted products", e)
            databaseHelper.getAllProducts().filter { it.discount > 0 }
        }
    }

    suspend fun searchProducts(query: String): List<Product> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firebaseHelper.searchProducts(query)
            } else {
                databaseHelper.getAllProducts().filter { product ->
                    product.name.contains(query, ignoreCase = true) ||
                    product.description.contains(query, ignoreCase = true)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error searching products", e)
            emptyList()
        }
    }

    suspend fun getProductsByCategory(category: String): List<Product> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firebaseHelper.getProductsByCategory(category)
            } else {
                databaseHelper.getAllProducts().filter { it.category == category }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading products by category", e)
            databaseHelper.getAllProducts().filter { it.category == category }
        }
    }

    suspend fun addProduct(product: Product, imageFile: File? = null): Boolean {
        return try {
            var finalProduct = product

            // Handle image save/upload
            if (imageFile != null && AppConfig.USE_REAL_FIREBASE) {
                val imageUrl = firebaseHelper.uploadProductImage(imageFile)
                if (imageUrl != null) {
                    finalProduct = product.copy(imageUrl = imageUrl)
                    Log.d(TAG, "Image uploaded: $imageUrl")
                }
            }

            val success = if (AppConfig.USE_REAL_FIREBASE) {
                val firebaseSuccess = firebaseHelper.addProduct(finalProduct)
                if (firebaseSuccess) {
                    // Also add to local database
                    databaseHelper.insertProduct(finalProduct) > 0
                } else {
                    false
                }
            } else {
                databaseHelper.insertProduct(finalProduct) > 0
            }

            Log.d(TAG, "Product added: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error adding product", e)
            false
        }
    }

    suspend fun updateProduct(product: Product, imageFile: File? = null): Boolean {
        return try {
            var finalProduct = product

            // Upload new image if provided and using Firebase
            if (imageFile != null && AppConfig.USE_REAL_FIREBASE) {
                // Delete old image if exists
                if (product.imageUrl.isNotEmpty()) {
                    firebaseHelper.deleteProductImage(product.imageUrl)
                }

                // Upload new image
                val imageUrl = firebaseHelper.uploadProductImage(imageFile)
                if (imageUrl != null) {
                    finalProduct = product.copy(imageUrl = imageUrl)
                    Log.d(TAG, "New image uploaded: $imageUrl")
                }
            }

            val success = if (AppConfig.USE_REAL_FIREBASE) {
                val firebaseSuccess = firebaseHelper.updateProduct(finalProduct)
                if (firebaseSuccess) {
                    // Also update local database
                    databaseHelper.updateProduct(finalProduct)
                } else {
                    false
                }
            } else {
                databaseHelper.updateProduct(finalProduct)
            }

            Log.d(TAG, "Product updated: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error updating product", e)
            false
        }
    }

    suspend fun deleteProduct(productId: Int): Boolean {
        return try {
            val success = if (AppConfig.USE_REAL_FIREBASE) {
                val firebaseSuccess = firebaseHelper.deleteProduct(productId)
                if (firebaseSuccess) {
                    // Also delete from local database
                    databaseHelper.deleteProduct(productId)
                } else {
                    false
                }
            } else {
                databaseHelper.deleteProduct(productId)
            }
            
            Log.d(TAG, "Product deleted: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting product", e)
            false
        }
    }

    // Orders operations
    suspend fun getAllOrders(): List<Order> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                Log.d(TAG, "Loading orders from Firebase")
                val firebaseOrders = firebaseHelper.getAllOrders()
                
                // Sync with local database
                syncOrdersToLocal(firebaseOrders)
                
                firebaseOrders
            } else {
                Log.d(TAG, "Loading orders from local database")
                databaseHelper.getAllOrders()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error loading orders, falling back to local", e)
            databaseHelper.getAllOrders()
        }
    }

    suspend fun addOrder(order: Order): Boolean {
        return try {
            val success = if (AppConfig.USE_REAL_FIREBASE) {
                val firebaseSuccess = firebaseHelper.addOrder(order)
                if (firebaseSuccess) {
                    // Also add to local database
                    databaseHelper.insertOrder(order) > 0
                } else {
                    false
                }
            } else {
                databaseHelper.insertOrder(order) > 0
            }
            
            Log.d(TAG, "Order added: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error adding order", e)
            false
        }
    }

    suspend fun updateOrderStatus(orderId: Int, status: String): Boolean {
        return try {
            val success = if (AppConfig.USE_REAL_FIREBASE) {
                val firebaseSuccess = firebaseHelper.updateOrderStatus(orderId, status)
                if (firebaseSuccess) {
                    // Also update local database
                    databaseHelper.updateOrderStatus(orderId, status)
                } else {
                    false
                }
            } else {
                databaseHelper.updateOrderStatus(orderId, status)
            }
            
            Log.d(TAG, "Order status updated: $success")
            success
        } catch (e: Exception) {
            Log.e(TAG, "Error updating order status", e)
            false
        }
    }

    // Sync operations
    private suspend fun syncProductsToLocal(products: List<Product>) {
        try {
            products.forEach { product ->
                // Check if product exists locally
                val localProducts = databaseHelper.getAllProducts()
                val existingProduct = localProducts.find { it.id == product.id }
                
                if (existingProduct == null) {
                    databaseHelper.insertProduct(product)
                } else {
                    databaseHelper.updateProduct(product)
                }
            }
            Log.d(TAG, "Products synced to local database")
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing products to local", e)
        }
    }

    private suspend fun syncOrdersToLocal(orders: List<Order>) {
        try {
            orders.forEach { order ->
                // Check if order exists locally
                val localOrders = databaseHelper.getAllOrders()
                val existingOrder = localOrders.find { it.id == order.id }
                
                if (existingOrder == null) {
                    databaseHelper.insertOrder(order)
                }
            }
            Log.d(TAG, "Orders synced to local database")
        } catch (e: Exception) {
            Log.e(TAG, "Error syncing orders to local", e)
        }
    }

    // Initialize data
    suspend fun initializeData(): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firebaseHelper.initializeSampleData()
            } else {
                // Local database already has sample data
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing data", e)
            false
        }
    }

    // Statistics (for admin)
    fun getTotalSales(): Double {
        return databaseHelper.getTotalSales()
    }

    fun getTotalProducts(): Int {
        return databaseHelper.getAllProducts().size
    }

    fun getTotalOrders(): Int {
        return databaseHelper.getAllOrders().size
    }
}
