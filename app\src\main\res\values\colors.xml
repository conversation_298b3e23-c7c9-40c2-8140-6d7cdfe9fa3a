<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Modern Primary Colors - Beautiful Gradient Theme (matching category icons) -->
    <color name="primary_blue">#1976D2</color>
    <color name="primary_blue_dark">#1565C0</color>
    <color name="primary_blue_light">#BBDEFB</color>
    <color name="primary_blue_variant">#E3F2FD</color>

    <!-- Category Theme Colors -->
    <color name="category_tools_primary">#BBDEFB</color>
    <color name="category_tools_secondary">#E3F2FD</color>
    <color name="category_home_primary">#E1BEE7</color>
    <color name="category_home_secondary">#F3E5F5</color>
    <color name="category_electrical_primary">#FFF176</color>
    <color name="category_electrical_secondary">#FFF8E1</color>

    <!-- Secondary Colors - Elegant Grays -->
    <color name="white">#FFFFFF</color>
    <color name="light_gray">#F8FAFC</color>
    <color name="medium_gray">#64748B</color>
    <color name="dark_gray">#334155</color>
    <color name="surface_gray">#F1F5F9</color>

    <!-- Accent Colors - Beautiful Palette -->
    <color name="accent_orange">#FFD700</color>
    <color name="accent_purple">#CE93D8</color>
    <color name="accent_teal">#90CAF9</color>
    <color name="success_green">#81C784</color>
    <color name="error_red">#EF5350</color>
    <color name="warning_yellow">#FFD54F</color>

    <!-- Text Colors - High Contrast -->
    <color name="text_primary">#0F172A</color>
    <color name="text_secondary">#475569</color>
    <color name="text_tertiary">#94A3B8</color>
    <color name="text_hint">#CBD5E1</color>
    <color name="text_white">#FFFFFF</color>
    <color name="text_on_primary">#FFFFFF</color>

    <!-- Background Colors - Beautiful Gradients -->
    <color name="background_primary">#FFFFFF</color>
    <color name="background_secondary">#F8FAFC</color>
    <color name="background_tertiary">#F1F5F9</color>
    <color name="background_light">#F8FAFC</color>
    <color name="background_card">#FFFFFF</color>
    <color name="background_overlay">#80000000</color>
    <color name="background_gradient_start">#1976D2</color>
    <color name="background_gradient_end">#1565C0</color>
    <color name="background_gradient_tools">#E3F2FD</color>
    <color name="background_gradient_home">#F3E5F5</color>
    <color name="background_gradient_electrical">#FFF8E1</color>

    <!-- Status Colors - Beautiful & Vibrant -->
    <color name="available_green">#81C784</color>
    <color name="out_of_stock_red">#EF5350</color>
    <color name="discount_red">#FF5722</color>
    <color name="new_badge_blue">#90CAF9</color>

    <!-- Interactive Colors - Matching Theme -->
    <color name="button_primary">#1976D2</color>
    <color name="button_secondary">#E3F2FD</color>
    <color name="button_success">#81C784</color>
    <color name="button_danger">#EF5350</color>
    <color name="button_warning">#FFD54F</color>
    <color name="button_info">#90CAF9</color>

    <!-- Border Colors -->
    <color name="border_light">#E2E8F0</color>
    <color name="border_medium">#CBD5E1</color>
    <color name="border_dark">#94A3B8</color>
    <color name="border_color">#E2E8F0</color>
    <color name="divider_color">#E2E8F0</color>

    <!-- App Theme Colors -->
    <color name="primary">@color/primary_blue</color>
    <color name="primary_light">@color/primary_blue_light</color>
    <color name="primary_dark">@color/primary_blue_dark</color>
    <color name="accent">@color/accent_orange</color>
    <color name="success">@color/success_green</color>
    <color name="error">@color/error_red</color>
    <color name="warning">@color/warning_yellow</color>

    <!-- Enhanced UI Colors -->
    <color name="gradient_start_blue">#1976D2</color>
    <color name="gradient_end_blue">#1565C0</color>
    <color name="gradient_start_purple">#9C27B0</color>
    <color name="gradient_end_purple">#7B1FA2</color>
    <color name="gradient_start_orange">#FF9800</color>
    <color name="gradient_end_orange">#F57C00</color>
    <color name="gradient_start_green">#4CAF50</color>
    <color name="gradient_end_green">#388E3C</color>
    <color name="gradient_start_red">#F44336</color>
    <color name="gradient_end_red">#D32F2F</color>

    <!-- Card Colors -->
    <color name="card_background">#FFFFFF</color>
    <color name="card_shadow">#1A000000</color>
    <color name="card_border">#E0E0E0</color>

    <!-- Shimmer Colors -->
    <color name="shimmer_base">#F0F0F0</color>
    <color name="shimmer_highlight">#FFFFFF</color>

    <!-- Ripple Colors -->
    <color name="ripple_blue">#331976D2</color>
    <color name="ripple_white">#33FFFFFF</color>
    <color name="ripple_gray">#33000000</color>

    <!-- Background Colors for Events -->
    <color name="error_background">#FFEBEE</color>
    <color name="success_background">#E8F5E8</color>
    <color name="warning_background">#FFF8E1</color>
    <color name="info_background">#E3F2FD</color>

    <!-- Light Colors for Reviews -->
    <color name="success_light">#E8F5E8</color>
    <color name="accent_light">#FFF8E1</color>

    <!-- Material Design Colors -->
    <color name="purple_200">#FFBB86FC</color>
    <color name="purple_500">#FF6200EE</color>
    <color name="purple_700">#FF3700B3</color>
    <color name="teal_200">#FF03DAC5</color>
    <color name="teal_700">#FF018786</color>
    <color name="black">#FF000000</color>
</resources>
