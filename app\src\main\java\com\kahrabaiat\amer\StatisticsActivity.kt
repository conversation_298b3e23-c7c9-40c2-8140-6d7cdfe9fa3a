package com.kahrabaiat.amer

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.BarChart
import com.github.mikephil.charting.charts.LineChart
import com.github.mikephil.charting.charts.PieChart
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.*
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.formatter.PercentFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import com.kahrabaiat.amer.databinding.ActivityStatisticsBinding
import com.kahrabaiat.amer.models.CategoryConstants
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.FirebaseHelper
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class StatisticsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityStatisticsBinding
    private lateinit var firebaseHelper: FirebaseHelper
    private var allProducts = mutableListOf<Product>()
    private var allOrders = mutableListOf<Order>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityStatisticsBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupClickListeners()
            loadData()
        } catch (e: Exception) {
            android.util.Log.e("StatisticsActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة الإحصائيات: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        firebaseHelper = FirebaseHelper()
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "الإحصائيات والتقارير"
            }
        } catch (e: Exception) {
            android.util.Log.e("StatisticsActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupClickListeners() {
        binding.swipeRefresh.setOnRefreshListener {
            loadData()
        }
    }

    private fun loadData() {
        lifecycleScope.launch {
            try {
                binding.swipeRefresh.isRefreshing = true
                binding.progressBar.visibility = View.VISIBLE

                // Load products and orders
                val products = firebaseHelper.getAllProducts()
                val orders = firebaseHelper.getAllOrders()

                allProducts.clear()
                allProducts.addAll(products)

                allOrders.clear()
                allOrders.addAll(orders)

                // Calculate and display statistics
                calculateOverallStatistics()
                setupProductsChart()
                setupOrdersChart()
                setupRevenueChart()
                setupCategoryChart()

            } catch (e: Exception) {
                android.util.Log.e("StatisticsActivity", "Error loading data", e)
                showToast("خطأ في تحميل البيانات: ${e.message}")
            } finally {
                binding.swipeRefresh.isRefreshing = false
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun calculateOverallStatistics() {
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))

        // Products statistics
        val totalProducts = allProducts.size
        val availableProducts = allProducts.count { it.available }
        val outOfStockProducts = allProducts.count { it.stock == 0 }
        val discountedProducts = allProducts.count { it.discount > 0 }

        binding.tvTotalProducts.text = numberFormat.format(totalProducts)
        binding.tvAvailableProducts.text = numberFormat.format(availableProducts)
        binding.tvOutOfStockProducts.text = numberFormat.format(outOfStockProducts)
        binding.tvDiscountedProducts.text = numberFormat.format(discountedProducts)

        // Orders statistics
        val totalOrders = allOrders.size
        val pendingOrders = allOrders.count { it.status == "pending" }
        val completedOrders = allOrders.count { it.status == "delivered" }
        val cancelledOrders = allOrders.count { it.status == "cancelled" }

        binding.tvTotalOrders.text = numberFormat.format(totalOrders)
        binding.tvPendingOrders.text = numberFormat.format(pendingOrders)
        binding.tvCompletedOrders.text = numberFormat.format(completedOrders)
        binding.tvCancelledOrders.text = numberFormat.format(cancelledOrders)

        // Revenue statistics
        val totalRevenue = allOrders.filter { it.status == "delivered" }.sumOf { it.total }
        val pendingRevenue = allOrders.filter { it.status != "cancelled" && it.status != "delivered" }.sumOf { it.total }
        val averageOrderValue = if (completedOrders > 0) totalRevenue / completedOrders else 0.0

        binding.tvTotalRevenue.text = "${numberFormat.format(totalRevenue.toInt())} د.ع"
        binding.tvPendingRevenue.text = "${numberFormat.format(pendingRevenue.toInt())} د.ع"
        binding.tvAverageOrderValue.text = "${numberFormat.format(averageOrderValue.toInt())} د.ع"

        // Calculate growth rates (mock data for demo)
        binding.tvProductsGrowth.text = "+12%"
        binding.tvOrdersGrowth.text = "+8%"
        binding.tvRevenueGrowth.text = "+15%"
    }

    private fun setupProductsChart() {
        val pieChart = binding.pieChartProducts

        val entries = mutableListOf<PieEntry>()
        val availableCount = allProducts.count { it.available }.toFloat()
        val unavailableCount = allProducts.count { !it.available }.toFloat()

        if (availableCount > 0) entries.add(PieEntry(availableCount, "متاح"))
        if (unavailableCount > 0) entries.add(PieEntry(unavailableCount, "غير متاح"))

        if (entries.isEmpty()) {
            pieChart.visibility = View.GONE
            return
        }

        val dataSet = PieDataSet(entries, "حالة المنتجات")
        dataSet.colors = listOf(
            Color.parseColor("#4CAF50"), // Green for available
            Color.parseColor("#F44336")  // Red for unavailable
        )
        dataSet.valueTextSize = 12f
        dataSet.valueTextColor = Color.WHITE

        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())

        pieChart.apply {
            this.data = data
            description.isEnabled = false
            isRotationEnabled = true
            setUsePercentValues(true)
            setEntryLabelColor(Color.BLACK)
            setEntryLabelTextSize(12f)
            animateY(1400, Easing.EaseInOutQuad)
            invalidate()
        }
    }

    private fun setupOrdersChart() {
        val barChart = binding.barChartOrders

        val entries = mutableListOf<BarEntry>()
        val labels = mutableListOf<String>()

        val statusCounts = mapOf(
            "معلق" to allOrders.count { it.status == "pending" },
            "مؤكد" to allOrders.count { it.status == "confirmed" },
            "قيد المعالجة" to allOrders.count { it.status == "processing" },
            "مشحون" to allOrders.count { it.status == "shipped" },
            "مسلم" to allOrders.count { it.status == "delivered" },
            "ملغي" to allOrders.count { it.status == "cancelled" }
        )

        var index = 0f
        statusCounts.forEach { (status, count) ->
            if (count > 0) {
                entries.add(BarEntry(index, count.toFloat()))
                labels.add(status)
                index++
            }
        }

        if (entries.isEmpty()) {
            barChart.visibility = View.GONE
            return
        }

        val dataSet = BarDataSet(entries, "حالات الطلبات")
        dataSet.colors = ColorTemplate.MATERIAL_COLORS.toList()
        dataSet.valueTextSize = 12f

        val data = BarData(dataSet)
        data.barWidth = 0.8f

        barChart.apply {
            this.data = data
            description.isEnabled = false
            setFitBars(true)

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = IndexAxisValueFormatter(labels)
                granularity = 1f
                setDrawGridLines(false)
            }

            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }

            axisRight.isEnabled = false
            legend.isEnabled = true

            animateY(1400)
            invalidate()
        }
    }

    private fun setupRevenueChart() {
        val lineChart = binding.lineChartRevenue

        // Group orders by month for the last 6 months
        val calendar = Calendar.getInstance()
        val monthlyRevenue = mutableMapOf<String, Double>()
        val monthFormat = SimpleDateFormat("MMM", Locale("ar"))

        // Initialize last 6 months
        for (i in 5 downTo 0) {
            calendar.time = Date()
            calendar.add(Calendar.MONTH, -i)
            val monthKey = monthFormat.format(calendar.time)
            monthlyRevenue[monthKey] = 0.0
        }

        // Calculate actual revenue per month
        allOrders.filter { it.status == "delivered" }.forEach { order ->
            calendar.timeInMillis = order.orderDate
            val monthKey = monthFormat.format(calendar.time)
            monthlyRevenue[monthKey] = monthlyRevenue.getOrDefault(monthKey, 0.0) + order.total
        }

        val entries = mutableListOf<Entry>()
        val labels = mutableListOf<String>()

        var index = 0f
        monthlyRevenue.forEach { (month, revenue) ->
            entries.add(Entry(index, revenue.toFloat()))
            labels.add(month)
            index++
        }

        if (entries.isEmpty()) {
            lineChart.visibility = View.GONE
            return
        }

        val dataSet = LineDataSet(entries, "الإيرادات الشهرية")
        dataSet.apply {
            color = Color.parseColor("#2196F3")
            setCircleColor(Color.parseColor("#2196F3"))
            lineWidth = 3f
            circleRadius = 6f
            setDrawCircleHole(false)
            valueTextSize = 10f
            setDrawFilled(true)
            fillColor = Color.parseColor("#E3F2FD")
        }

        val data = LineData(dataSet)

        lineChart.apply {
            this.data = data
            description.isEnabled = false

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = IndexAxisValueFormatter(labels)
                granularity = 1f
                setDrawGridLines(false)
            }

            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }

            axisRight.isEnabled = false
            legend.isEnabled = true

            animateX(1400)
            invalidate()
        }
    }

    private fun setupCategoryChart() {
        val pieChart = binding.pieChartCategories

        val categoryProducts = mapOf(
            "الأجهزة المنزلية" to allProducts.count { it.category == CategoryConstants.HOME_APPLIANCES },
            "الأجهزة الكهربائية" to allProducts.count { it.category == CategoryConstants.ELECTRICAL_APPLIANCES },
            "العدد اليدوية" to allProducts.count { it.category == CategoryConstants.HAND_TOOLS }
        )

        val entries = mutableListOf<PieEntry>()
        categoryProducts.forEach { (category, count) ->
            if (count > 0) {
                entries.add(PieEntry(count.toFloat(), category))
            }
        }

        if (entries.isEmpty()) {
            pieChart.visibility = View.GONE
            return
        }

        val dataSet = PieDataSet(entries, "توزيع المنتجات حسب الفئة")
        dataSet.colors = ColorTemplate.JOYFUL_COLORS.toList()
        dataSet.valueTextSize = 12f
        dataSet.valueTextColor = Color.WHITE

        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())

        pieChart.apply {
            this.data = data
            description.isEnabled = false
            isRotationEnabled = true
            setUsePercentValues(true)
            setEntryLabelColor(Color.BLACK)
            setEntryLabelTextSize(10f)
            animateY(1400, Easing.EaseInOutQuad)
            invalidate()
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: android.view.Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_statistics, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_custom_reports -> {
                val intent = Intent(this, StatisticsActivity::class.java)
                startActivity(intent)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
