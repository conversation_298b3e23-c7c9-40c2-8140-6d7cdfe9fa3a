package com.kahrabaiat.amer.utils

import android.content.Context
import android.content.Intent
import android.os.Environment
import androidx.core.content.FileProvider
import com.kahrabaiat.amer.models.Order
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

class PDFGenerator(private val context: Context) {

    fun generateInvoice(order: Order): File? {
        return try {
            val fileName = "invoice_${order.id}.txt"
            val file = File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), fileName)

            val content = buildInvoiceContent(order)
            file.writeText(content, Charsets.UTF_8)

            // Share the file
            shareInvoice(file)

            file
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun buildInvoiceContent(order: Order): String {
        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
        val numberFormat = java.text.NumberFormat.getNumberInstance(Locale("ar", "IQ"))
        val content = StringBuilder()

        // Header
        content.append("╔═══════════════════════════════════════╗\n")
        content.append("║              🔌 كهربائيات عامر 🔌              ║\n")
        content.append("║           أدوات كهربائية عالية الجودة           ║\n")
        content.append("╚═══════════════════════════════════════╝\n")
        content.append("\n")

        // Invoice Info
        content.append("┌─────────────────────────────────────────┐\n")
        content.append("│                🧾 فاتورة شراء                │\n")
        content.append("└─────────────────────────────────────────┘\n")
        content.append("📋 رقم الفاتورة: #${order.id}\n")
        content.append("📅 التاريخ: ${dateFormat.format(Date(order.orderDate))}\n")
        content.append("⏰ الوقت: ${SimpleDateFormat("HH:mm", Locale("ar")).format(Date(order.orderDate))}\n")
        content.append("📍 الحالة: ${getStatusDisplayName(order.status)}\n")
        content.append("\n")

        // Customer Info
        content.append("┌─────────────────────────────────────────┐\n")
        content.append("│              👤 معلومات العميل              │\n")
        content.append("└─────────────────────────────────────────┘\n")
        content.append("👨‍💼 الاسم: ${order.customerName}\n")
        content.append("📞 الهاتف: ${order.customerPhone}\n")
        content.append("🏠 العنوان: ${order.customerAddress}\n")
        content.append("\n")

        // Items Header
        content.append("┌─────────────────────────────────────────┐\n")
        content.append("│              🛍️ تفاصيل المشتريات              │\n")
        content.append("└─────────────────────────────────────────┘\n")
        content.append("┌─────┬──────────────────────┬─────┬─────────┬─────────┐\n")
        content.append("│ ت   │ اسم المنتج            │ كمية │ سعر وحدة │ المجموع  │\n")
        content.append("├─────┼──────────────────────┼─────┼─────────┼─────────┤\n")

        // Items
        var totalItems = 0
        var subtotal = 0.0

        order.items.forEachIndexed { index, item ->
            val itemName = if (item.productName.length > 18) {
                item.productName.take(15) + "..."
            } else {
                item.productName.padEnd(18)
            }

            val quantity = item.quantity.toString().padStart(3)
            val unitPrice = numberFormat.format(item.price.toInt()).padStart(7)
            val itemTotal = numberFormat.format(item.total.toInt()).padStart(7)

            content.append("│ ${(index + 1).toString().padStart(2)}  │ $itemName │ $quantity │ $unitPrice │ $itemTotal │\n")

            totalItems += item.quantity
            subtotal += item.total
        }

        content.append("└─────┴──────────────────────┴─────┴─────────┴─────────┘\n")
        content.append("\n")

        // Summary
        content.append("┌─────────────────────────────────────────┐\n")
        content.append("│              💰 ملخص الفاتورة              │\n")
        content.append("└─────────────────────────────────────────┘\n")
        content.append("📦 إجمالي المنتجات: ${order.items.size} منتج\n")
        content.append("🔢 إجمالي الكمية: $totalItems قطعة\n")
        content.append("💵 المجموع الفرعي: ${numberFormat.format(subtotal.toInt())} د.ع\n")
        content.append("🏷️ الخصم: 0 د.ع\n")
        content.append("📊 الضريبة: 0 د.ع\n")
        content.append("═══════════════════════════════════════\n")
        content.append("💰 المبلغ الإجمالي: ${numberFormat.format(order.total.toInt())} د.ع\n")
        content.append("═══════════════════════════════════════\n")
        content.append("\n")

        // Payment Info
        content.append("┌─────────────────────────────────────────┐\n")
        content.append("│              💳 معلومات الدفع              │\n")
        content.append("└─────────────────────────────────────────┘\n")
        content.append("💰 طريقة الدفع: نقداً عند الاستلام\n")
        content.append("📋 حالة الدفع: معلق\n")
        content.append("🚚 طريقة التسليم: توصيل منزلي\n")
        content.append("\n")

        // Footer
        content.append("┌─────────────────────────────────────────┐\n")
        content.append("│              📞 معلومات التواصل              │\n")
        content.append("└─────────────────────────────────────────┘\n")
        content.append("📱 الهاتف: 07901234567\n")
        content.append("📧 البريد: <EMAIL>\n")
        content.append("🌐 الموقع: www.kahrabaiat-amer.com\n")
        content.append("📍 العنوان: بغداد - الكرادة\n")
        content.append("\n")
        content.append("╔═══════════════════════════════════════╗\n")
        content.append("║        🙏 شكراً لتسوقكم معنا! 🙏        ║\n")
        content.append("║         كهربائيات عامر - جودة وثقة         ║\n")
        content.append("║      نتطلع لخدمتكم في المستقبل ✨      ║\n")
        content.append("╚═══════════════════════════════════════╝\n")
        content.append("\n")
        content.append("تاريخ الطباعة: ${SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale("ar")).format(Date())}\n")

        return content.toString()
    }

    private fun getStatusDisplayName(status: String): String {
        return when (status.lowercase()) {
            "pending" -> "معلق"
            "confirmed" -> "مؤكد"
            "processing" -> "قيد المعالجة"
            "shipped" -> "مشحون"
            "delivered" -> "مسلم"
            "cancelled" -> "ملغي"
            else -> "غير محدد"
        }
    }

    private fun shareInvoice(file: File) {
        try {
            val uri = FileProvider.getUriForFile(
                context,
                "${context.packageName}.fileprovider",
                file
            )

            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_STREAM, uri)
                putExtra(Intent.EXTRA_SUBJECT, "🧾 فاتورة شراء - كهربائيات عامر")
                putExtra(Intent.EXTRA_TEXT, "فاتورة شراء من كهربائيات عامر\nجودة وثقة في كل منتج 🔌")
                addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            context.startActivity(Intent.createChooser(intent, "📤 مشاركة الفاتورة"))
        } catch (e: Exception) {
            android.util.Log.e("PDFGenerator", "Error sharing invoice", e)
        }
    }

    fun generateOrderReport(order: Order): String {
        return buildInvoiceContent(order)
    }
}
