<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:orientation="vertical"
    tools:context=".AddProductActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_background"
        android:elevation="8dp"
        app:titleTextColor="@color/white" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Product Form Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Product Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/InputField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="اسم المنتج">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etProductName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Product Image Section -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="صورة المنتج"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:orientation="horizontal">

                        <!-- Image Preview -->
                        <androidx.cardview.widget.CardView
                            android:layout_width="120dp"
                            android:layout_height="120dp"
                            android:layout_marginEnd="16dp"
                            app:cardCornerRadius="8dp"
                            app:cardElevation="2dp">

                            <ImageView
                                android:id="@+id/ivProductImage"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@color/background_secondary"
                                android:scaleType="centerCrop"
                                android:src="@drawable/ic_image_placeholder" />

                        </androidx.cardview.widget.CardView>

                        <!-- Image Selection Buttons -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnSelectImage"
                                style="@style/Widget.Material3.Button.OutlinedButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="8dp"
                                android:text="اختيار صورة"
                                app:icon="@drawable/ic_image"
                                app:iconGravity="textStart" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnTakePhoto"
                                style="@style/Widget.Material3.Button.OutlinedButton"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="التقاط صورة"
                                app:icon="@drawable/ic_camera"
                                app:iconGravity="textStart" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Price -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/InputField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="السعر (دينار عراقي)"
                        app:suffixText="د.ع">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etPrice"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Category -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="التصنيف"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <Spinner
                        android:id="@+id/spinnerCategory"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/spinner_background"
                        android:padding="12dp" />

                    <!-- Stock -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/InputField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="الكمية المتوفرة">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etStock"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Discount -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/InputField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="نسبة الخصم (اختياري)"
                        app:suffixText="%">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etDiscount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Description -->
                    <com.google.android.material.textfield.TextInputLayout
                        style="@style/InputField"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="24dp"
                        android:hint="وصف المنتج">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etDescription"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="top"
                            android:inputType="textMultiLine"
                            android:lines="3"
                            android:maxLines="5" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Action Buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- Cancel Button -->
                        <Button
                            android:id="@+id/btnCancel"
                            style="@style/SecondaryButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:text="إلغاء" />

                        <!-- Save Button -->
                        <Button
                            android:id="@+id/btnSaveProduct"
                            style="@style/PrimaryButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:drawableStart="@drawable/ic_save"
                            android:drawableTint="@color/white"
                            android:text="حفظ المنتج" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
