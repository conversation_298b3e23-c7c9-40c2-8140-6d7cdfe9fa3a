<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow Layer 1 -->
    <item android:top="4dp" android:left="4dp">
        <shape android:shape="rectangle">
            <solid android:color="#10000000" />
            <corners android:radius="20dp" />
        </shape>
    </item>
    
    <!-- Shadow Layer 2 -->
    <item android:top="2dp" android:left="2dp">
        <shape android:shape="rectangle">
            <solid android:color="#20000000" />
            <corners android:radius="18dp" />
        </shape>
    </item>
    
    <!-- Card Background -->
    <item android:bottom="4dp" android:right="4dp">
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="#FFFFFF"
                android:endColor="#FAFAFA"
                android:type="linear" />
            <stroke 
                android:width="1dp" 
                android:color="#E0E0E0" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
</layer-list>
