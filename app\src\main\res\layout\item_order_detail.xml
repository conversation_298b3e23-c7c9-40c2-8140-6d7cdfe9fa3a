<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- Product Image -->
        <ImageView
            android:id="@+id/ivProductImage"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/rounded_corner_background"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_product_placeholder" />

        <!-- Product Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Product Name -->
            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="مفك براغي احترافي مع مجموعة رؤوس" />

            <!-- Quantity and Unit Price -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvQuantity"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="الكمية: 2" />

                <TextView
                    android:id="@+id/tvUnitPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="15000 د.ع" />

            </LinearLayout>

            <!-- Discount (if any) -->
            <TextView
                android:id="@+id/tvDiscount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="@color/success_green"
                android:textSize="11sp"
                android:visibility="gone"
                tools:text="خصم: 5000 د.ع"
                tools:visibility="visible" />

        </LinearLayout>

        <!-- Total Price -->
        <TextView
            android:id="@+id/tvTotalPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:textColor="@color/primary_blue"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="30000 د.ع" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
