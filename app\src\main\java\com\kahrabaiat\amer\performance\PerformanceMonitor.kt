package com.kahrabaiat.amer.performance

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Handler
import android.os.Looper
import android.util.Log
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentLinkedQueue

class PerformanceMonitor private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "PerformanceMonitor"
        private const val MONITORING_INTERVAL = 5000L // 5 ثوان
        private const val MAX_METRICS_HISTORY = 100
        
        @Volatile
        private var INSTANCE: PerformanceMonitor? = null
        
        fun getInstance(context: Context): PerformanceMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: PerformanceMonitor(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    private val metricsHistory = ConcurrentLinkedQueue<PerformanceMetrics>()
    private var monitoringJob: Job? = null
    private var isMonitoring = false
    
    private val performanceListeners = mutableListOf<PerformanceListener>()
    
    /**
     * بدء مراقبة الأداء
     */
    fun startMonitoring() {
        if (isMonitoring) return
        
        isMonitoring = true
        monitoringJob = CoroutineScope(Dispatchers.IO).launch {
            while (isMonitoring) {
                try {
                    val metrics = collectMetrics()
                    addMetrics(metrics)
                    
                    // تحليل الأداء وإرسال تنبيهات إذا لزم الأمر
                    analyzePerformance(metrics)
                    
                    delay(MONITORING_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "Error during performance monitoring", e)
                }
            }
        }
        
        Log.d(TAG, "Performance monitoring started")
    }
    
    /**
     * إيقاف مراقبة الأداء
     */
    fun stopMonitoring() {
        isMonitoring = false
        monitoringJob?.cancel()
        Log.d(TAG, "Performance monitoring stopped")
    }
    
    /**
     * جمع مقاييس الأداء الحالية
     */
    private fun collectMetrics(): PerformanceMetrics {
        val runtime = Runtime.getRuntime()
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        // معلومات الذاكرة
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory()
        val memoryUsagePercent = (usedMemory.toDouble() / maxMemory * 100).toInt()
        
        // معلومات النظام
        val availableMemory = memoryInfo.availMem
        val totalSystemMemory = memoryInfo.totalMem
        val isLowMemory = memoryInfo.lowMemory
        
        // معلومات CPU (تقديرية)
        val cpuUsage = getCpuUsage()
        
        // معلومات التخزين
        val storageInfo = getStorageInfo()
        
        // معلومات الشبكة
        val networkInfo = getNetworkInfo()
        
        return PerformanceMetrics(
            timestamp = System.currentTimeMillis(),
            usedMemory = usedMemory,
            totalMemory = totalMemory,
            maxMemory = maxMemory,
            memoryUsagePercent = memoryUsagePercent,
            availableSystemMemory = availableMemory,
            totalSystemMemory = totalSystemMemory,
            isLowMemory = isLowMemory,
            cpuUsage = cpuUsage,
            storageInfo = storageInfo,
            networkInfo = networkInfo
        )
    }
    
    /**
     * الحصول على استخدام CPU (تقديري)
     */
    private fun getCpuUsage(): Double {
        return try {
            val startTime = Debug.threadCpuTimeNanos()
            Thread.sleep(100)
            val endTime = Debug.threadCpuTimeNanos()
            val cpuTime = endTime - startTime
            val wallTime = 100_000_000.0 // 100ms in nanoseconds
            (cpuTime / wallTime * 100).coerceAtMost(100.0)
        } catch (e: Exception) {
            0.0
        }
    }
    
    /**
     * الحصول على معلومات التخزين
     */
    private fun getStorageInfo(): StorageInfo {
        return try {
            val internalDir = context.filesDir
            val totalSpace = internalDir.totalSpace
            val freeSpace = internalDir.freeSpace
            val usedSpace = totalSpace - freeSpace
            val usagePercent = (usedSpace.toDouble() / totalSpace * 100).toInt()
            
            StorageInfo(
                totalSpace = totalSpace,
                freeSpace = freeSpace,
                usedSpace = usedSpace,
                usagePercent = usagePercent
            )
        } catch (e: Exception) {
            StorageInfo(0, 0, 0, 0)
        }
    }
    
    /**
     * الحصول على معلومات الشبكة
     */
    private fun getNetworkInfo(): NetworkInfo {
        return try {
            val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) 
                as android.net.ConnectivityManager
            val activeNetwork = connectivityManager.activeNetworkInfo
            
            NetworkInfo(
                isConnected = activeNetwork?.isConnected == true,
                networkType = activeNetwork?.typeName ?: "Unknown",
                isWifi = activeNetwork?.type == android.net.ConnectivityManager.TYPE_WIFI,
                isMobile = activeNetwork?.type == android.net.ConnectivityManager.TYPE_MOBILE
            )
        } catch (e: Exception) {
            NetworkInfo(false, "Unknown", false, false)
        }
    }
    
    /**
     * إضافة مقاييس جديدة للتاريخ
     */
    private fun addMetrics(metrics: PerformanceMetrics) {
        metricsHistory.offer(metrics)
        
        // الاحتفاظ بعدد محدود من المقاييس
        while (metricsHistory.size > MAX_METRICS_HISTORY) {
            metricsHistory.poll()
        }
    }
    
    /**
     * تحليل الأداء وإرسال تنبيهات
     */
    private fun analyzePerformance(metrics: PerformanceMetrics) {
        val issues = mutableListOf<PerformanceIssue>()
        
        // فحص استخدام الذاكرة
        if (metrics.memoryUsagePercent > 85) {
            issues.add(PerformanceIssue.HIGH_MEMORY_USAGE)
        }
        
        if (metrics.isLowMemory) {
            issues.add(PerformanceIssue.LOW_SYSTEM_MEMORY)
        }
        
        // فحص استخدام CPU
        if (metrics.cpuUsage > 80) {
            issues.add(PerformanceIssue.HIGH_CPU_USAGE)
        }
        
        // فحص التخزين
        if (metrics.storageInfo.usagePercent > 90) {
            issues.add(PerformanceIssue.LOW_STORAGE_SPACE)
        }
        
        // فحص الشبكة
        if (!metrics.networkInfo.isConnected) {
            issues.add(PerformanceIssue.NO_NETWORK_CONNECTION)
        }
        
        // إرسال تنبيهات للمستمعين
        if (issues.isNotEmpty()) {
            notifyPerformanceIssues(metrics, issues)
        }
    }
    
    /**
     * إشعار المستمعين بمشاكل الأداء
     */
    private fun notifyPerformanceIssues(metrics: PerformanceMetrics, issues: List<PerformanceIssue>) {
        Handler(Looper.getMainLooper()).post {
            performanceListeners.forEach { listener ->
                try {
                    listener.onPerformanceIssuesDetected(metrics, issues)
                } catch (e: Exception) {
                    Log.e(TAG, "Error notifying performance listener", e)
                }
            }
        }
    }
    
    /**
     * إضافة مستمع للأداء
     */
    fun addPerformanceListener(listener: PerformanceListener) {
        performanceListeners.add(listener)
    }
    
    /**
     * إزالة مستمع الأداء
     */
    fun removePerformanceListener(listener: PerformanceListener) {
        performanceListeners.remove(listener)
    }
    
    /**
     * الحصول على المقاييس الحالية
     */
    fun getCurrentMetrics(): PerformanceMetrics? {
        return metricsHistory.lastOrNull()
    }
    
    /**
     * الحصول على تاريخ المقاييس
     */
    fun getMetricsHistory(): List<PerformanceMetrics> {
        return metricsHistory.toList()
    }
    
    /**
     * الحصول على تقرير الأداء
     */
    fun getPerformanceReport(): PerformanceReport {
        val metrics = metricsHistory.toList()
        if (metrics.isEmpty()) {
            return PerformanceReport.empty()
        }
        
        val avgMemoryUsage = metrics.map { it.memoryUsagePercent }.average()
        val maxMemoryUsage = metrics.maxOfOrNull { it.memoryUsagePercent } ?: 0
        val avgCpuUsage = metrics.map { it.cpuUsage }.average()
        val maxCpuUsage = metrics.maxOfOrNull { it.cpuUsage } ?: 0.0
        
        val lowMemoryEvents = metrics.count { it.isLowMemory }
        val networkIssues = metrics.count { !it.networkInfo.isConnected }
        
        return PerformanceReport(
            totalSamples = metrics.size,
            averageMemoryUsage = avgMemoryUsage.toInt(),
            maxMemoryUsage = maxMemoryUsage,
            averageCpuUsage = avgCpuUsage,
            maxCpuUsage = maxCpuUsage,
            lowMemoryEvents = lowMemoryEvents,
            networkIssues = networkIssues,
            monitoringDuration = if (metrics.isNotEmpty()) metrics.last().timestamp - metrics.first().timestamp else 0
        )
    }
    
    /**
     * تنظيف الذاكرة
     */
    fun performMemoryCleanup() {
        try {
            // تشغيل garbage collector
            System.gc()
            
            // تنظيف ذاكرة الصور
            com.kahrabaiat.amer.ui.ImageLoader.clearCache(context)
            
            Log.d(TAG, "Memory cleanup performed")
        } catch (e: Exception) {
            Log.e(TAG, "Error during memory cleanup", e)
        }
    }
    
    /**
     * تحسين الأداء التلقائي
     */
    fun optimizePerformance() {
        val currentMetrics = getCurrentMetrics() ?: return
        
        if (currentMetrics.memoryUsagePercent > 80) {
            performMemoryCleanup()
        }
        
        if (currentMetrics.isLowMemory) {
            // تقليل جودة الصور مؤقتاً
            com.kahrabaiat.amer.ui.ImageLoader.pauseRequests(context)
            
            // تأخير قصير ثم استئناف
            Handler(Looper.getMainLooper()).postDelayed({
                com.kahrabaiat.amer.ui.ImageLoader.resumeRequests(context)
            }, 2000)
        }
    }
    
    // Data classes
    data class PerformanceMetrics(
        val timestamp: Long,
        val usedMemory: Long,
        val totalMemory: Long,
        val maxMemory: Long,
        val memoryUsagePercent: Int,
        val availableSystemMemory: Long,
        val totalSystemMemory: Long,
        val isLowMemory: Boolean,
        val cpuUsage: Double,
        val storageInfo: StorageInfo,
        val networkInfo: NetworkInfo
    )
    
    data class StorageInfo(
        val totalSpace: Long,
        val freeSpace: Long,
        val usedSpace: Long,
        val usagePercent: Int
    )
    
    data class NetworkInfo(
        val isConnected: Boolean,
        val networkType: String,
        val isWifi: Boolean,
        val isMobile: Boolean
    )
    
    data class PerformanceReport(
        val totalSamples: Int,
        val averageMemoryUsage: Int,
        val maxMemoryUsage: Int,
        val averageCpuUsage: Double,
        val maxCpuUsage: Double,
        val lowMemoryEvents: Int,
        val networkIssues: Int,
        val monitoringDuration: Long
    ) {
        companion object {
            fun empty() = PerformanceReport(0, 0, 0, 0.0, 0.0, 0, 0, 0)
        }
    }
    
    enum class PerformanceIssue {
        HIGH_MEMORY_USAGE,
        LOW_SYSTEM_MEMORY,
        HIGH_CPU_USAGE,
        LOW_STORAGE_SPACE,
        NO_NETWORK_CONNECTION
    }
    
    interface PerformanceListener {
        fun onPerformanceIssuesDetected(metrics: PerformanceMetrics, issues: List<PerformanceIssue>)
    }
}
