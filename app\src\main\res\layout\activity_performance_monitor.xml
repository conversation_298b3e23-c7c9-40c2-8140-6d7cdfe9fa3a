<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_background_main"
    tools:context=".PerformanceMonitorActivity">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_header_background"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Performance Metrics Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="مقاييس الأداء"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_analytics"
                        android:drawablePadding="8dp" />

                    <!-- Memory Usage -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/tvMemoryUsage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="استخدام الذاكرة: 0%"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <ProgressBar
                            android:id="@+id/progressMemory"
                            style="@android:style/Widget.ProgressBar.Horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="8dp"
                            android:layout_marginBottom="4dp"
                            android:max="100" />

                        <TextView
                            android:id="@+id/tvMemoryDetails"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 MB / 0 MB"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- CPU Usage -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/tvCpuUsage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="استخدام المعالج: 0%"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <ProgressBar
                            android:id="@+id/progressCpu"
                            style="@android:style/Widget.ProgressBar.Horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="8dp"
                            android:max="100"
                            android:progressTint="@color/primary_blue" />

                    </LinearLayout>

                    <!-- Storage Usage -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:id="@+id/tvStorageUsage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="استخدام التخزين: 0%"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <ProgressBar
                            android:id="@+id/progressStorage"
                            style="@android:style/Widget.ProgressBar.Horizontal"
                            android:layout_width="match_parent"
                            android:layout_height="8dp"
                            android:layout_marginBottom="4dp"
                            android:max="100"
                            android:progressTint="@color/warning_yellow" />

                        <TextView
                            android:id="@+id/tvStorageDetails"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0 GB / 0 GB"
                            android:textSize="12sp"
                            android:textColor="@color/text_secondary" />

                    </LinearLayout>

                    <!-- Low Memory Status -->
                    <TextView
                        android:id="@+id/tvLowMemoryStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="الذاكرة طبيعية"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:drawableStart="@drawable/ic_memory"
                        android:drawablePadding="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Network Status Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="حالة الشبكة"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_network"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:id="@+id/tvNetworkStatus"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="متصل"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvNetworkType"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="نوع الشبكة: واي فاي"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvNetworkMetered"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="شبكة غير محدودة"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Cache Statistics Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إحصائيات التخزين المؤقت"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_storage"
                        android:drawablePadding="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="8dp">

                        <TextView
                            android:id="@+id/tvCacheMemoryEntries"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عناصر الذاكرة: 0"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvCacheDiskEntries"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="عناصر القرص: 0"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvCacheHitRatio"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="نسبة النجاح: 0%"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <ProgressBar
                        android:id="@+id/progressCacheHitRatio"
                        style="@android:style/Widget.ProgressBar.Horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="8dp"
                        android:layout_marginBottom="8dp"
                        android:max="100" />

                    <TextView
                        android:id="@+id/tvCacheDiskSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="حجم القرص: 0 MB"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Crash Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="معلومات الأخطاء"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_bug_report"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:id="@+id/tvCrashCount"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="عدد الأخطاء: 0"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvLastCrash"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="لا توجد أخطاء مسجلة"
                        android:textSize="14sp"
                        android:textColor="@color/text_secondary" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="8dp"
                android:background="@drawable/card_background_elevated">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="إجراءات الأداء"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_settings"
                        android:drawablePadding="8dp" />

                    <Button
                        android:id="@+id/btnOptimizePerformance"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="🚀 تحسين الأداء"
                        android:backgroundTint="@color/primary_blue"
                        android:textColor="@color/white"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:padding="20dp"
                        android:elevation="6dp" />

                    <!-- Memory Cleanup Button -->
                    <Button
                        android:id="@+id/btnMemoryCleanup"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="🧹 تنظيف الذاكرة"
                        android:textColor="@color/success_green"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="16dp"
                        app:strokeColor="@color/success_green"
                        app:strokeWidth="2dp" />

                    <!-- Clear Cache Button -->
                    <Button
                        android:id="@+id/btnClearCache"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="🗑️ مسح التخزين المؤقت"
                        android:textColor="@color/warning_yellow"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="16dp"
                        app:strokeColor="@color/warning_yellow"
                        app:strokeWidth="2dp" />

                    <!-- Crash Reports Button -->
                    <Button
                        android:id="@+id/btnViewCrashReports"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="🐛 عرض تقارير الأخطاء"
                        android:backgroundTint="@color/error_red"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="16dp"
                        android:elevation="4dp" />

                    <!-- Refresh Data Button -->
                    <Button
                        android:id="@+id/btnRefreshData"
                        style="@style/Widget.Material3.Button"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="🔄 تحديث البيانات"
                        android:backgroundTint="@color/success_green"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="16dp"
                        android:elevation="4dp" />

                    <!-- Export Report Button -->
                    <Button
                        android:id="@+id/btnExportReport"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="📊 تصدير تقرير الأداء"
                        android:textColor="@color/primary_blue"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:padding="16dp"
                        app:strokeColor="@color/primary_blue"
                        app:strokeWidth="2dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
