package com.kahrabaiat.amer.utils

import android.content.Context
import android.content.SharedPreferences
import com.kahrabaiat.amer.security.EncryptionManager
import com.kahrabaiat.amer.security.SecurityMonitor

class AuthManager(context: Context) {

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("admin_prefs", Context.MODE_PRIVATE)
    private val encryptionManager = EncryptionManager.getInstance(context)
    private val securityMonitor = SecurityMonitor.getInstance(context)

    companion object {
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_LOGIN_TIME = "login_time"
        private const val KEY_USER_TYPE = "user_type"
        private const val SESSION_DURATION = 24 * 60 * 60 * 1000L // 24 ساعة

        @Volatile
        private var INSTANCE: AuthManager? = null

        fun getInstance(context: Context): AuthManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AuthManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    enum class UserType {
        ADMIN,    // إدارة عامة
        OWNER,    // المالك (عامر)
        GUEST     // زائر
    }

    /**
     * التحقق من صحة الجلسة الحالية
     */
    fun isValidSession(): Boolean {
        val isLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
        val loginTime = sharedPreferences.getLong(KEY_LOGIN_TIME, 0)
        val currentTime = System.currentTimeMillis()

        return isLoggedIn && (currentTime - loginTime) < SESSION_DURATION
    }

    /**
     * حفظ جلسة تسجيل الدخول
     */
    fun saveLoginSession(userType: UserType) {
        sharedPreferences.edit().apply {
            putBoolean(KEY_IS_LOGGED_IN, true)
            putLong(KEY_LOGIN_TIME, System.currentTimeMillis())
            putString(KEY_USER_TYPE, userType.name)
            apply()
        }
    }

    /**
     * الحصول على نوع المستخدم الحالي
     */
    fun getCurrentUserType(): UserType {
        val userTypeString = sharedPreferences.getString(KEY_USER_TYPE, UserType.GUEST.name)
        return try {
            UserType.valueOf(userTypeString ?: UserType.GUEST.name)
        } catch (e: IllegalArgumentException) {
            UserType.GUEST
        }
    }

    /**
     * التحقق من صلاحيات الإدارة
     */
    fun hasAdminPermission(): Boolean {
        if (!isValidSession()) return false

        val userType = getCurrentUserType()
        return userType == UserType.ADMIN || userType == UserType.OWNER
    }

    /**
     * التحقق من صلاحيات المالك
     */
    fun hasOwnerPermission(): Boolean {
        if (!isValidSession()) return false

        val userType = getCurrentUserType()
        return userType == UserType.OWNER
    }

    /**
     * تسجيل الخروج
     */
    fun logout() {
        sharedPreferences.edit().apply {
            putBoolean(KEY_IS_LOGGED_IN, false)
            remove(KEY_LOGIN_TIME)
            remove(KEY_USER_TYPE)
            apply()
        }
    }

    /**
     * الحصول على اسم المستخدم الحالي
     */
    fun getCurrentUserName(): String {
        return when (getCurrentUserType()) {
            UserType.ADMIN -> "المدير"
            UserType.OWNER -> "أستاذ عامر"
            UserType.GUEST -> "زائر"
        }
    }

    /**
     * الحصول على وقت انتهاء الجلسة
     */
    fun getSessionExpiryTime(): Long {
        val loginTime = sharedPreferences.getLong(KEY_LOGIN_TIME, 0)
        return loginTime + SESSION_DURATION
    }

    /**
     * تمديد الجلسة
     */
    fun extendSession() {
        if (isValidSession()) {
            sharedPreferences.edit().apply {
                putLong(KEY_LOGIN_TIME, System.currentTimeMillis())
                apply()
            }
        }
    }

    /**
     * التحقق من انتهاء الجلسة قريباً (خلال ساعة)
     */
    fun isSessionExpiringSoon(): Boolean {
        if (!isValidSession()) return false

        val expiryTime = getSessionExpiryTime()
        val currentTime = System.currentTimeMillis()
        val oneHour = 60 * 60 * 1000L

        return (expiryTime - currentTime) < oneHour
    }

    /**
     * تسجيل دخول آمن مع التحقق من الأمان
     */
    fun secureLogin(username: String, password: String): LoginResult {
        // التحقق من حالة الحظر
        if (securityMonitor.isAccountBlocked()) {
            val blockedUntil = securityMonitor.getBlockedUntilTime()
            val remainingTime = (blockedUntil - System.currentTimeMillis()) / 1000 / 60 // دقائق
            return LoginResult.BLOCKED("Account is blocked for $remainingTime minutes")
        }

        // التحقق من بيانات الدخول
        val loginSuccess = when {
            username == "admin" && password == "kahrabaiat2024" -> UserType.ADMIN
            username == "amer" && password == "amer123456" -> UserType.OWNER
            else -> null
        }

        if (loginSuccess != null) {
            // تسجيل دخول ناجح
            securityMonitor.recordSuccessfulLogin(username, loginSuccess.name)
            saveLoginSession(loginSuccess)
            return LoginResult.SUCCESS(loginSuccess)
        } else {
            // تسجيل محاولة فاشلة
            securityMonitor.recordFailedLoginAttempt(username)
            val attempts = securityMonitor.getLoginAttempts()
            return LoginResult.FAILED("Invalid credentials. Attempt $attempts/5")
        }
    }

    /**
     * تغيير كلمة المرور بشكل آمن
     */
    fun changePassword(oldPassword: String, newPassword: String, username: String): Boolean {
        // التحقق من كلمة المرور الحالية
        val currentUserType = getCurrentUserType()
        val isValidOldPassword = when (currentUserType) {
            UserType.ADMIN -> oldPassword == "kahrabaiat2024"
            UserType.OWNER -> oldPassword == "amer123456"
            else -> false
        }

        if (!isValidOldPassword) {
            securityMonitor.recordSuspiciousActivity(
                "PASSWORD_CHANGE_ATTEMPT",
                "Invalid old password provided",
                mapOf("username" to username)
            )
            return false
        }

        // تشفير وحفظ كلمة المرور الجديدة
        val encryptedPassword = encryptionManager.encryptPassword(newPassword)
        if (encryptedPassword != null) {
            // حفظ كلمة المرور المشفرة
            sharedPreferences.edit().apply {
                putString("encrypted_password_$username", encryptedPassword)
                apply()
            }

            // تسجيل حدث أمني
            securityMonitor.recordSuspiciousActivity(
                "PASSWORD_CHANGED",
                "Password changed successfully",
                mapOf("username" to username)
            )

            return true
        }

        return false
    }

    /**
     * الحصول على تقرير الأمان
     */
    fun getSecurityReport(): SecurityMonitor.SecurityReport {
        return securityMonitor.getSecurityReport()
    }

    /**
     * إلغاء حظر الحساب (للمالك فقط)
     */
    fun unblockAccount(): Boolean {
        if (hasOwnerPermission()) {
            securityMonitor.unblockAccount()
            return true
        }
        return false
    }

    /**
     * مسح سجل الأمان (للمالك فقط)
     */
    fun clearSecurityLog(): Boolean {
        if (hasOwnerPermission()) {
            securityMonitor.clearSecurityLog()
            return true
        }
        return false
    }

    /**
     * التحقق من سلامة التطبيق
     */
    fun checkAppIntegrity(context: Context): com.kahrabaiat.amer.security.AppIntegrityChecker.IntegrityResult {
        return com.kahrabaiat.amer.security.AppIntegrityChecker.checkAppIntegrity(context)
    }

    // نتائج تسجيل الدخول

    sealed class LoginResult {
        data class SUCCESS(val userType: UserType) : LoginResult()
        data class FAILED(val message: String) : LoginResult()
        data class BLOCKED(val message: String) : LoginResult()
    }
}
