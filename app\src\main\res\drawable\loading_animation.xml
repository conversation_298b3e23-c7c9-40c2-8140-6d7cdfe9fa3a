<?xml version="1.0" encoding="utf-8"?>
<animated-vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt">

    <aapt:attr name="android:drawable">
        <vector
            android:width="48dp"
            android:height="48dp"
            android:viewportWidth="48"
            android:viewportHeight="48">

            <group android:name="rotationGroup">
                <path
                    android:name="circle"
                    android:pathData="M24,4 A20,20 0 0,1 44,24"
                    android:strokeColor="@color/primary_blue"
                    android:strokeWidth="4"
                    android:strokeLineCap="round" />
            </group>

        </vector>
    </aapt:attr>

    <target android:name="rotationGroup">
        <aapt:attr name="android:animation">
            <objectAnimator
                android:propertyName="rotation"
                android:duration="1000"
                android:valueFrom="0"
                android:valueTo="360"
                android:repeatCount="infinite"
                android:interpolator="@android:anim/linear_interpolator" />
        </aapt:attr>
    </target>

</animated-vector>
