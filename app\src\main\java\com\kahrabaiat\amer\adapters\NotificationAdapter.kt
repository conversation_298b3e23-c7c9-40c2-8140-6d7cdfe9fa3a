package com.kahrabaiat.amer.adapters

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemNotificationBinding
import com.kahrabaiat.amer.models.Notification
import com.kahrabaiat.amer.models.NotificationType

class NotificationAdapter(
    private val onNotificationClick: (Notification) -> Unit = {},
    private val onMarkAsRead: (Notification) -> Unit = {},
    private val onDeleteClick: (Notification) -> Unit = {},
    private val showAdminActions: Boolean = false
) : ListAdapter<Notification, NotificationAdapter.NotificationViewHolder>(NotificationDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): NotificationViewHolder {
        val binding = ItemNotificationBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return NotificationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: NotificationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class NotificationViewHolder(
        private val binding: ItemNotificationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(notification: Notification) {
            binding.apply {
                // العنوان والرسالة
                tvNotificationTitle.text = notification.title
                tvNotificationMessage.text = notification.message
                tvNotificationTime.text = notification.getRelativeTime()

                // الأيقونة حسب النوع
                tvNotificationIcon.text = notification.getIcon()

                // الصورة إذا كانت متوفرة
                if (notification.imageUrl.isNotEmpty()) {
                    Glide.with(itemView.context)
                        .load(notification.imageUrl)
                        .placeholder(R.drawable.ic_notification)
                        .error(R.drawable.ic_notification)
                        .into(ivNotificationImage)
                    ivNotificationImage.visibility = View.VISIBLE
                } else {
                    ivNotificationImage.visibility = View.GONE
                }

                // حالة القراءة
                if (notification.isRead) {
                    cardNotification.alpha = 0.7f
                    tvNotificationTitle.setTextColor(Color.parseColor("#64748B"))
                    indicatorUnread.visibility = View.GONE
                } else {
                    cardNotification.alpha = 1.0f
                    tvNotificationTitle.setTextColor(Color.parseColor("#0F172A"))
                    indicatorUnread.visibility = View.VISIBLE
                }

                // لون الحدود حسب الأولوية
                try {
                    val borderColor = Color.parseColor(notification.getColorCode())
                    cardNotification.setStrokeColor(borderColor)
                    indicatorUnread.setBackgroundColor(borderColor)
                } catch (e: Exception) {
                    // استخدام اللون الافتراضي
                }

                // شارات إضافية
                chipNotificationType.text = getTypeText(notification.type)
                chipNotificationType.visibility = View.VISIBLE

                if (notification.isNew()) {
                    chipNew.visibility = View.VISIBLE
                } else {
                    chipNew.visibility = View.GONE
                }

                if (notification.isExpired()) {
                    chipExpired.visibility = View.VISIBLE
                    cardNotification.alpha = 0.5f
                } else {
                    chipExpired.visibility = View.GONE
                }

                // أزرار الإجراءات
                if (showAdminActions) {
                    layoutAdminActions.visibility = View.VISIBLE
                    
                    btnMarkAsRead.text = if (notification.isRead) "مقروء" else "تحديد كمقروء"
                    btnMarkAsRead.isEnabled = !notification.isRead
                    
                    btnMarkAsRead.setOnClickListener {
                        onMarkAsRead(notification)
                    }
                    
                    btnDelete.setOnClickListener {
                        onDeleteClick(notification)
                    }
                } else {
                    layoutAdminActions.visibility = View.GONE
                }

                // النقر على الإشعار
                cardNotification.setOnClickListener {
                    onNotificationClick(notification)
                }

                // انيميشن ظهور
                com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(cardNotification, adapterPosition * 50L)
            }
        }

        private fun getTypeText(type: NotificationType): String {
            return when (type) {
                NotificationType.NEW_PRODUCT -> "منتج جديد"
                NotificationType.ORDER_STATUS -> "حالة الطلب"
                NotificationType.PROMOTION -> "عرض"
                NotificationType.REVIEW -> "مراجعة"
                NotificationType.STOCK_ALERT -> "تنبيه مخزون"
                NotificationType.SYSTEM -> "نظام"
                NotificationType.GENERAL -> "عام"
            }
        }
    }

    class NotificationDiffCallback : DiffUtil.ItemCallback<Notification>() {
        override fun areItemsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem == newItem
        }
    }
}

/**
 * محول مبسط للإشعارات في الشاشة الرئيسية
 */
class SimpleNotificationAdapter(
    private val onNotificationClick: (Notification) -> Unit = {}
) : ListAdapter<Notification, SimpleNotificationAdapter.SimpleNotificationViewHolder>(NotificationDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SimpleNotificationViewHolder {
        val binding = ItemNotificationBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SimpleNotificationViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SimpleNotificationViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SimpleNotificationViewHolder(
        private val binding: ItemNotificationBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(notification: Notification) {
            binding.apply {
                // إخفاء العناصر غير المطلوبة في العرض المبسط
                layoutAdminActions.visibility = View.GONE
                chipExpired.visibility = View.GONE
                ivNotificationImage.visibility = View.GONE

                // عرض المعلومات الأساسية
                tvNotificationTitle.text = notification.title
                tvNotificationMessage.text = if (notification.message.length > 50) {
                    notification.message.substring(0, 50) + "..."
                } else {
                    notification.message
                }
                tvNotificationTime.text = notification.getRelativeTime()
                tvNotificationIcon.text = notification.getIcon()

                // حالة القراءة
                if (notification.isRead) {
                    cardNotification.alpha = 0.8f
                    indicatorUnread.visibility = View.GONE
                } else {
                    cardNotification.alpha = 1.0f
                    indicatorUnread.visibility = View.VISIBLE
                }

                // نوع الإشعار
                chipNotificationType.text = getTypeText(notification.type)
                chipNotificationType.visibility = View.VISIBLE

                // شارة جديد
                chipNew.visibility = if (notification.isNew()) View.VISIBLE else View.GONE

                // النقر
                cardNotification.setOnClickListener {
                    onNotificationClick(notification)
                }
            }
        }

        private fun getTypeText(type: NotificationType): String {
            return when (type) {
                NotificationType.NEW_PRODUCT -> "منتج جديد"
                NotificationType.ORDER_STATUS -> "طلب"
                NotificationType.PROMOTION -> "عرض"
                NotificationType.REVIEW -> "مراجعة"
                NotificationType.STOCK_ALERT -> "مخزون"
                NotificationType.SYSTEM -> "نظام"
                NotificationType.GENERAL -> "عام"
            }
        }
    }

    class NotificationDiffCallback : DiffUtil.ItemCallback<Notification>() {
        override fun areItemsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Notification, newItem: Notification): Boolean {
            return oldItem == newItem
        }
    }
}
