package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemCartBinding
import com.kahrabaiat.amer.models.CartItem

class CartAdapter(
    private val onQuantityChanged: (Int, Int) -> Unit,
    private val onRemoveItem: (Int) -> Unit
) : ListAdapter<CartItem, CartAdapter.CartViewHolder>(CartDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CartViewHolder {
        val binding = ItemCartBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CartViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CartViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CartViewHolder(
        private val binding: ItemCartBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(cartItem: CartItem) {
            binding.apply {
                val product = cartItem.product

                // Product info
                tvProductName.text = product.name
                tvProductPrice.text = product.getFormattedPrice()
                tvQuantity.text = cartItem.quantity.toString()
                tvTotalPrice.text = "المجموع: ${cartItem.getFormattedTotalPrice()}"

                // Product image
                if (product.imageUrl.isNotEmpty()) {
                    Glide.with(itemView.context)
                        .load(product.imageUrl)
                        .placeholder(R.drawable.ic_product_placeholder)
                        .error(R.drawable.ic_product_placeholder)
                        .into(ivProductImage)
                } else {
                    ivProductImage.setImageResource(R.drawable.ic_product_placeholder)
                }

                // Quantity controls
                btnDecrease.setOnClickListener {
                    val currentQuantity = cartItem.quantity
                    val newQuantity = currentQuantity - 1
                    if (newQuantity > 0) {
                        // Update quantity immediately in UI
                        tvQuantity.text = newQuantity.toString()
                        onQuantityChanged(product.id, newQuantity)
                    } else {
                        onRemoveItem(product.id)
                    }
                }

                btnIncrease.setOnClickListener {
                    val currentQuantity = cartItem.quantity
                    val newQuantity = currentQuantity + 1
                    if (newQuantity <= product.stock) {
                        // Update quantity immediately in UI
                        tvQuantity.text = newQuantity.toString()
                        onQuantityChanged(product.id, newQuantity)
                    } else {
                        showStockLimitMessage()
                    }
                }

                // Remove button
                btnRemove.setOnClickListener {
                    onRemoveItem(product.id)
                }

                // Enable/disable increase button based on stock
                btnIncrease.isEnabled = cartItem.quantity < product.stock
                btnIncrease.alpha = if (btnIncrease.isEnabled) 1.0f else 0.5f
            }
        }

        private fun showStockLimitMessage() {
            android.widget.Toast.makeText(
                itemView.context,
                "لا يمكن إضافة أكثر من الكمية المتوفرة",
                android.widget.Toast.LENGTH_SHORT
            ).show()
        }
    }

    private class CartDiffCallback : DiffUtil.ItemCallback<CartItem>() {
        override fun areItemsTheSame(oldItem: CartItem, newItem: CartItem): Boolean {
            return oldItem.product.id == newItem.product.id
        }

        override fun areContentsTheSame(oldItem: CartItem, newItem: CartItem): Boolean {
            return oldItem == newItem
        }
    }
}
