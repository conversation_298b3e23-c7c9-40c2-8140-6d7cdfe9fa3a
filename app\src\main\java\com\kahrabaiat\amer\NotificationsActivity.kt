package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.NotificationAdapter
import com.kahrabaiat.amer.databinding.ActivityNotificationsBinding
import com.kahrabaiat.amer.models.Notification
import com.kahrabaiat.amer.utils.AppNotificationManager
import kotlinx.coroutines.launch

class NotificationsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityNotificationsBinding
    private lateinit var notificationManager: AppNotificationManager
    private lateinit var notificationAdapter: NotificationAdapter
    private var isAdminMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNotificationsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getIntentData()
        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadNotifications()
    }

    private fun getIntentData() {
        isAdminMode = intent.getBooleanExtra("admin_mode", false)
    }

    private fun initializeComponents() {
        notificationManager = AppNotificationManager.getInstance(this)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = if (isAdminMode) "إدارة الإشعارات" else "الإشعارات"
        }
    }

    private fun setupRecyclerView() {
        notificationAdapter = NotificationAdapter(
            onNotificationClick = { notification ->
                handleNotificationClick(notification)
            },
            onMarkAsRead = { notification ->
                markNotificationAsRead(notification)
            },
            onDeleteClick = { notification ->
                deleteNotification(notification)
            },
            showAdminActions = isAdminMode
        )

        binding.rvNotifications.apply {
            layoutManager = LinearLayoutManager(this@NotificationsActivity)
            adapter = notificationAdapter
        }
    }

    private fun setupClickListeners() {
        binding.fabAddNotification.setOnClickListener {
            if (isAdminMode) {
                // TODO: إنشاء CreateNotificationActivity لاحقاً
                android.widget.Toast.makeText(this, "سيتم إضافة صفحة إنشاء الإشعارات قريباً", android.widget.Toast.LENGTH_SHORT).show()
            }
        }

        binding.btnMarkAllAsRead.setOnClickListener {
            markAllAsRead()
        }

        binding.btnRefresh.setOnClickListener {
            loadNotifications()
        }

        // إظهار زر الإضافة للمدير فقط
        binding.fabAddNotification.visibility = if (isAdminMode) View.VISIBLE else View.GONE
    }

    private fun loadNotifications() {
        showLoading()

        lifecycleScope.launch {
            try {
                val notifications = notificationManager.getAllNotifications()
                showNotifications(notifications)
            } catch (e: Exception) {
                showError("خطأ في تحميل الإشعارات: ${e.message}")
            }
        }
    }

    private fun showNotifications(notifications: List<Notification>) {
        binding.progressBar.visibility = View.GONE
        
        if (notifications.isNotEmpty()) {
            binding.rvNotifications.visibility = View.VISIBLE
            binding.layoutEmpty.visibility = View.GONE
            notificationAdapter.submitList(notifications)
            
            // إظهار إحصائيات
            updateStats(notifications)
        } else {
            binding.rvNotifications.visibility = View.GONE
            binding.layoutEmpty.visibility = View.VISIBLE
        }
    }

    private fun updateStats(notifications: List<Notification>) {
        val unreadCount = notifications.count { !it.isRead }
        val totalCount = notifications.size
        
        binding.tvNotificationStats.text = "الإجمالي: $totalCount | غير مقروء: $unreadCount"
        binding.tvNotificationStats.visibility = View.VISIBLE
        
        // إظهار زر "تحديد الكل كمقروء" إذا كان هناك إشعارات غير مقروءة
        binding.btnMarkAllAsRead.visibility = if (unreadCount > 0) View.VISIBLE else View.GONE
    }

    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.rvNotifications.visibility = View.GONE
        binding.layoutEmpty.visibility = View.GONE
    }

    private fun showError(message: String) {
        binding.progressBar.visibility = View.GONE
        binding.layoutEmpty.visibility = View.VISIBLE
        binding.tvEmptyMessage.text = message
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun handleNotificationClick(notification: Notification) {
        // تحديد الإشعار كمقروء
        if (!notification.isRead) {
            markNotificationAsRead(notification)
        }

        // التنقل حسب نوع الإشعار
        when {
            notification.actionUrl.startsWith("product_detail/") -> {
                val productId = notification.relatedId
                // فتح تفاصيل المنتج
                navigateToProduct(productId)
            }
            notification.actionUrl.startsWith("order_detail/") -> {
                val orderId = notification.relatedId
                // فتح تفاصيل الطلب
                navigateToOrder(orderId)
            }
            notification.actionUrl == "promotions" -> {
                // فتح صفحة العروض
                navigateToPromotions()
            }
            else -> {
                // إجراء افتراضي
                android.widget.Toast.makeText(this, "تم النقر على: ${notification.title}", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun markNotificationAsRead(notification: Notification) {
        lifecycleScope.launch {
            try {
                // تحديث حالة القراءة في Firebase
                // للآن سنكتفي بالتحديث المحلي
                val updatedNotification = notification.copy(isRead = true)
                
                // تحديث القائمة
                val currentList = notificationAdapter.currentList.toMutableList()
                val index = currentList.indexOfFirst { it.id == notification.id }
                if (index != -1) {
                    currentList[index] = updatedNotification
                    notificationAdapter.submitList(currentList)
                    updateStats(currentList)
                }
                
                android.widget.Toast.makeText(this@NotificationsActivity, "تم تحديد الإشعار كمقروء", android.widget.Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                android.widget.Toast.makeText(this@NotificationsActivity, "خطأ في تحديث الإشعار", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun deleteNotification(notification: Notification) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("حذف الإشعار")
            .setMessage("هل أنت متأكد من حذف هذا الإشعار؟")
            .setPositiveButton("حذف") { _, _ ->
                performDeleteNotification(notification)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun performDeleteNotification(notification: Notification) {
        lifecycleScope.launch {
            try {
                // حذف من Firebase
                // للآن سنكتفي بالحذف المحلي
                val currentList = notificationAdapter.currentList.toMutableList()
                currentList.removeAll { it.id == notification.id }
                notificationAdapter.submitList(currentList)
                updateStats(currentList)
                
                android.widget.Toast.makeText(this@NotificationsActivity, "تم حذف الإشعار", android.widget.Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                android.widget.Toast.makeText(this@NotificationsActivity, "خطأ في حذف الإشعار", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun markAllAsRead() {
        lifecycleScope.launch {
            try {
                val currentList = notificationAdapter.currentList.map { it.copy(isRead = true) }
                notificationAdapter.submitList(currentList)
                updateStats(currentList)
                
                android.widget.Toast.makeText(this@NotificationsActivity, "تم تحديد جميع الإشعارات كمقروءة", android.widget.Toast.LENGTH_SHORT).show()
            } catch (e: Exception) {
                android.widget.Toast.makeText(this@NotificationsActivity, "خطأ في تحديث الإشعارات", android.widget.Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun navigateToProduct(productId: String) {
        // TODO: تنفيذ التنقل لتفاصيل المنتج
        android.widget.Toast.makeText(this, "فتح المنتج: $productId", android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun navigateToOrder(orderId: String) {
        // TODO: تنفيذ التنقل لتفاصيل الطلب
        android.widget.Toast.makeText(this, "فتح الطلب: $orderId", android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun navigateToPromotions() {
        // TODO: تنفيذ التنقل لصفحة العروض
        android.widget.Toast.makeText(this, "فتح صفحة العروض", android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onResume() {
        super.onResume()
        // إعادة تحميل الإشعارات عند العودة للنشاط
        loadNotifications()
    }
}
