package com.kahrabaiat.amer

import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.kahrabaiat.amer.databinding.ActivityInventorySettingsBinding
import com.kahrabaiat.amer.utils.InventoryManager
import com.kahrabaiat.amer.utils.NotificationHelper
import kotlinx.coroutines.launch

class InventorySettingsActivity : BaseAdminActivity() {

    private lateinit var binding: ActivityInventorySettingsBinding
    private lateinit var inventoryManager: InventoryManager
    private lateinit var notificationHelper: NotificationHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityInventorySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        setupToolbar()
        loadCurrentSettings()
        setupClickListeners()
    }

    private fun initializeComponents() {
        inventoryManager = InventoryManager.getInstance(this)
        notificationHelper = NotificationHelper(this)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "إعدادات المخزون"
        }
    }

    private fun loadCurrentSettings() {
        // تحميل الإعدادات الحالية
        binding.etLowStockThreshold.setText(inventoryManager.getLowStockThreshold().toString())
        binding.etCriticalStockThreshold.setText(inventoryManager.getCriticalStockThreshold().toString())
        binding.switchNotifications.isChecked = inventoryManager.areNotificationsEnabled()

        // عرض معلومات آخر فحص
        val lastCheckTime = inventoryManager.getLastCheckTime()
        if (lastCheckTime > 0) {
            val dateFormat = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
            binding.tvLastCheck.text = "آخر فحص: ${dateFormat.format(java.util.Date(lastCheckTime))}"
        } else {
            binding.tvLastCheck.text = "لم يتم فحص المخزون بعد"
        }

        // عرض تقرير المخزون
        displayInventoryReport()
    }

    private fun setupClickListeners() {
        binding.btnSaveSettings.setOnClickListener {
            saveSettings()
        }

        binding.btnCheckNow.setOnClickListener {
            checkInventoryNow()
        }

        binding.btnTestNotification.setOnClickListener {
            testNotification()
        }

        binding.switchNotifications.setOnCheckedChangeListener { _, isChecked ->
            inventoryManager.setNotificationsEnabled(isChecked)
            notificationHelper.setInventoryNotificationEnabled(isChecked)
            showToast(if (isChecked) "تم تفعيل إشعارات المخزون" else "تم إلغاء تفعيل إشعارات المخزون")
        }
    }

    private fun saveSettings() {
        try {
            val lowStockThreshold = binding.etLowStockThreshold.text.toString().toIntOrNull()
            val criticalStockThreshold = binding.etCriticalStockThreshold.text.toString().toIntOrNull()

            if (lowStockThreshold == null || lowStockThreshold < 1) {
                binding.etLowStockThreshold.error = "يرجى إدخال رقم صحيح أكبر من 0"
                return
            }

            if (criticalStockThreshold == null || criticalStockThreshold < 1) {
                binding.etCriticalStockThreshold.error = "يرجى إدخال رقم صحيح أكبر من 0"
                return
            }

            if (criticalStockThreshold >= lowStockThreshold) {
                binding.etCriticalStockThreshold.error = "الحد الحرج يجب أن يكون أقل من الحد المنخفض"
                return
            }

            // حفظ الإعدادات
            inventoryManager.setLowStockThreshold(lowStockThreshold)
            inventoryManager.setCriticalStockThreshold(criticalStockThreshold)

            showToast("تم حفظ الإعدادات بنجاح")

            // إعادة فحص المخزون بالإعدادات الجديدة
            checkInventoryNow()

        } catch (e: Exception) {
            showToast("خطأ في حفظ الإعدادات: ${e.message}")
        }
    }

    private fun checkInventoryNow() {
        lifecycleScope.launch {
            try {
                binding.btnCheckNow.isEnabled = false
                binding.btnCheckNow.text = "جاري الفحص..."

                // فحص المخزون
                val firebaseHelper = com.kahrabaiat.amer.utils.FirebaseHelper()
                val products = firebaseHelper.getAllProducts()
                inventoryManager.checkInventoryStatus(products)

                // تحديث العرض
                loadCurrentSettings()
                displayInventoryReport()

                showToast("تم فحص المخزون بنجاح")

            } catch (e: Exception) {
                showToast("خطأ في فحص المخزون: ${e.message}")
            } finally {
                binding.btnCheckNow.isEnabled = true
                binding.btnCheckNow.text = "فحص الآن"
            }
        }
    }

    private fun testNotification() {
        if (!inventoryManager.areNotificationsEnabled()) {
            showToast("الإشعارات معطلة. يرجى تفعيلها أولاً")
            return
        }

        // إنشاء منتج تجريبي لاختبار الإشعار
        val testProduct = com.kahrabaiat.amer.models.Product(
            id = 999,
            name = "منتج تجريبي",
            price = 100.0,
            description = "منتج لاختبار الإشعارات",
            imageUrl = "",
            category = "test",
            stock = 0,
            available = true
        )

        notificationHelper.sendOutOfStockNotification(listOf(testProduct))
        showToast("تم إرسال إشعار تجريبي")
    }

    private fun displayInventoryReport() {
        val report = inventoryManager.getInventoryReport()

        binding.tvLowStockCount.text = "منتجات مخزون منخفض: ${report.lowStockCount}"
        binding.tvOutOfStockCount.text = "منتجات نفد مخزونها: ${report.outOfStockCount}"

        // تلوين النصوص حسب الحالة
        binding.tvLowStockCount.setTextColor(
            if (report.lowStockCount > 0)
                getColor(com.kahrabaiat.amer.R.color.warning_yellow)
            else
                getColor(com.kahrabaiat.amer.R.color.success_green)
        )

        binding.tvOutOfStockCount.setTextColor(
            if (report.outOfStockCount > 0)
                getColor(com.kahrabaiat.amer.R.color.error_red)
            else
                getColor(com.kahrabaiat.amer.R.color.success_green)
        )
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
