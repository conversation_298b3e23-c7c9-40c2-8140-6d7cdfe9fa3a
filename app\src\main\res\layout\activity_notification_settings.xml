<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".NotificationSettingsActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Header -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="إعدادات الإشعارات"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:text="تحكم في أنواع الإشعارات التي تريد استقبالها"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- Notification Types -->
            
            <!-- New Orders -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/circle_background_primary"
                            android:padding="12dp"
                            android:src="@drawable/ic_notification"
                            android:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="📦 الطلبات الجديدة"
                                android:textColor="@color/text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvNewOrdersSummary"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="سيتم إرسال إشعار عند وصول طلب جديد"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchNewOrders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Updates -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/circle_background_accent"
                            android:padding="12dp"
                            android:src="@drawable/ic_update"
                            android:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="🔄 تحديثات الطلبات"
                                android:textColor="@color/text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvOrderUpdatesSummary"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="سيتم إرسال إشعار عند تغيير حالة الطلب"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchOrderUpdates"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- General Notifications -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:layout_marginEnd="16dp"
                            android:background="@drawable/circle_background_success"
                            android:padding="12dp"
                            android:src="@drawable/ic_info"
                            android:tint="@color/white" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="ℹ️ الإشعارات العامة"
                                android:textColor="@color/text_primary"
                                android:textSize="18sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvGeneralSummary"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="سيتم إرسال الإشعارات العامة والتذكيرات"
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                        </LinearLayout>

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switchGeneral"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center_vertical"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Test Notifications Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="اختبار الإشعارات"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

            <!-- Test Buttons -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="اختبر الإشعارات للتأكد من عملها"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnTestNewOrder"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="اختبار إشعار طلب جديد"
                            app:icon="@drawable/ic_notification"
                            app:iconGravity="textStart" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnTestStatusUpdate"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="اختبار إشعار تحديث الحالة"
                            app:icon="@drawable/ic_update"
                            app:iconGravity="textStart" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnTestGeneral"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="اختبار إشعار عام"
                            app:icon="@drawable/ic_info"
                            app:iconGravity="textStart" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- System Settings Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="إعدادات النظام"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold" />

            <!-- System Settings Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="إدارة إعدادات الإشعارات على مستوى النظام"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnSystemSettings"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:text="إعدادات النظام"
                            app:icon="@drawable/ic_settings"
                            app:iconGravity="textStart" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnClearAll"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:text="مسح الكل"
                            app:icon="@drawable/ic_clear"
                            app:iconGravity="textStart" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
