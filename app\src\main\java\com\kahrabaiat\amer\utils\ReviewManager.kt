package com.kahrabaiat.amer.utils

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.models.ProductReviewStats
import com.kahrabaiat.amer.models.Review
import kotlinx.coroutines.tasks.await

/**
 * مدير المراجعات والتقييمات
 */
class ReviewManager private constructor(private val context: Context) {
    
    private val firestore = FirebaseFirestore.getInstance()
    
    companion object {
        private const val TAG = "ReviewManager"
        private const val REVIEWS_COLLECTION = "reviews"
        
        @Volatile
        private var INSTANCE: ReviewManager? = null
        
        fun getInstance(context: Context): ReviewManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: ReviewManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    /**
     * إضافة مراجعة جديدة
     */
    suspend fun addReview(review: Review): Boolean {
        return try {
            if (!review.isValid()) {
                Log.w(TAG, "Invalid review data")
                return false
            }

            val reviewData = hashMapOf(
                "productId" to review.productId,
                "customerName" to review.customerName,
                "customerPhone" to review.customerPhone,
                "rating" to review.rating,
                "comment" to review.comment,
                "reviewDate" to review.reviewDate,
                "isVerifiedPurchase" to review.isVerifiedPurchase,
                "isApproved" to review.isApproved,
                "helpfulCount" to review.helpfulCount,
                "reportCount" to review.reportCount
            )

            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(REVIEWS_COLLECTION)
                    .document(review.id)
                    .set(reviewData)
                    .await()
            }

            Log.i(TAG, "Review added successfully: ${review.id}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding review", e)
            false
        }
    }

    /**
     * الحصول على جميع مراجعات منتج معين
     */
    suspend fun getProductReviews(productId: Int): List<Review> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(REVIEWS_COLLECTION)
                    .whereEqualTo("productId", productId)
                    .whereEqualTo("isApproved", true)
                    .orderBy("reviewDate", Query.Direction.DESCENDING)
                    .get()
                    .await()

                snapshot.documents.mapNotNull { doc ->
                    try {
                        Review(
                            id = doc.id,
                            productId = doc.getLong("productId")?.toInt() ?: 0,
                            customerName = doc.getString("customerName") ?: "",
                            customerPhone = doc.getString("customerPhone") ?: "",
                            rating = doc.getDouble("rating")?.toFloat() ?: 0f,
                            comment = doc.getString("comment") ?: "",
                            reviewDate = doc.getLong("reviewDate") ?: 0L,
                            isVerifiedPurchase = doc.getBoolean("isVerifiedPurchase") ?: false,
                            isApproved = doc.getBoolean("isApproved") ?: true,
                            helpfulCount = doc.getLong("helpfulCount")?.toInt() ?: 0,
                            reportCount = doc.getLong("reportCount")?.toInt() ?: 0
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing review document", e)
                        null
                    }
                }
            } else {
                // إرجاع مراجعات تجريبية للاختبار
                getSampleReviews(productId)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting product reviews", e)
            emptyList()
        }
    }

    /**
     * حساب إحصائيات المراجعات للمنتج
     */
    suspend fun getProductReviewStats(productId: Int): ProductReviewStats {
        return try {
            val reviews = getProductReviews(productId)
            val approvedReviews = reviews.filter { it.isApproved }
            
            if (approvedReviews.isEmpty()) {
                return ProductReviewStats(productId = productId)
            }

            val averageRating = Review.calculateAverageRating(approvedReviews)
            val ratingDistribution = Review.calculateRatingDistribution(approvedReviews)
            val verifiedPurchaseCount = approvedReviews.count { it.isVerifiedPurchase }
            val recentReviewsCount = approvedReviews.count { it.isRecent() }

            ProductReviewStats(
                productId = productId,
                totalReviews = approvedReviews.size,
                averageRating = averageRating,
                ratingDistribution = ratingDistribution,
                verifiedPurchaseCount = verifiedPurchaseCount,
                recentReviewsCount = recentReviewsCount
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error calculating review stats", e)
            ProductReviewStats(productId = productId)
        }
    }

    /**
     * تحديث عدد الإعجابات للمراجعة
     */
    suspend fun markReviewAsHelpful(reviewId: String): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(REVIEWS_COLLECTION)
                    .document(reviewId)
                    .update("helpfulCount", com.google.firebase.firestore.FieldValue.increment(1))
                    .await()
            }
            
            Log.i(TAG, "Review marked as helpful: $reviewId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error marking review as helpful", e)
            false
        }
    }

    /**
     * الإبلاغ عن مراجعة
     */
    suspend fun reportReview(reviewId: String, reason: String): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(REVIEWS_COLLECTION)
                    .document(reviewId)
                    .update("reportCount", com.google.firebase.firestore.FieldValue.increment(1))
                    .await()
                
                // يمكن إضافة منطق لإخفاء المراجعة إذا تم الإبلاغ عنها كثيراً
                // أو إرسال تنبيه للمدير
            }
            
            Log.i(TAG, "Review reported: $reviewId, reason: $reason")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error reporting review", e)
            false
        }
    }

    /**
     * حذف مراجعة (للمدير فقط)
     */
    suspend fun deleteReview(reviewId: String): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(REVIEWS_COLLECTION)
                    .document(reviewId)
                    .delete()
                    .await()
            }
            
            Log.i(TAG, "Review deleted: $reviewId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting review", e)
            false
        }
    }

    /**
     * الموافقة على مراجعة أو رفضها (للمدير فقط)
     */
    suspend fun approveReview(reviewId: String, isApproved: Boolean): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(REVIEWS_COLLECTION)
                    .document(reviewId)
                    .update("isApproved", isApproved)
                    .await()
            }
            
            Log.i(TAG, "Review approval updated: $reviewId, approved: $isApproved")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating review approval", e)
            false
        }
    }

    /**
     * الحصول على جميع المراجعات (للمدير فقط)
     */
    suspend fun getAllReviews(): List<Review> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(REVIEWS_COLLECTION)
                    .orderBy("reviewDate", Query.Direction.DESCENDING)
                    .get()
                    .await()

                snapshot.documents.mapNotNull { doc ->
                    try {
                        Review(
                            id = doc.id,
                            productId = doc.getLong("productId")?.toInt() ?: 0,
                            customerName = doc.getString("customerName") ?: "",
                            customerPhone = doc.getString("customerPhone") ?: "",
                            rating = doc.getDouble("rating")?.toFloat() ?: 0f,
                            comment = doc.getString("comment") ?: "",
                            reviewDate = doc.getLong("reviewDate") ?: 0L,
                            isVerifiedPurchase = doc.getBoolean("isVerifiedPurchase") ?: false,
                            isApproved = doc.getBoolean("isApproved") ?: true,
                            helpfulCount = doc.getLong("helpfulCount")?.toInt() ?: 0,
                            reportCount = doc.getLong("reportCount")?.toInt() ?: 0
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing review document", e)
                        null
                    }
                }
            } else {
                // إرجاع مراجعات تجريبية
                getAllSampleReviews()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting all reviews", e)
            emptyList()
        }
    }

    /**
     * التحقق من وجود مراجعة للعميل على منتج معين
     */
    suspend fun hasCustomerReviewedProduct(productId: Int, customerPhone: String): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(REVIEWS_COLLECTION)
                    .whereEqualTo("productId", productId)
                    .whereEqualTo("customerPhone", customerPhone)
                    .limit(1)
                    .get()
                    .await()

                !snapshot.isEmpty
            } else {
                false // للاختبار، السماح بمراجعات متعددة
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking customer review", e)
            false
        }
    }

    /**
     * مراجعات تجريبية للاختبار
     */
    private fun getSampleReviews(productId: Int): List<Review> {
        return listOf(
            Review.create(
                productId = productId,
                customerName = "أحمد محمد",
                customerPhone = "07901234567",
                rating = 5f,
                comment = "منتج ممتاز جداً، جودة عالية وسعر مناسب. أنصح بشرائه بقوة.",
                isVerifiedPurchase = true
            ),
            Review.create(
                productId = productId,
                customerName = "فاطمة علي",
                customerPhone = "07801234567",
                rating = 4f,
                comment = "جيد جداً ولكن التوصيل كان متأخر قليلاً. المنتج نفسه ممتاز.",
                isVerifiedPurchase = true
            ),
            Review.create(
                productId = productId,
                customerName = "محمد حسن",
                customerPhone = "07701234567",
                rating = 4.5f,
                comment = "راضي جداً عن المنتج، يعمل بكفاءة عالية ومطابق للوصف.",
                isVerifiedPurchase = false
            )
        )
    }

    private fun getAllSampleReviews(): List<Review> {
        val reviews = mutableListOf<Review>()
        for (productId in 1..10) {
            reviews.addAll(getSampleReviews(productId))
        }
        return reviews
    }
}
