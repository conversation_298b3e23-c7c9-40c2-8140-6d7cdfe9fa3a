<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/ripple_white">
    
    <item>
        <layer-list>
            <!-- Shadow -->
            <item android:top="2dp" android:left="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#30000000" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            
            <!-- Button Background -->
            <item android:bottom="2dp" android:right="2dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:angle="135"
                        android:startColor="@color/gradient_start_blue"
                        android:centerColor="#1E88E5"
                        android:endColor="@color/gradient_end_blue"
                        android:type="linear" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
</ripple>
