package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.ProductAdapter
import com.kahrabaiat.amer.databinding.ActivityMainBinding
import com.kahrabaiat.amer.models.CategoryConstants
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.CartManager
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.utils.DataManager
import com.kahrabaiat.amer.utils.AppNotificationManager
import kotlinx.coroutines.launch

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private lateinit var cartManager: CartManager
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var productAdapter: ProductAdapter
    private lateinit var dataManager: DataManager
    private lateinit var notificationManager: AppNotificationManager

    companion object {
        private const val CART_REQUEST_CODE = 1001
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        setupRecyclerViews()
        setupClickListeners()
        initializeFirebase()
        loadData()
        updateCartBadge()
        updateNotificationBadge()

        // انيميشن ظهور العناصر
        animateUIElements()
    }

    override fun onResume() {
        super.onResume()
        // تحديث البيانات عند العودة للشاشة الرئيسية
        loadData()
        updateCartBadge()
        updateNotificationBadge()

        // فحص المخزون في الخلفية
        checkInventoryInBackground()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CART_REQUEST_CODE) {
            // Update cart badge when returning from cart
            updateCartBadge()
        }
    }

    private fun initializeComponents() {
        cartManager = CartManager.getInstance(this)
        databaseHelper = DatabaseHelper.getInstance(this)
        dataManager = DataManager.getInstance(this)
        notificationManager = AppNotificationManager.getInstance(this)
    }

    private fun initializeFirebase() {
        lifecycleScope.launch {
            try {
                dataManager.initializeData()
                android.util.Log.d("MainActivity", "Data initialized successfully")
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error initializing data", e)
            }
        }
    }

    private fun setupRecyclerViews() {
        // Products RecyclerView only (categories are now static cards)

        // Products RecyclerView
        productAdapter = ProductAdapter(
            onProductClick = { product ->
                val intent = Intent(this, ProductDetailActivity::class.java)
                intent.putExtra("product", product)
                startActivity(intent)
            },
            onAddToCartClick = { product ->
                cartManager.addToCart(product)
                updateCartBadge()
                showAddToCartMessage(product.name)
            }
        )

        binding.rvLatestProducts.apply {
            layoutManager = GridLayoutManager(this@MainActivity, 2)
            adapter = productAdapter
        }
    }

    private fun setupClickListeners() {
        // Cart click
        binding.ivCart.setOnClickListener {
            val intent = Intent(this, CartActivity::class.java)
            startActivityForResult(intent, CART_REQUEST_CODE)
        }

        // Notifications click
        binding.ivNotifications.setOnClickListener {
            val intent = Intent(this, NotificationsActivity::class.java)
            startActivity(intent)
        }

        // Admin click with error handling
        binding.ivAdmin.setOnClickListener {
            try {
                val intent = Intent(this, AdminLoginActivity::class.java)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(intent)
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error opening admin login", e)
                android.widget.Toast.makeText(this, "حدث خطأ في فتح تسجيل دخول المدير", android.widget.Toast.LENGTH_SHORT).show()
            }
        }

        // View all products click
        binding.tvViewAllProducts.setOnClickListener {
            startActivity(Intent(this, ProductsActivity::class.java))
        }

        // Category clicks with animations
        binding.cardHandTools.setOnClickListener {
            com.kahrabaiat.amer.ui.AnimationUtils.animateCardClick(it) {
                openCategoryProducts(CategoryConstants.HAND_TOOLS)
            }
        }

        binding.cardHomeAppliances.setOnClickListener {
            com.kahrabaiat.amer.ui.AnimationUtils.animateCardClick(it) {
                openCategoryProducts(CategoryConstants.HOME_APPLIANCES)
            }
        }

        binding.cardElectricalAppliances.setOnClickListener {
            com.kahrabaiat.amer.ui.AnimationUtils.animateCardClick(it) {
                openCategoryProducts(CategoryConstants.ELECTRICAL_APPLIANCES)
            }
        }

        // Special offers/coupons click
        binding.layoutSpecialOffers.setOnClickListener {
            com.kahrabaiat.amer.ui.AnimationUtils.animateCardClick(it) {
                val intent = Intent(this, CouponsActivity::class.java)
                startActivity(intent)
            }
        }

        // Search functionality
        binding.etSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                val query = s.toString().trim()
                if (query.isNotEmpty()) {
                    searchProducts(query)
                } else {
                    loadLatestProducts()
                }
            }
        })
    }

    private fun loadData() {
        loadLatestProducts()
        loadSpecialOffers()
    }

    private fun loadLatestProducts() {
        lifecycleScope.launch {
            try {
                val products = dataManager.getLatestProducts(10)
                productAdapter.submitList(products)
            } catch (e: Exception) {
                // Handle error - show toast or error message
                android.util.Log.e("MainActivity", "Error loading products", e)
            }
        }
    }

    private fun loadSpecialOffers() {
        lifecycleScope.launch {
            try {
                val discountedProducts = databaseHelper.getAllProducts().filter { it.discount > 0 }
                if (discountedProducts.isNotEmpty()) {
                    binding.layoutSpecialOffers.visibility = View.VISIBLE
                    // Setup ViewPager for special offers
                    // Implementation for special offers slider
                } else {
                    binding.layoutSpecialOffers.visibility = View.GONE
                }
            } catch (e: Exception) {
                binding.layoutSpecialOffers.visibility = View.GONE
            }
        }
    }

    private fun searchProducts(query: String) {
        lifecycleScope.launch {
            try {
                val products = databaseHelper.getAllProducts().filter { product ->
                    product.name.contains(query, ignoreCase = true) ||
                    product.description.contains(query, ignoreCase = true)
                }
                productAdapter.submitList(products)
            } catch (e: Exception) {
                // Handle error
                android.util.Log.e("MainActivity", "Error searching products", e)
            }
        }
    }

    private fun updateCartBadge() {
        val itemCount = cartManager.getCartItemCount()
        if (itemCount > 0) {
            binding.tvCartCount.visibility = View.VISIBLE
            binding.tvCartCount.text = itemCount.toString()
        } else {
            binding.tvCartCount.visibility = View.GONE
        }
    }

    private fun updateNotificationBadge() {
        lifecycleScope.launch {
            try {
                val notifications = notificationManager.getAllNotifications(10)
                val unreadCount = notifications.count { !it.isRead }

                if (unreadCount > 0) {
                    binding.tvNotificationCount.text = unreadCount.toString()
                    binding.tvNotificationCount.visibility = View.VISIBLE
                } else {
                    binding.tvNotificationCount.visibility = View.GONE
                }
            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error updating notification badge", e)
                binding.tvNotificationCount.visibility = View.GONE
            }
        }
    }

    private fun showAddToCartMessage(productName: String) {
        // Show a toast or snackbar message
        android.widget.Toast.makeText(
            this,
            "تم إضافة $productName إلى السلة",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    private fun openCategoryProducts(category: String) {
        val intent = Intent(this, ProductsActivity::class.java)
        intent.putExtra("category", category)
        startActivity(intent)
    }

    /**
     * فحص المخزون في الخلفية
     */
    private fun checkInventoryInBackground() {
        lifecycleScope.launch {
            try {
                val inventoryManager = com.kahrabaiat.amer.utils.InventoryManager.getInstance(this@MainActivity)

                // فحص المخزون فقط إذا مر وقت كافي على آخر فحص (ساعة واحدة)
                val lastCheckTime = inventoryManager.getLastCheckTime()
                val currentTime = System.currentTimeMillis()
                val oneHour = 60 * 60 * 1000L

                if (currentTime - lastCheckTime > oneHour) {
                    val products = databaseHelper.getAllProducts()
                    inventoryManager.checkInventoryStatus(products)

                    android.util.Log.d("MainActivity", "Inventory check completed in background")
                }

            } catch (e: Exception) {
                android.util.Log.e("MainActivity", "Error in background inventory check", e)
            }
        }
    }

    /**
     * انيميشن ظهور عناصر الواجهة
     */
    private fun animateUIElements() {
        // انيميشن كروت الفئات
        com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(binding.cardHandTools, 100)
        com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(binding.cardHomeAppliances, 200)
        com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(binding.cardElectricalAppliances, 300)

        // انيميشن قسم المنتجات
        com.kahrabaiat.amer.ui.AnimationUtils.slideUpFromBottom(binding.rvLatestProducts, 400)

        // انيميشن شارة السلة إذا كانت تحتوي على عناصر
        if (cartManager.getCartItemCount() > 0) {
            com.kahrabaiat.amer.ui.AnimationUtils.pulseBadge(binding.tvCartCount)
        }
    }
}
