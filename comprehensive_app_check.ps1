# 🔍 فحص شامل لتطبيق كهربائيات عامر
# يتحقق من سلامة جميع المكونات والتكامل بينها

Write-Host "🔍 بدء الفحص الشامل لتطبيق كهربائيات عامر..." -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Cyan

# 1. فحص الملفات الأساسية
Write-Host "`n📁 فحص الملفات الأساسية..." -ForegroundColor Yellow

$coreFiles = @(
    "app/src/main/AndroidManifest.xml",
    "app/src/main/java/com/kahrabaiat/amer/KahrabaiatAmerApplication.kt",
    "app/src/main/java/com/kahrabaiat/amer/MainActivity.kt",
    "app/build.gradle",
    "build.gradle"
)

$missingFiles = @()
foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file مفقود!" -ForegroundColor Red
        $missingFiles += $file
    }
}

# 2. فحص Activities المطلوبة
Write-Host "`n🎯 فحص Activities..." -ForegroundColor Yellow

$activities = @(
    "app/src/main/java/com/kahrabaiat/amer/MainActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/ProductsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/ProductDetailActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/CartActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/CheckoutActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/AdminLoginActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/AdminActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/AddProductActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/ManageProductsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/OrdersActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/NotificationSettingsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/InventorySettingsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/SecuritySettingsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/PerformanceMonitorActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/CustomReportsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/AdvancedAnalyticsActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/UserManagementActivity.kt"
)

$missingActivities = @()
foreach ($activity in $activities) {
    if (Test-Path $activity) {
        Write-Host "✅ $(Split-Path $activity -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $activity -Leaf) مفقود!" -ForegroundColor Red
        $missingActivities += $activity
    }
}

# 3. فحص الكلاسات المساعدة
Write-Host "`n🛠️ فحص الكلاسات المساعدة..." -ForegroundColor Yellow

$utilityClasses = @(
    "app/src/main/java/com/kahrabaiat/amer/BaseAdminActivity.kt",
    "app/src/main/java/com/kahrabaiat/amer/utils/AuthManager.kt",
    "app/src/main/java/com/kahrabaiat/amer/utils/FirebaseHelper.kt",
    "app/src/main/java/com/kahrabaiat/amer/utils/CartManager.kt",
    "app/src/main/java/com/kahrabaiat/amer/utils/NotificationHelper.kt",
    "app/src/main/java/com/kahrabaiat/amer/security/EncryptionManager.kt",
    "app/src/main/java/com/kahrabaiat/amer/security/SecurityMonitor.kt",
    "app/src/main/java/com/kahrabaiat/amer/security/AppIntegrityChecker.kt",
    "app/src/main/java/com/kahrabaiat/amer/performance/PerformanceMonitor.kt",
    "app/src/main/java/com/kahrabaiat/amer/performance/CrashReporter.kt",
    "app/src/main/java/com/kahrabaiat/amer/network/NetworkManager.kt",
    "app/src/main/java/com/kahrabaiat/amer/cache/CacheManager.kt"
)

$missingUtilities = @()
foreach ($utility in $utilityClasses) {
    if (Test-Path $utility) {
        Write-Host "✅ $(Split-Path $utility -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $utility -Leaf) مفقود!" -ForegroundColor Red
        $missingUtilities += $utility
    }
}

# 4. فحص Adapters
Write-Host "`n📋 فحص Adapters..." -ForegroundColor Yellow

$adapters = @(
    "app/src/main/java/com/kahrabaiat/amer/adapters/ProductAdapter.kt",
    "app/src/main/java/com/kahrabaiat/amer/adapters/CartAdapter.kt",
    "app/src/main/java/com/kahrabaiat/amer/adapters/OrderAdapter.kt",
    "app/src/main/java/com/kahrabaiat/amer/adapters/SecurityEventAdapter.kt"
)

$missingAdapters = @()
foreach ($adapter in $adapters) {
    if (Test-Path $adapter) {
        Write-Host "✅ $(Split-Path $adapter -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $adapter -Leaf) مفقود!" -ForegroundColor Red
        $missingAdapters += $adapter
    }
}

# 5. فحص Models
Write-Host "`n📊 فحص Models..." -ForegroundColor Yellow

$models = @(
    "app/src/main/java/com/kahrabaiat/amer/models/Product.kt",
    "app/src/main/java/com/kahrabaiat/amer/models/CartItem.kt",
    "app/src/main/java/com/kahrabaiat/amer/models/Order.kt",
    "app/src/main/java/com/kahrabaiat/amer/models/Category.kt"
)

$missingModels = @()
foreach ($model in $models) {
    if (Test-Path $model) {
        Write-Host "✅ $(Split-Path $model -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $model -Leaf) مفقود!" -ForegroundColor Red
        $missingModels += $model
    }
}

# 6. فحص UI Components
Write-Host "`n🎨 فحص UI Components..." -ForegroundColor Yellow

$uiComponents = @(
    "app/src/main/java/com/kahrabaiat/amer/ui/FontManager.kt",
    "app/src/main/java/com/kahrabaiat/amer/ui/PerformanceOptimizer.kt",
    "app/src/main/java/com/kahrabaiat/amer/ui/ImageLoader.kt",
    "app/src/main/java/com/kahrabaiat/amer/ui/AnimationUtils.kt"
)

$missingUIComponents = @()
foreach ($component in $uiComponents) {
    if (Test-Path $component) {
        Write-Host "✅ $(Split-Path $component -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $component -Leaf) مفقود!" -ForegroundColor Red
        $missingUIComponents += $component
    }
}

# 7. فحص Layout Files
Write-Host "`n📱 فحص Layout Files..." -ForegroundColor Yellow

$layouts = @(
    "app/src/main/res/layout/activity_main.xml",
    "app/src/main/res/layout/activity_admin.xml",
    "app/src/main/res/layout/activity_admin_login.xml",
    "app/src/main/res/layout/activity_cart.xml",
    "app/src/main/res/layout/activity_checkout.xml",
    "app/src/main/res/layout/activity_security_settings.xml",
    "app/src/main/res/layout/activity_performance_monitor.xml",
    "app/src/main/res/layout/item_product.xml",
    "app/src/main/res/layout/item_cart.xml",
    "app/src/main/res/layout/item_order.xml",
    "app/src/main/res/layout/item_security_event.xml"
)

$missingLayouts = @()
foreach ($layout in $layouts) {
    if (Test-Path $layout) {
        Write-Host "✅ $(Split-Path $layout -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $layout -Leaf) مفقود!" -ForegroundColor Red
        $missingLayouts += $layout
    }
}

# 8. فحص Resource Files
Write-Host "`n🎨 فحص Resource Files..." -ForegroundColor Yellow

$resources = @(
    "app/src/main/res/values/strings.xml",
    "app/src/main/res/values/colors.xml",
    "app/src/main/res/values/themes.xml",
    "app/src/main/res/drawable/ic_app_logo_alt.xml",
    "app/src/main/res/drawable/gradient_background_main.xml",
    "app/src/main/res/drawable/button_primary_enhanced.xml"
)

$missingResources = @()
foreach ($resource in $resources) {
    if (Test-Path $resource) {
        Write-Host "✅ $(Split-Path $resource -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $resource -Leaf) مفقود!" -ForegroundColor Red
        $missingResources += $resource
    }
}

# 9. فحص Services
Write-Host "`n🔧 فحص Services..." -ForegroundColor Yellow

$services = @(
    "app/src/main/java/com/kahrabaiat/amer/services/MyFirebaseMessagingService.kt"
)

$missingServices = @()
foreach ($service in $services) {
    if (Test-Path $service) {
        Write-Host "✅ $(Split-Path $service -Leaf)" -ForegroundColor Green
    } else {
        Write-Host "❌ $(Split-Path $service -Leaf) مفقود!" -ForegroundColor Red
        $missingServices += $service
    }
}

# 10. تلخيص النتائج
Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "📊 ملخص نتائج الفحص:" -ForegroundColor Yellow

$totalFiles = $coreFiles.Count + $activities.Count + $utilityClasses.Count + $adapters.Count + $models.Count + $uiComponents.Count + $layouts.Count + $resources.Count + $services.Count
$totalMissing = $missingFiles.Count + $missingActivities.Count + $missingUtilities.Count + $missingAdapters.Count + $missingModels.Count + $missingUIComponents.Count + $missingLayouts.Count + $missingResources.Count + $missingServices.Count
$totalPresent = $totalFiles - $totalMissing

Write-Host "إجمالي الملفات المفحوصة: $totalFiles" -ForegroundColor White
Write-Host "الملفات الموجودة: $totalPresent" -ForegroundColor Green
Write-Host "الملفات المفقودة: $totalMissing" -ForegroundColor Red

if ($totalMissing -eq 0) {
    Write-Host "`n🎉 ممتاز! جميع الملفات موجودة والتطبيق مكتمل!" -ForegroundColor Green
    Write-Host "✅ التطبيق جاهز للبناء والتشغيل" -ForegroundColor Green
} else {
    Write-Host "`n⚠️ يوجد ملفات مفقودة تحتاج إلى إنشاء:" -ForegroundColor Yellow
    
    if ($missingFiles.Count -gt 0) {
        Write-Host "`nملفات أساسية مفقودة:" -ForegroundColor Red
        $missingFiles | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
    }
    
    if ($missingActivities.Count -gt 0) {
        Write-Host "`nActivities مفقودة:" -ForegroundColor Red
        $missingActivities | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingUtilities.Count -gt 0) {
        Write-Host "`nكلاسات مساعدة مفقودة:" -ForegroundColor Red
        $missingUtilities | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingAdapters.Count -gt 0) {
        Write-Host "`nAdapters مفقودة:" -ForegroundColor Red
        $missingAdapters | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingModels.Count -gt 0) {
        Write-Host "`nModels مفقودة:" -ForegroundColor Red
        $missingModels | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingUIComponents.Count -gt 0) {
        Write-Host "`nUI Components مفقودة:" -ForegroundColor Red
        $missingUIComponents | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingLayouts.Count -gt 0) {
        Write-Host "`nLayout Files مفقودة:" -ForegroundColor Red
        $missingLayouts | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingResources.Count -gt 0) {
        Write-Host "`nResource Files مفقودة:" -ForegroundColor Red
        $missingResources | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
    
    if ($missingServices.Count -gt 0) {
        Write-Host "`nServices مفقودة:" -ForegroundColor Red
        $missingServices | ForEach-Object { Write-Host "  - $(Split-Path $_ -Leaf)" -ForegroundColor Red }
    }
}

# 11. فحص build.gradle للتبعيات
Write-Host "`n🔧 فحص التبعيات في build.gradle..." -ForegroundColor Yellow

if (Test-Path "app/build.gradle") {
    $buildGradleContent = Get-Content "app/build.gradle" -Raw
    
    $requiredDependencies = @(
        "androidx.core:core-ktx",
        "androidx.appcompat:appcompat",
        "com.google.android.material:material",
        "androidx.constraintlayout:constraintlayout",
        "androidx.recyclerview:recyclerview",
        "com.google.firebase:firebase-bom",
        "com.github.bumptech.glide:glide",
        "com.google.code.gson:gson"
    )
    
    $missingDependencies = @()
    foreach ($dependency in $requiredDependencies) {
        if ($buildGradleContent -match [regex]::Escape($dependency)) {
            Write-Host "✅ $dependency" -ForegroundColor Green
        } else {
            Write-Host "❌ $dependency مفقود!" -ForegroundColor Red
            $missingDependencies += $dependency
        }
    }
    
    if ($missingDependencies.Count -eq 0) {
        Write-Host "✅ جميع التبعيات المطلوبة موجودة" -ForegroundColor Green
    }
} else {
    Write-Host "❌ ملف build.gradle غير موجود!" -ForegroundColor Red
}

# 12. اختبار البناء
Write-Host "`n🔨 اختبار بناء المشروع..." -ForegroundColor Yellow

if ($totalMissing -eq 0) {
    Write-Host "جاري تنظيف المشروع..." -ForegroundColor Cyan
    try {
        & .\gradlew.bat clean 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ تم تنظيف المشروع بنجاح" -ForegroundColor Green
            
            Write-Host "جاري بناء المشروع..." -ForegroundColor Cyan
            & .\gradlew.bat assembleDebug 2>$null
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ تم بناء المشروع بنجاح!" -ForegroundColor Green
                Write-Host "🎉 التطبيق جاهز للتشغيل!" -ForegroundColor Green
            } else {
                Write-Host "❌ فشل في بناء المشروع" -ForegroundColor Red
                Write-Host "يرجى مراجعة الأخطاء في Android Studio" -ForegroundColor Yellow
            }
        } else {
            Write-Host "❌ فشل في تنظيف المشروع" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ خطأ في تشغيل Gradle" -ForegroundColor Red
        Write-Host "تأكد من وجود gradlew.bat في المجلد الحالي" -ForegroundColor Yellow
    }
} else {
    Write-Host "⏭️ تم تخطي اختبار البناء بسبب وجود ملفات مفقودة" -ForegroundColor Yellow
}

Write-Host "`n" + "=" * 60 -ForegroundColor Cyan
Write-Host "🏁 انتهى الفحص الشامل" -ForegroundColor Green

# إحصائيات نهائية
$completionPercentage = [math]::Round(($totalPresent / $totalFiles) * 100, 1)
Write-Host "نسبة اكتمال التطبيق: $completionPercentage%" -ForegroundColor $(if ($completionPercentage -eq 100) { "Green" } elseif ($completionPercentage -gt 80) { "Yellow" } else { "Red" })

if ($completionPercentage -eq 100) {
    Write-Host "`n🎊 مبروك! التطبيق مكتمل 100% وجاهز للاستخدام!" -ForegroundColor Green
    Write-Host "يمكنك الآن:" -ForegroundColor White
    Write-Host "  1. فتح المشروع في Android Studio" -ForegroundColor Cyan
    Write-Host "  2. تشغيل التطبيق على الجهاز أو المحاكي" -ForegroundColor Cyan
    Write-Host "  3. اختبار جميع الميزات" -ForegroundColor Cyan
} elseif ($completionPercentage -gt 90) {
    Write-Host "`n✨ ممتاز! التطبيق شبه مكتمل" -ForegroundColor Green
    Write-Host "يحتاج فقط لبعض الملفات الثانوية" -ForegroundColor Yellow
} elseif ($completionPercentage -gt 70) {
    Write-Host "`n👍 جيد! معظم المكونات موجودة" -ForegroundColor Yellow
    Write-Host "يحتاج لإكمال بعض الملفات المهمة" -ForegroundColor Yellow
} else {
    Write-Host "`n⚠️ التطبيق يحتاج لمزيد من العمل" -ForegroundColor Red
    Write-Host "يرجى إنشاء الملفات المفقودة أولاً" -ForegroundColor Red
}

Write-Host "`nشكراً لاستخدام نظام الفحص الشامل! 🙏" -ForegroundColor Cyan
