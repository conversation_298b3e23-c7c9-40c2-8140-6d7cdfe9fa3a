<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardCoupon"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="6dp"
    app:strokeWidth="2dp"
    app:strokeColor="@color/accent_orange">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/rounded_background">

        <!-- Header Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center_vertical">

            <!-- Coupon Icon -->
            <TextView
                android:id="@+id/tvCouponIcon"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:text="🎫"
                android:textSize="24sp"
                android:gravity="center"
                android:background="@drawable/circle_background"
                android:backgroundTint="@color/accent_light"
                android:layout_marginEnd="12dp" />

            <!-- Title and Value -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCouponTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="عنوان الكوبون"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:maxLines="2"
                    android:ellipsize="end" />

                <TextView
                    android:id="@+id/tvCouponValue"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="20%"
                    android:textColor="@color/accent_orange"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:layout_marginTop="4dp" />

            </LinearLayout>

            <!-- Status Chip -->
            <com.google.android.material.chip.Chip
                android:id="@+id/chipCouponStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="نشط"
                android:textSize="12sp"
                android:textColor="@color/success_green"
                app:chipBackgroundColor="@color/success_light"
                app:chipStrokeColor="@color/success_green"
                app:chipStrokeWidth="1dp" />

        </LinearLayout>

        <!-- Coupon Image (Optional) -->
        <ImageView
            android:id="@+id/ivCouponImage"
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:scaleType="centerCrop"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:background="@drawable/rounded_background"
            android:visibility="gone"
            tools:src="@drawable/ic_local_offer" />

        <!-- Description -->
        <TextView
            android:id="@+id/tvCouponDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="وصف الكوبون والتفاصيل"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:fontFamily="@font/cairo_regular"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Coupon Code Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:padding="12dp"
            android:background="@drawable/rounded_background"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الكود:"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tvCouponCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SAVE20"
                android:textColor="@color/primary_blue"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="@font/cairo_bold"
                android:background="@drawable/rounded_background"
                android:padding="8dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnCopyCode"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="📋 نسخ"
                android:textColor="@color/primary_blue"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginStart="8dp"
                app:cornerRadius="18dp" />

        </LinearLayout>

        <!-- Details Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp">

            <!-- Minimum Order -->
            <TextView
                android:id="@+id/tvMinimumOrder"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="حد أدنى: 100 دينار"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginBottom="4dp"
                android:drawableStart="@drawable/ic_shopping_cart"
                android:drawableTint="@color/text_secondary"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

            <!-- Valid Until -->
            <TextView
                android:id="@+id/tvCouponValidUntil"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="صالح حتى: 31/12/2024"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginBottom="4dp"
                android:drawableStart="@drawable/ic_visibility"
                android:drawableTint="@color/text_secondary"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

            <!-- Days Remaining -->
            <TextView
                android:id="@+id/tvDaysRemaining"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ينتهي خلال 5 أيام"
                android:textColor="@color/warning_yellow"
                android:textSize="12sp"
                android:textStyle="bold"
                android:fontFamily="@font/cairo_bold"
                android:layout_marginBottom="4dp"
                android:drawableStart="@drawable/ic_notifications"
                android:drawableTint="@color/warning_yellow"
                android:drawablePadding="4dp"
                android:gravity="center_vertical" />

        </LinearLayout>

        <!-- Usage Progress -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tvUsageInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="متبقي: 35 من 50"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginBottom="4dp" />

            <ProgressBar
                android:id="@+id/progressUsage"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:max="50"
                android:progress="15"
                android:progressTint="@color/accent_orange"
                android:progressBackgroundTint="@color/border_light" />

        </LinearLayout>

        <!-- Badges -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chipNew"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🆕 جديد"
                android:textSize="10sp"
                android:textColor="@color/success_green"
                app:chipBackgroundColor="@color/success_light"
                app:chipStrokeColor="@color/success_green"
                app:chipStrokeWidth="1dp"
                android:layout_marginEnd="4dp"
                android:visibility="gone" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipFirstTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="👋 عملاء جدد"
                android:textSize="10sp"
                android:textColor="@color/primary_blue"
                app:chipBackgroundColor="@color/primary_light"
                app:chipStrokeColor="@color/primary_blue"
                app:chipStrokeWidth="1dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Terms and Conditions -->
        <TextView
            android:id="@+id/tvTerms"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="الشروط والأحكام تظهر هنا"
            android:textColor="@color/text_tertiary"
            android:textSize="11sp"
            android:fontFamily="@font/cairo_regular"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="12dp"
            android:maxLines="2"
            android:ellipsize="end"
            android:visibility="gone" />

        <!-- Admin Actions (Admin Only) -->
        <LinearLayout
            android:id="@+id/layoutAdminActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:padding="12dp"
            android:background="@drawable/rounded_background"
            android:layout_margin="16dp"
            android:visibility="gone">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnToggleStatus"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="⏸️ إيقاف"
                android:textColor="@color/warning_yellow"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginEnd="8dp"
                app:cornerRadius="18dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="🗑️ حذف"
                android:textColor="@color/error_red"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                app:cornerRadius="18dp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
