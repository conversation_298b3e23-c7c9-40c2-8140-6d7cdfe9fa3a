<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:orientation="vertical"
    tools:context=".ExportActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_background"
        android:elevation="8dp"
        app:title="تصدير البيانات"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Header -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تصدير البيانات"
                        android:textColor="@color/text_primary"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:gravity="center" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="اختر نوع البيانات التي تريد تصديرها"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:gravity="center" />

                    <!-- Progress Bar -->
                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:visibility="gone"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:indeterminate="true" />

                    <!-- Status Text -->
                    <TextView
                        android:id="@+id/tvStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textColor="@color/primary_blue"
                        android:textSize="14sp"
                        android:gravity="center"
                        android:visibility="gone"
                        tools:text="جاري تصدير البيانات..." />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Export Options -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- Export Products -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_inventory"
                            android:tint="@color/primary_blue"
                            android:layout_gravity="center_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تصدير المنتجات"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvProductCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="(0 منتج)"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <Button
                            android:id="@+id/btnExportProducts"
                            style="@style/SecondaryButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تصدير"
                            android:textSize="12sp"
                            android:layout_gravity="center_vertical" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Export Orders -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_orders"
                            android:tint="@color/success_green"
                            android:layout_gravity="center_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تصدير الطلبات"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvOrderCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="(0 طلب)"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <Button
                            android:id="@+id/btnExportOrders"
                            style="@style/SecondaryButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تصدير"
                            android:textSize="12sp"
                            android:layout_gravity="center_vertical" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Export Customers -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_people"
                            android:tint="@color/accent_orange"
                            android:layout_gravity="center_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تصدير بيانات العملاء"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvCustomerCount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="(0 عميل)"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <Button
                            android:id="@+id/btnExportCustomers"
                            style="@style/SecondaryButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تصدير"
                            android:textSize="12sp"
                            android:layout_gravity="center_vertical" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Export Statistics -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="2dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <ImageView
                            android:layout_width="40dp"
                            android:layout_height="40dp"
                            android:src="@drawable/ic_chart"
                            android:tint="@color/accent_teal"
                            android:layout_gravity="center_vertical" />

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="16dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="تصدير الإحصائيات"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvStatisticsInfo"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="(تقارير شاملة)"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                        </LinearLayout>

                        <Button
                            android:id="@+id/btnExportStatistics"
                            style="@style/SecondaryButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="تصدير"
                            android:textSize="12sp"
                            android:layout_gravity="center_vertical" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:orientation="vertical">

                <!-- Export All -->
                <Button
                    android:id="@+id/btnExportAll"
                    style="@style/PrimaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_export"
                    android:drawableTint="@color/white"
                    android:text="تصدير جميع البيانات" />

                <!-- Custom Export -->
                <Button
                    android:id="@+id/btnCustomExport"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableStart="@drawable/ic_custom_report"
                    android:drawableTint="@color/primary_blue"
                    android:text="تصدير مخصص" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
