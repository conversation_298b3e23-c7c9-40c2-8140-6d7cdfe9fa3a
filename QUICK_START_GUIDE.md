# 🚀 دليل التشغيل السريع - تطبيق كهربائيات عامر

## 📋 **متطلبات التشغيل**

### 🔧 **البيئة المطلوبة:**
- **Android Studio** Hedgehog أو أحدث
- **JDK 8** أو أحدث  
- **Android SDK** مع API 26+
- **Gradle 8.0** أو أحدث
- **جهاز Android** أو محاكي بـ API 26+

---

## ⚡ **خطوات التشغيل السريع**

### **1. 📥 تحضير المشروع**
```bash
# فتح المشروع في Android Studio
File → Open → اختر مجلد المشروع

# انتظار تحميل Gradle والفهرسة
# (قد يستغرق 2-5 دقائق في المرة الأولى)
```

### **2. 🔍 فحص سلامة المشروع**
```powershell
# تشغيل سكريبت الفحص الشامل
.\comprehensive_app_check.ps1

# يجب أن تحصل على: "نسبة اكتمال التطبيق: 100%"
```

### **3. 🧹 تنظيف وبناء المشروع**
```bash
# في Terminal داخل Android Studio أو PowerShell:

# تنظيف المشروع
.\gradlew clean

# بناء المشروع
.\gradlew assembleDebug

# تثبيت على الجهاز/المحاكي
.\gradlew installDebug
```

### **4. ▶️ تشغيل التطبيق**
```bash
# في Android Studio:
1. اختر الجهاز/المحاكي من القائمة العلوية
2. اضغط على زر Run (▶️) أو Shift+F10
3. انتظر تثبيت وتشغيل التطبيق
```

---

## 🔐 **بيانات تسجيل الدخول**

### **👤 حسابات الإدارة:**

#### **🔑 حساب المدير:**
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `kahrabaiat2024`
- **الصلاحيات:** إدارة كاملة عدا الأمان المتقدم

#### **👑 حساب المالك:**
- **اسم المستخدم:** `amer`
- **كلمة المرور:** `amer123456`  
- **الصلاحيات:** صلاحيات كاملة + إدارة الأمان

---

## 🧪 **اختبار الوظائف الأساسية**

### **✅ 1. اختبار الشاشة الرئيسية:**
- تصفح المنتجات والفئات
- استخدام البحث
- إضافة منتجات للسلة
- عرض تفاصيل المنتج

### **✅ 2. اختبار سلة المشتريات:**
- عرض العناصر المضافة
- تعديل الكميات
- حذف عناصر
- إتمام الشراء

### **✅ 3. اختبار لوحة الإدارة:**
```bash
# خطوات الاختبار:
1. اضغط على أيقونة الإدارة في الشاشة الرئيسية
2. سجل دخول بحساب admin أو amer
3. جرب جميع الأزرار في لوحة التحكم
4. اختبر إضافة/تعديل/حذف المنتجات
5. عرض الطلبات والتقارير
```

### **✅ 4. اختبار الأمان:**
```bash
# اختبار الحماية:
1. جرب تسجيل دخول بكلمة مرور خاطئة 5 مرات
2. تأكد من حظر الحساب مؤقتاً
3. اختبر فحص سلامة التطبيق
4. عرض سجل الأحداث الأمنية
```

### **✅ 5. اختبار الأداء:**
```bash
# مراقبة الأداء:
1. افتح شاشة مراقبة الأداء
2. راقب استهلاك الذاكرة والمعالج
3. اختبر تحسين الأداء التلقائي
4. عرض تقارير الأخطاء
```

---

## 🔧 **حل المشاكل الشائعة**

### **❌ مشكلة: فشل في البناء**
```bash
# الحلول:
1. تنظيف المشروع: .\gradlew clean
2. إعادة تحميل Gradle: File → Sync Project with Gradle Files
3. تحديث Android Studio للإصدار الأحدث
4. التأكد من اتصال الإنترنت لتحميل التبعيات
```

### **❌ مشكلة: التطبيق لا يعمل على المحاكي**
```bash
# الحلول:
1. استخدم محاكي بـ API 26 أو أحدث
2. تأكد من تفعيل Hardware Acceleration
3. زيادة ذاكرة المحاكي إلى 2GB على الأقل
4. استخدم Google Play Services في المحاكي
```

### **❌ مشكلة: خطأ في Firebase**
```bash
# الحل:
التطبيق يعمل حالياً ببيانات تجريبية محلية
Firebase معطل مؤقتاً للتطوير
لا حاجة لإعداد Firebase في هذه المرحلة
```

### **❌ مشكلة: الخطوط العربية لا تظهر**
```bash
# الحلول:
1. تأكد من تفعيل RTL في الجهاز
2. تغيير لغة النظام للعربية
3. إعادة تشغيل التطبيق
```

---

## 📊 **مراقبة الأداء**

### **🔍 أدوات المراقبة المدمجة:**
- **شاشة مراقبة الأداء:** لمتابعة الذاكرة والمعالج
- **تقارير الأخطاء:** تسجيل تلقائي للمشاكل
- **سجل الأمان:** مراقبة الأنشطة المشبوهة
- **إحصائيات الشبكة:** حالة الاتصال والجودة

### **📈 مؤشرات الأداء الطبيعية:**
- **استهلاك الذاكرة:** أقل من 80%
- **استهلاك المعالج:** أقل من 70%
- **وقت الاستجابة:** أقل من 300ms
- **معدل نجاح التخزين المؤقت:** أكثر من 80%

---

## 🎯 **نصائح للاستخدام الأمثل**

### **⚡ تحسين الأداء:**
- استخدم WiFi للتحميل السريع
- أغلق التطبيقات الأخرى لتوفير الذاكرة
- نظف التخزين المؤقت دورياً
- راقب تقارير الأداء بانتظام

### **🔐 الأمان:**
- غير كلمات المرور الافتراضية
- راجع سجل الأمان دورياً
- فعل التنبيهات الأمنية
- اختبر فحص سلامة التطبيق

### **📱 تجربة المستخدم:**
- اختبر على أجهزة مختلفة
- تأكد من سرعة الاستجابة
- راجع وضوح النصوص العربية
- اختبر جميع الانيميشن

---

## 📞 **الدعم والمساعدة**

### **🆘 في حالة المشاكل:**
1. **راجع سجل الأخطاء** في Android Studio
2. **اختبر على جهاز حقيقي** بدلاً من المحاكي
3. **تأكد من التبعيات** في build.gradle
4. **نظف وأعد بناء** المشروع

### **📋 معلومات مفيدة:**
- **إصدار التطبيق:** 1.0
- **الحد الأدنى لـ Android:** 8.0 (API 26)
- **الهدف:** Android 14 (API 34)
- **حجم التطبيق:** ~15-20 MB

---

## 🎉 **تهانينا!**

**إذا وصلت لهنا فقد نجحت في تشغيل التطبيق! 🎊**

**التطبيق الآن جاهز للاستخدام مع جميع الميزات المتقدمة:**
- ✅ إدارة المنتجات والطلبات
- ✅ نظام أمان متطور  
- ✅ مراقبة الأداء المستمرة
- ✅ تقارير شاملة ومفصلة
- ✅ واجهة عربية احترافية

**استمتع باستخدام تطبيق كهربائيات عامر! 🚀**
