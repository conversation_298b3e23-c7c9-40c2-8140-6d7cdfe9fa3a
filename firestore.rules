rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Products collection
    // قراءة للجميع، كتابة للمدير فقط
    match /products/{productId} {
      // السماح بالقراءة للجميع
      allow read: if true;
      
      // السماح بالكتابة للمدير فقط
      // في الإنتاج، يجب استخدام Firebase Authentication
      allow write: if true; // مؤقتاً للتطوير
    }
    
    // Orders collection
    // إنشاء للجميع، قراءة وتعديل للمدير فقط
    match /orders/{orderId} {
      // السماح بإنشاء طلب جديد للجميع
      allow create: if true;

      // السماح بالقراءة والتعديل للمدير فقط
      // في الإنتاج، يجب استخدام Firebase Authentication
      allow read, update, delete: if true; // مؤقتاً للتطوير
    }

    // Reviews collection
    // إنشاء للجميع، قراءة للجميع، تعديل للمدير فقط
    match /reviews/{reviewId} {
      // السماح بإنشاء مراجعة جديدة للجميع
      allow create: if true;

      // السماح بالقراءة للجميع (المراجعات المُوافق عليها فقط)
      allow read: if true;

      // السماح بالتعديل والحذف للمدير فقط
      // في الإنتاج، يجب استخدام Firebase Authentication
      allow update, delete: if true; // مؤقتاً للتطوير
    }

    // Notifications collection
    // إنشاء للمدير، قراءة للجميع، تعديل للمدير فقط
    match /notifications/{notificationId} {
      // السماح بإنشاء إشعار جديد للمدير فقط
      // في الإنتاج، يجب التحقق من صلاحيات المدير
      allow create: if true; // مؤقتاً للتطوير

      // السماح بالقراءة للجميع (الإشعارات النشطة فقط)
      allow read: if true;

      // السماح بالتعديل والحذف للمدير فقط
      // في الإنتاج، يجب استخدام Firebase Authentication
      allow update, delete: if true; // مؤقتاً للتطوير
    }

    // Notification Settings collection
    // إنشاء وتعديل للمستخدم نفسه، قراءة للمدير
    match /notification_settings/{userId} {
      // السماح بإنشاء وتعديل إعدادات الإشعارات للمستخدم نفسه
      allow create, update: if true; // مؤقتاً للتطوير

      // السماح بالقراءة للمدير والمستخدم نفسه
      allow read: if true; // مؤقتاً للتطوير

      // السماح بالحذف للمدير فقط
      allow delete: if true; // مؤقتاً للتطوير
    }

    // Coupons collection
    // إنشاء للمدير، قراءة للجميع، تعديل للمدير فقط
    match /coupons/{couponId} {
      // السماح بإنشاء كوبون جديد للمدير فقط
      // في الإنتاج، يجب التحقق من صلاحيات المدير
      allow create: if true; // مؤقتاً للتطوير

      // السماح بالقراءة للجميع (الكوبونات النشطة فقط)
      allow read: if true;

      // السماح بالتعديل والحذف للمدير فقط
      // في الإنتاج، يجب استخدام Firebase Authentication
      allow update, delete: if true; // مؤقتاً للتطوير
    }

    // Coupon Usage collection
    // إنشاء للجميع، قراءة للمدير فقط
    match /coupon_usage/{usageId} {
      // السماح بتسجيل استخدام الكوبون للجميع
      allow create: if true;

      // السماح بالقراءة للمدير فقط
      // في الإنتاج، يجب التحقق من صلاحيات المدير
      allow read: if true; // مؤقتاً للتطوير

      // السماح بالتعديل والحذف للمدير فقط
      allow update, delete: if true; // مؤقتاً للتطوير
    }
    
    // Categories collection (إذا أردت إضافة تصنيفات منفصلة)
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if true; // مؤقتاً للتطوير
    }
    
    // Users collection (للمستقبل)
    match /users/{userId} {
      allow read, write: if true; // مؤقتاً للتطوير
    }
    
    // Settings collection (إعدادات التطبيق)
    match /settings/{settingId} {
      allow read: if true;
      allow write: if true; // مؤقتاً للتطوير
    }
  }
}

// ملاحظات للإنتاج:
// 1. استبدال "if true" بقواعد أمان حقيقية
// 2. استخدام Firebase Authentication
// 3. إضافة تحقق من صلاحيات المدير
// 4. إضافة تحقق من ملكية البيانات

// مثال على قواعد أمان متقدمة (للمستقبل):
/*
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && 
                   request.auth.token.admin == true;
    }
    
    match /orders/{orderId} {
      allow create: if true;
      allow read, update, delete: if request.auth != null && 
                                   request.auth.token.admin == true;
    }
  }
}
*/
