# تطبيق كهربائيات عامر

تطبيق Android لمتجر كهربائيات عامر - تطبيق تجارة إلكترونية شامل للأجهزة الكهربائية والمنزلية والعدد اليدوية.

## المميزات

### للعملاء:
- عرض المنتجات بتصنيفات واضحة
- البحث في المنتجات
- سلة المشتريات
- إتمام الطلبات مع معلومات العميل
- عرض العروض والخصومات
- دعم كامل للغة العربية مع RTL

### للمدير:
- لوحة تحكم شاملة
- إضافة وتعديل وحذف المنتجات
- إدارة المخزون والأسعار
- متابعة الطلبات
- إحصائيات سريعة
- إشعارات الطلبات الجديدة

## التقنيات المستخدمة

- **اللغة**: Kotlin
- **قاعدة البيانات**: Firebase Firestore
- **التخزين**: Firebase Storage
- **الإشعارات**: Firebase Cloud Messaging (FCM)
- **واجهة المستخدم**: Material Design 3
- **معمارية**: MVVM
- **مكتبات إضافية**:
  - Glide لتحميل الصور
  - iText7 لإنشاء ملفات PDF
  - ViewPager2 للسلايدر

## إعداد المشروع

### 1. متطلبات النظام
- Android Studio Arctic Fox أو أحدث
- Android SDK 24 أو أحدث
- Kotlin 1.9.10 أو أحدث

### 2. إعداد Firebase (اختياري)
**ملاحظة:** التطبيق يعمل حالياً ببيانات تجريبية محلية بدون الحاجة لـ Firebase.

لإعداد Firebase (للإنتاج)، راجع ملف [FIREBASE_SETUP.md](FIREBASE_SETUP.md) للتعليمات المفصلة.

### 3. إعداد Firestore
إنشاء المجموعات التالية:
```
products/
├── id (string)
├── name (string)
├── price (number)
├── description (string)
├── imageUrl (string)
├── category (string)
├── stock (number)
├── available (boolean)
├── discount (number)
└── createdAt (timestamp)

orders/
├── id (string)
├── orderNumber (string)
├── customerName (string)
├── customerAddress (string)
├── customerPhone (string)
├── items (array)
├── totalAmount (number)
├── orderDate (timestamp)
└── status (string)
```

### 4. قواعد الأمان في Firestore
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Products - قراءة للجميع، كتابة للمدير فقط
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }

    // Orders - كتابة للجميع، قراءة للمدير فقط
    match /orders/{orderId} {
      allow create: if true;
      allow read, update, delete: if request.auth != null && request.auth.token.admin == true;
    }
  }
}
```

### 3. تشغيل التطبيق
1. استنساخ المشروع
2. فتح المشروع في Android Studio
3. تشغيل `./gradlew build`
4. تشغيل التطبيق على الجهاز أو المحاكي

**ملاحظة:** لا حاجة لإعداد Firebase للتشغيل الأولي!

## استخدام التطبيق

### تسجيل دخول المدير
- كلمة المرور الافتراضية: `admin123456`
- يمكن تغييرها من ملف `AdminLoginActivity.kt`

### إضافة منتجات تجريبية
يمكن إضافة منتجات تجريبية من لوحة تحكم المدير أو مباشرة في Firestore.

## هيكل المشروع

```
app/src/main/
├── java/com/kahrabaiat/amer/
│   ├── adapters/          # محولات RecyclerView
│   ├── models/            # نماذج البيانات
│   ├── services/          # خدمات Firebase
│   ├── utils/             # أدوات مساعدة
│   └── *.kt              # الأنشطة الرئيسية
├── res/
│   ├── drawable/         # الرموز والخلفيات
│   ├── layout/           # تخطيطات الشاشات
│   ├── menu/             # قوائم التطبيق
│   ├── values/           # الألوان والنصوص والأنماط
│   └── xml/              # ملفات XML إضافية
└── AndroidManifest.xml   # ملف البيان
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للدعم والاستفسارات، يرجى فتح issue في GitHub أو التواصل عبر البريد الإلكتروني.

---

تم تطوير هذا التطبيق بواسطة Augment Agent 🤖
