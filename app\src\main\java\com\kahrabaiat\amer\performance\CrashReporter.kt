package com.kahrabaiat.amer.performance

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import android.util.Log
import java.io.PrintWriter
import java.io.StringWriter
import java.text.SimpleDateFormat
import java.util.*

class CrashReporter private constructor(private val context: Context) : Thread.UncaughtExceptionHandler {

    companion object {
        private const val TAG = "CrashReporter"
        private const val PREFS_NAME = "crash_reports"
        private const val KEY_CRASH_COUNT = "crash_count"
        private const val KEY_LAST_CRASH_TIME = "last_crash_time"
        private const val MAX_CRASH_REPORTS = 10

        @Volatile
        private var INSTANCE: CrashReporter? = null

        fun getInstance(context: Context): CrashReporter {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CrashReporter(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private val crashListeners = mutableListOf<CrashListener>()

    /**
     * تفعيل مراقبة الأخطاء
     */
    fun initialize() {
        Thread.setDefaultUncaughtExceptionHandler(this)
        Log.d(TAG, "Crash reporter initialized")
    }

    override fun uncaughtException(thread: Thread, exception: Throwable) {
        try {
            // جمع معلومات الخطأ
            val crashReport = generateCrashReport(thread, exception)

            // حفظ تقرير الخطأ
            saveCrashReport(crashReport)

            // إشعار المستمعين
            notifyCrashListeners(crashReport)

            Log.e(TAG, "Uncaught exception occurred", exception)

        } catch (e: Exception) {
            Log.e(TAG, "Error in crash reporter", e)
        } finally {
            // استدعاء المعالج الافتراضي
            defaultHandler?.uncaughtException(thread, exception)
        }
    }

    /**
     * إنشاء تقرير خطأ شامل
     */
    private fun generateCrashReport(thread: Thread, exception: Throwable): CrashReport {
        val timestamp = System.currentTimeMillis()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())

        // معلومات الخطأ
        val stackTrace = getStackTrace(exception)
        val exceptionType = exception.javaClass.simpleName
        val exceptionMessage = exception.message ?: "No message"

        // معلومات الخيط
        val threadName = thread.name
        val threadId = thread.id

        // معلومات الجهاز
        val deviceInfo = getDeviceInfo()

        // معلومات التطبيق
        val appInfo = getAppInfo()

        // معلومات الأداء
        val performanceInfo = getPerformanceInfo()

        return CrashReport(
            timestamp = timestamp,
            formattedTime = dateFormat.format(Date(timestamp)),
            exceptionType = exceptionType,
            exceptionMessage = exceptionMessage,
            stackTrace = stackTrace,
            threadName = threadName,
            threadId = threadId,
            deviceInfo = deviceInfo,
            appInfo = appInfo,
            performanceInfo = performanceInfo
        )
    }

    /**
     * الحصول على stack trace كنص
     */
    private fun getStackTrace(exception: Throwable): String {
        val stringWriter = StringWriter()
        val printWriter = PrintWriter(stringWriter)
        exception.printStackTrace(printWriter)
        return stringWriter.toString()
    }

    /**
     * الحصول على معلومات الجهاز
     */
    private fun getDeviceInfo(): DeviceInfo {
        return DeviceInfo(
            manufacturer = Build.MANUFACTURER,
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            apiLevel = Build.VERSION.SDK_INT,
            brand = Build.BRAND,
            device = Build.DEVICE,
            hardware = Build.HARDWARE,
            board = Build.BOARD
        )
    }

    /**
     * الحصول على معلومات التطبيق
     */
    private fun getAppInfo(): AppInfo {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            AppInfo(
                packageName = context.packageName,
                versionName = packageInfo.versionName ?: "Unknown",
                versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode.toInt()
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode
                },
                targetSdkVersion = packageInfo.applicationInfo.targetSdkVersion,
                minSdkVersion = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    packageInfo.applicationInfo.minSdkVersion
                } else {
                    0
                }
            )
        } catch (e: Exception) {
            AppInfo("Unknown", "Unknown", 0, 0, 0)
        }
    }

    /**
     * الحصول على معلومات الأداء
     */
    private fun getPerformanceInfo(): PerformanceInfo {
        return try {
            val runtime = Runtime.getRuntime()
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val memoryInfo = android.app.ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            PerformanceInfo(
                totalMemory = runtime.totalMemory(),
                freeMemory = runtime.freeMemory(),
                maxMemory = runtime.maxMemory(),
                availableSystemMemory = memoryInfo.availMem,
                isLowMemory = memoryInfo.lowMemory,
                processorCount = runtime.availableProcessors()
            )
        } catch (e: Exception) {
            PerformanceInfo(0, 0, 0, 0, false, 0)
        }
    }

    /**
     * حفظ تقرير الخطأ
     */
    private fun saveCrashReport(crashReport: CrashReport) {
        try {
            // زيادة عداد الأخطاء
            val crashCount = sharedPreferences.getInt(KEY_CRASH_COUNT, 0) + 1
            sharedPreferences.edit().apply {
                putInt(KEY_CRASH_COUNT, crashCount)
                putLong(KEY_LAST_CRASH_TIME, crashReport.timestamp)
                apply()
            }

            // حفظ التقرير في ملف
            val reportJson = com.google.gson.Gson().toJson(crashReport)
            val fileName = "crash_report_${crashReport.timestamp}.json"

            context.openFileOutput(fileName, Context.MODE_PRIVATE).use { output ->
                output.write(reportJson.toByteArray())
            }

            // تنظيف التقارير القديمة
            cleanupOldReports()

            Log.d(TAG, "Crash report saved: $fileName")

        } catch (e: Exception) {
            Log.e(TAG, "Error saving crash report", e)
        }
    }

    /**
     * تنظيف التقارير القديمة
     */
    private fun cleanupOldReports() {
        try {
            val files = context.filesDir.listFiles { _, name ->
                name.startsWith("crash_report_") && name.endsWith(".json")
            }

            if (files != null && files.size > MAX_CRASH_REPORTS) {
                // ترتيب الملفات حسب التاريخ
                files.sortBy { it.lastModified() }

                // حذف الملفات الأقدم
                for (i in 0 until files.size - MAX_CRASH_REPORTS) {
                    files[i].delete()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up old reports", e)
        }
    }

    /**
     * إشعار المستمعين بالخطأ
     */
    private fun notifyCrashListeners(crashReport: CrashReport) {
        crashListeners.forEach { listener ->
            try {
                listener.onCrashDetected(crashReport)
            } catch (e: Exception) {
                Log.e(TAG, "Error notifying crash listener", e)
            }
        }
    }

    /**
     * إضافة مستمع للأخطاء
     */
    fun addCrashListener(listener: CrashListener) {
        crashListeners.add(listener)
    }

    /**
     * إزالة مستمع الأخطاء
     */
    fun removeCrashListener(listener: CrashListener) {
        crashListeners.remove(listener)
    }

    /**
     * الحصول على عدد الأخطاء
     */
    fun getCrashCount(): Int {
        return sharedPreferences.getInt(KEY_CRASH_COUNT, 0)
    }

    /**
     * الحصول على وقت آخر خطأ
     */
    fun getLastCrashTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_CRASH_TIME, 0)
    }

    /**
     * الحصول على جميع تقارير الأخطاء
     */
    fun getAllCrashReports(): List<CrashReport> {
        val reports = mutableListOf<CrashReport>()

        try {
            val files = context.filesDir.listFiles { _, name ->
                name.startsWith("crash_report_") && name.endsWith(".json")
            }

            files?.forEach { file ->
                try {
                    val json = file.readText()
                    val report = com.google.gson.Gson().fromJson(json, CrashReport::class.java)
                    reports.add(report)
                } catch (e: Exception) {
                    Log.e(TAG, "Error reading crash report: ${file.name}", e)
                }
            }

            // ترتيب حسب التاريخ (الأحدث أولاً)
            reports.sortByDescending { it.timestamp }

        } catch (e: Exception) {
            Log.e(TAG, "Error loading crash reports", e)
        }

        return reports
    }

    /**
     * مسح جميع تقارير الأخطاء
     */
    fun clearAllReports() {
        try {
            val files = context.filesDir.listFiles { _, name ->
                name.startsWith("crash_report_") && name.endsWith(".json")
            }

            files?.forEach { it.delete() }

            sharedPreferences.edit().apply {
                putInt(KEY_CRASH_COUNT, 0)
                putLong(KEY_LAST_CRASH_TIME, 0)
                apply()
            }

            Log.d(TAG, "All crash reports cleared")

        } catch (e: Exception) {
            Log.e(TAG, "Error clearing crash reports", e)
        }
    }

    /**
     * تصدير تقرير شامل
     */
    fun exportCrashReport(): String {
        val reports = getAllCrashReports()
        val crashCount = getCrashCount()
        val lastCrashTime = getLastCrashTime()

        return buildString {
            appendLine("=== تقرير الأخطاء الشامل ===")
            appendLine("تاريخ التقرير: ${SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(Date())}")
            appendLine("إجمالي الأخطاء: $crashCount")

            if (lastCrashTime > 0) {
                appendLine("آخر خطأ: ${SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault()).format(Date(lastCrashTime))}")
            }

            appendLine()
            appendLine("=== تفاصيل الأخطاء ===")

            reports.forEach { report ->
                appendLine("--- خطأ في ${report.formattedTime} ---")
                appendLine("النوع: ${report.exceptionType}")
                appendLine("الرسالة: ${report.exceptionMessage}")
                appendLine("الخيط: ${report.threadName} (ID: ${report.threadId})")
                appendLine("الجهاز: ${report.deviceInfo.manufacturer} ${report.deviceInfo.model}")
                appendLine("Android: ${report.deviceInfo.androidVersion} (API ${report.deviceInfo.apiLevel})")
                appendLine("التطبيق: ${report.appInfo.versionName} (${report.appInfo.versionCode})")
                appendLine("الذاكرة المستخدمة: ${report.performanceInfo.totalMemory - report.performanceInfo.freeMemory} bytes")
                appendLine("ذاكرة منخفضة: ${if (report.performanceInfo.isLowMemory) "نعم" else "لا"}")
                appendLine()
                appendLine("Stack Trace:")
                appendLine(report.stackTrace)
                appendLine("=".repeat(50))
                appendLine()
            }
        }
    }

    // Data classes
    data class CrashReport(
        val timestamp: Long,
        val formattedTime: String,
        val exceptionType: String,
        val exceptionMessage: String,
        val stackTrace: String,
        val threadName: String,
        val threadId: Long,
        val deviceInfo: DeviceInfo,
        val appInfo: AppInfo,
        val performanceInfo: PerformanceInfo
    )

    data class DeviceInfo(
        val manufacturer: String,
        val model: String,
        val androidVersion: String,
        val apiLevel: Int,
        val brand: String,
        val device: String,
        val hardware: String,
        val board: String
    )

    data class AppInfo(
        val packageName: String,
        val versionName: String,
        val versionCode: Int,
        val targetSdkVersion: Int,
        val minSdkVersion: Int
    )

    data class PerformanceInfo(
        val totalMemory: Long,
        val freeMemory: Long,
        val maxMemory: Long,
        val availableSystemMemory: Long,
        val isLowMemory: Boolean,
        val processorCount: Int
    )

    interface CrashListener {
        fun onCrashDetected(crashReport: CrashReport)
    }
}
