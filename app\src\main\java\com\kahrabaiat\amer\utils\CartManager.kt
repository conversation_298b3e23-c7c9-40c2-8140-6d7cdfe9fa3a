package com.kahrabaiat.amer.utils

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.kahrabaiat.amer.models.CartItem
import com.kahrabaiat.amer.models.Product

class CartManager private constructor(context: Context) {

    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences("cart_prefs", Context.MODE_PRIVATE)
    private val gson = Gson()
    private val cartItems = mutableListOf<CartItem>()

    companion object {
        @Volatile
        private var INSTANCE: CartManager? = null

        fun getInstance(context: Context): CartManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CartManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    init {
        loadCartFromPreferences()
    }

    fun addToCart(product: Product, quantity: Int = 1) {
        val existingItem = cartItems.find { it.product.id == product.id }
        if (existingItem != null) {
            existingItem.quantity += quantity
        } else {
            cartItems.add(CartItem(product, quantity))
        }
        saveCartToPreferences()
    }

    fun removeFromCart(productId: Int) {
        cartItems.removeAll { it.product.id == productId }
        saveCartToPreferences()
    }

    fun updateQuantity(productId: Int, quantity: Int) {
        val item = cartItems.find { it.product.id == productId }
        if (item != null) {
            if (quantity <= 0) {
                removeFromCart(productId)
            } else {
                item.quantity = quantity
                saveCartToPreferences()
            }
        }
    }

    fun getCartItems(): List<CartItem> {
        return cartItems.toList()
    }

    fun getCartItemCount(): Int {
        return cartItems.sumOf { it.quantity }
    }

    fun getTotalAmount(): Double {
        return cartItems.sumOf { it.getTotalPrice() }
    }

    fun getFormattedTotal(): String {
        return "${getTotalAmount().toInt()} دينار عراقي"
    }

    fun clearCart() {
        cartItems.clear()
        saveCartToPreferences()
    }

    fun isProductInCart(productId: Int): Boolean {
        return cartItems.any { it.product.id == productId }
    }

    fun getProductQuantityInCart(productId: Int): Int {
        return cartItems.find { it.product.id == productId }?.quantity ?: 0
    }

    private fun saveCartToPreferences() {
        val json = gson.toJson(cartItems)
        sharedPreferences.edit().putString("cart_items", json).apply()
    }

    private fun loadCartFromPreferences() {
        val json = sharedPreferences.getString("cart_items", null)
        if (json != null) {
            val type = object : TypeToken<List<CartItem>>() {}.type
            val items: List<CartItem> = gson.fromJson(json, type) ?: emptyList()
            cartItems.clear()
            cartItems.addAll(items)
        }
    }
}
