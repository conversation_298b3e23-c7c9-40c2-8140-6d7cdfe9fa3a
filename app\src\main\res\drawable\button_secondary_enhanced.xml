<?xml version="1.0" encoding="utf-8"?>
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="@color/ripple_blue">
    
    <item>
        <layer-list>
            <!-- Shadow -->
            <item android:top="1dp" android:left="1dp">
                <shape android:shape="rectangle">
                    <solid android:color="#20000000" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            
            <!-- Button Background -->
            <item android:bottom="1dp" android:right="1dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:angle="135"
                        android:startColor="#FFFFFF"
                        android:endColor="#F8FAFC"
                        android:type="linear" />
                    <stroke 
                        android:width="2dp" 
                        android:color="@color/primary_blue" />
                    <corners android:radius="14dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
</ripple>
