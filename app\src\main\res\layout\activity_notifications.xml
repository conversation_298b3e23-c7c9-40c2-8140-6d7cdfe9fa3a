<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Stats and Actions Bar -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Stats -->
                <TextView
                    android:id="@+id/tvNotificationStats"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="الإجمالي: 0 | غير مقروء: 0"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:gravity="center"
                    android:layout_marginBottom="12dp"
                    android:visibility="gone" />

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnMarkAllAsRead"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:text="✓ تحديد الكل كمقروء"
                        android:textColor="@color/success_green"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        app:cornerRadius="20dp"
                        app:strokeColor="@color/success_green"
                        android:visibility="gone" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnRefresh"
                        style="@style/Widget.Material3.Button.TextButton"
                        android:layout_width="0dp"
                        android:layout_height="40dp"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:text="🔄 تحديث"
                        android:textColor="@color/primary_blue"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        app:cornerRadius="20dp" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Content Area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- Loading -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <!-- Notifications List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvNotifications"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="80dp" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layoutEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🔔"
                    android:textSize="64sp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tvEmptyTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="لا توجد إشعارات"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvEmptyMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="لم يتم العثور على أي إشعارات.\nستظهر الإشعارات الجديدة هنا."
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:fontFamily="@font/cairo_regular"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

    <!-- Floating Action Button (Admin Only) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddNotification"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@color/white"
        app:backgroundTint="@color/primary_blue"
        android:contentDescription="إضافة إشعار جديد"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
