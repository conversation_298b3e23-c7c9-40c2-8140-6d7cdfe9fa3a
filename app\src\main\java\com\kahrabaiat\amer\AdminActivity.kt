package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.AdminOrderAdapter
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.databinding.ActivityAdminBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.utils.NotificationHelper
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.util.*

class AdminActivity : BaseAdminActivity() {

    private lateinit var binding: ActivityAdminBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var adminOrderAdapter: AdminOrderAdapter
    private lateinit var notificationHelper: NotificationHelper
    private var recentOrders = mutableListOf<Order>()
    private var isDataLoading = false

    companion object {
        private const val REQUEST_ADD_PRODUCT = 1001
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityAdminBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupRecyclerView()
            setupClickListeners()
            loadData()

        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error in onCreate", e)
            Toast.makeText(this, "خطأ في تحميل لوحة التحكم: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        loadData()
    }

    private fun initializeComponents() {
        try {
            databaseHelper = DatabaseHelper.getInstance(this)
            notificationHelper = NotificationHelper(this)

            adminOrderAdapter = AdminOrderAdapter(
                orders = recentOrders,
                onOrderClick = { order ->
                    // Navigate to orders activity with specific order
                    val intent = Intent(this, OrdersActivity::class.java)
                    intent.putExtra("order_id", order.id)
                    startActivity(intent)
                },
                onStatusChange = { order, newStatus ->
                    updateOrderStatus(order, newStatus)
                }
            )
        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error initializing components", e)
            Toast.makeText(this, "خطأ في تهيئة المكونات: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun setupToolbar() {
        try {
            // Now we can safely set the toolbar since we have NoActionBar theme
            setSupportActionBar(binding.toolbar)

            supportActionBar?.apply {
                title = getString(R.string.admin_panel)
                setDisplayHomeAsUpEnabled(true)
                setDisplayShowHomeEnabled(true)
            }
        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error setting up toolbar", e)
            // Hide toolbar if there's an error
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        try {
            binding.rvRecentOrders.apply {
                layoutManager = LinearLayoutManager(this@AdminActivity)
                adapter = adminOrderAdapter
            }
        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error setting up RecyclerView", e)
            binding.rvRecentOrders.visibility = View.GONE
            binding.tvNoOrders.visibility = View.VISIBLE
            binding.tvNoOrders.text = "خطأ في تحميل الطلبات"
        }
    }

    private fun setupClickListeners() {
        binding.btnAddProduct.setOnClickListener {
            val intent = Intent(this, AddProductActivity::class.java)
            startActivityForResult(intent, REQUEST_ADD_PRODUCT)
        }

        binding.btnManageProducts.setOnClickListener {
            val intent = Intent(this, ManageProductsActivity::class.java)
            startActivity(intent)
        }

        binding.btnViewOrders.setOnClickListener {
            val intent = Intent(this, OrdersActivity::class.java)
            startActivity(intent)
        }

        binding.tvViewAllOrders.setOnClickListener {
            val intent = Intent(this, OrdersActivity::class.java)
            startActivity(intent)
        }

        // Add advanced search option
        binding.btnManageProducts.setOnLongClickListener {
            val intent = Intent(this, AdvancedSearchActivity::class.java)
            startActivity(intent)
            true
        }

        binding.btnStatistics.setOnClickListener {
            val intent = Intent(this, StatisticsActivity::class.java)
            startActivity(intent)
        }

        binding.btnExport.setOnClickListener {
            val intent = Intent(this, ExportActivity::class.java)
            startActivity(intent)
        }

        binding.btnNotificationSettings.setOnClickListener {
            val intent = Intent(this, NotificationSettingsActivity::class.java)
            startActivity(intent)
        }

        binding.btnInventorySettings.setOnClickListener {
            val intent = Intent(this, InventorySettingsActivity::class.java)
            startActivity(intent)
        }

        binding.btnSecuritySettings.setOnClickListener {
            val intent = Intent(this, AdminActivity::class.java)
            startActivity(intent)
        }

        binding.btnPerformanceMonitor.setOnClickListener {
            val intent = Intent(this, PerformanceMonitorActivity::class.java)
            startActivity(intent)
        }

        binding.btnCustomReports.setOnClickListener {
            val intent = Intent(this, StatisticsActivity::class.java)
            startActivity(intent)
        }

        binding.btnAdvancedAnalytics.setOnClickListener {
            val intent = Intent(this, AdvancedAnalyticsActivity::class.java)
            startActivity(intent)
        }

        binding.btnFirebaseTest.setOnClickListener {
            val intent = Intent(this, FirebaseTestActivity::class.java)
            startActivity(intent)
        }

        binding.btnUserManagement.setOnClickListener {
            val intent = Intent(this, UserManagementActivity::class.java)
            startActivity(intent)
        }
    }

    private fun loadData() {
        if (isDataLoading) return

        lifecycleScope.launch {
            try {
                isDataLoading = true
                showLoadingState()

                // Load data in parallel for better performance
                loadStatistics()
                loadRecentOrders()

                hideLoadingState()
            } catch (e: Exception) {
                android.util.Log.e("AdminActivity", "Error loading data", e)
                runOnUiThread {
                    hideLoadingState()
                    showErrorState("خطأ في تحميل البيانات: ${e.message}")
                }
            } finally {
                isDataLoading = false
            }
        }
    }

    private suspend fun loadStatistics() {
        try {
            val productCount = databaseHelper.getProductCount()
            val orderCount = databaseHelper.getOrderCount()
            val totalSales = databaseHelper.getTotalSales()

            // Get additional statistics
            val pendingOrders = databaseHelper.getAllOrders().count { it.status == "pending" }
            val completedOrders = databaseHelper.getAllOrders().count { it.status == "delivered" }
            val lowStockProducts = databaseHelper.getAllProducts().count { it.stock <= 5 }

            runOnUiThread {
                // Format numbers with Arabic locale
                val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))

                binding.tvTotalProducts.text = numberFormat.format(productCount)
                binding.tvTotalOrders.text = numberFormat.format(orderCount)
                binding.tvTotalSales.text = "${numberFormat.format(totalSales.toInt())} د.ع"

                // Update button texts with additional info
                updateButtonsWithStats(pendingOrders, completedOrders, lowStockProducts)
            }
        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error loading statistics", e)
            runOnUiThread {
                binding.tvTotalProducts.text = "0"
                binding.tvTotalOrders.text = "0"
            }
        }
    }

    private suspend fun loadRecentOrders() {
        try {
            val orders = databaseHelper.getAllOrders().take(5) // Get latest 5 orders

            runOnUiThread {
                recentOrders.clear()
                recentOrders.addAll(orders)
                adminOrderAdapter.notifyDataSetChanged()

                if (orders.isEmpty()) {
                    binding.rvRecentOrders.visibility = View.GONE
                    binding.tvNoOrders.visibility = View.VISIBLE
                    binding.tvNoOrders.text = "لا توجد طلبات حتى الآن"
                } else {
                    binding.rvRecentOrders.visibility = View.VISIBLE
                    binding.tvNoOrders.visibility = View.GONE
                }
            }
        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error loading recent orders", e)
            runOnUiThread {
                binding.rvRecentOrders.visibility = View.GONE
                binding.tvNoOrders.visibility = View.VISIBLE
                binding.tvNoOrders.text = "خطأ في تحميل الطلبات"
            }
        }
    }

    private fun updateOrderStatus(order: Order, newStatus: String) {
        lifecycleScope.launch {
            try {
                val success = databaseHelper.updateOrderStatus(order.id, newStatus)
                runOnUiThread {
                    if (success) {
                        Toast.makeText(this@AdminActivity, "تم تحديث حالة الطلب بنجاح", Toast.LENGTH_SHORT).show()
                        // Reload orders in coroutine
                        lifecycleScope.launch {
                            loadRecentOrders()
                        }
                    } else {
                        Toast.makeText(this@AdminActivity, "فشل في تحديث حالة الطلب", Toast.LENGTH_SHORT).show()
                    }
                }
            } catch (e: Exception) {
                android.util.Log.e("AdminActivity", "Error updating order status", e)
                runOnUiThread {
                    Toast.makeText(this@AdminActivity, "خطأ في تحديث حالة الطلب", Toast.LENGTH_SHORT).show()
                }
            }
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.admin_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                goToMainActivity()
                true
            }
            R.id.action_refresh -> {
                refreshData()
                true
            }
            R.id.action_user_info -> {
                showUserInfo()
                true
            }
            R.id.action_go_to_main -> {
                goToMainActivity()
                true
            }
            R.id.action_manage_products -> {
                extendSession()
                true
            }
            R.id.action_logout -> {
                showLogoutDialog()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun showLogoutDialog() {
        AlertDialog.Builder(this)
            .setTitle("تسجيل الخروج")
            .setMessage("هل تريد تسجيل الخروج من لوحة التحكم؟")
            .setPositiveButton("نعم") { _, _ ->
                logout()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun logout() {
        authManager.logout()
        finish()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_ADD_PRODUCT && resultCode == RESULT_OK) {
            // Refresh data after adding product
            loadData()
            showToast("تم إضافة المنتج بنجاح")
        }
    }

    // Helper functions for better UX
    private fun showLoadingState() {
        runOnUiThread {
            binding.tvTotalProducts.text = "..."
            binding.tvTotalOrders.text = "..."
            binding.tvTotalSales.text = "... د.ع"
        }
    }

    private fun hideLoadingState() {
        // Loading state is hidden when data is loaded
    }

    private fun showErrorState(message: String) {
        binding.tvTotalProducts.text = "0"
        binding.tvTotalOrders.text = "0"
        binding.tvTotalSales.text = "0 د.ع"
        binding.rvRecentOrders.visibility = View.GONE
        binding.tvNoOrders.visibility = View.VISIBLE
        binding.tvNoOrders.text = message
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    private fun updateButtonsWithStats(pendingOrders: Int, completedOrders: Int, lowStockProducts: Int) {
        try {
            // Update View Orders button with pending count
            if (pendingOrders > 0) {
                binding.btnViewOrders.text = "عرض الطلبات ($pendingOrders معلق)"
            } else {
                binding.btnViewOrders.text = getString(R.string.view_orders)
            }

            // Update Inventory Settings button if low stock
            if (lowStockProducts > 0) {
                binding.btnInventorySettings.text = "إعدادات المخزون ($lowStockProducts منخفض)"
            } else {
                binding.btnInventorySettings.text = "إعدادات المخزون"
            }

            // Update Statistics button with completion rate
            val totalOrders = pendingOrders + completedOrders
            if (totalOrders > 0) {
                val completionRate = (completedOrders * 100) / totalOrders
                binding.btnStatistics.text = "الإحصائيات والتقارير ($completionRate% مكتمل)"
            } else {
                binding.btnStatistics.text = "الإحصائيات والتقارير"
            }
        } catch (e: Exception) {
            android.util.Log.e("AdminActivity", "Error updating button stats", e)
        }
    }

    private fun refreshData() {
        loadData()
        Toast.makeText(this, "تم تحديث البيانات", Toast.LENGTH_SHORT).show()
    }

    private fun showUserInfo() {
        AlertDialog.Builder(this)
            .setTitle("معلومات المستخدم")
            .setMessage("المستخدم: مدير النظام\nالصلاحيات: إدارة كاملة\nتاريخ الدخول: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale("ar")).format(java.util.Date())}")
            .setPositiveButton("موافق", null)
            .show()
    }

    private fun extendSession() {
        Toast.makeText(this, "تم تمديد الجلسة بنجاح", Toast.LENGTH_SHORT).show()
    }

    private fun goToMainActivity() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        startActivity(intent)
        // لا نستخدم finish() هنا للحفاظ على لوحة التحكم في الخلفية
        showToast("تم الانتقال للواجهة الرئيسية")
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        // عند الضغط على زر الرجوع، اذهب للواجهة الرئيسية
        goToMainActivity()
    }

    override fun onSupportNavigateUp(): Boolean {
        // عند الضغط على سهم الرجوع في الشريط العلوي
        goToMainActivity()
        return true
    }
}
