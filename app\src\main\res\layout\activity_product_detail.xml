<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".ProductDetailActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:navigationIconTint="@color/white"
            app:title="@string/product_details"
            app:titleTextColor="@color/white" />

        <!-- Product Image -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="320dp"
            android:layout_margin="16dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="@color/white">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                tools:src="@drawable/ic_product_placeholder" />

            <!-- Discount Badge -->
            <TextView
                android:id="@+id/tvDiscount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="16dp"
                android:background="@drawable/discount_badge_background"
                android:padding="8dp"
                android:text="خصم 20%"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible" />

            </FrameLayout>

        </androidx.cardview.widget.CardView>

        <!-- Product Info -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="8dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

            <!-- Product Name -->
            <TextView
                android:id="@+id/tvProductName"
                style="@style/HeadlineText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                tools:text="ثلاجة سامسونج 18 قدم" />

            <!-- Price Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- Current Price -->
                <TextView
                    android:id="@+id/tvCurrentPrice"
                    style="@style/PriceText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textSize="24sp"
                    tools:text="1200 دينار عراقي" />

                <!-- Original Price (if discounted) -->
                <TextView
                    android:id="@+id/tvOriginalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:visibility="gone"
                    tools:text="1500 دينار عراقي"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- Stock Status -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvStockStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/stock_badge_background"
                    android:padding="6dp"
                    android:text="@string/available"
                    android:textColor="@color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvStockCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="الكمية المتوفرة: 15" />

            </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Product Description -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="20dp"
            app:cardElevation="6dp"
            app:cardBackgroundColor="@color/white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="وصف المنتج"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvProductDescription"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    tools:text="ثلاجة سامسونج بسعة 18 قدم مكعب، تتميز بتقنية التبريد المتطورة وتوفير الطاقة. مناسبة للعائلات الكبيرة." />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Quantity Selection -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardQuantitySelection"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="الكمية:"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- Quantity Controls -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <!-- Decrease Button -->
                    <ImageButton
                        android:id="@+id/btnDecrease"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/quantity_button_background"
                        android:src="@drawable/ic_remove"
                        android:tint="@color/primary_blue" />

                    <!-- Quantity -->
                    <TextView
                        android:id="@+id/tvQuantity"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- Increase Button -->
                    <ImageButton
                        android:id="@+id/btnIncrease"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/quantity_button_background"
                        android:src="@drawable/ic_add"
                        android:tint="@color/primary_blue" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Reviews Section -->
        <LinearLayout
            android:id="@+id/layoutReviews"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_margin="16dp"
            android:visibility="gone">

            <!-- Reviews Header -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تقييمات العملاء"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold"
                        android:layout_marginBottom="12dp" />

                    <!-- Rating Stats -->
                    <LinearLayout
                        android:id="@+id/layoutRatingStats"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <RatingBar
                            android:id="@+id/ratingBarOverall"
                            style="?android:attr/ratingBarStyleSmall"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:numStars="5"
                            android:rating="0"
                            android:stepSize="0.1"
                            android:isIndicator="true"
                            android:progressTint="@color/accent_orange"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/tvAverageRating"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="0.0"
                            android:textColor="@color/accent_orange"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/cairo_bold"
                            android:layout_marginEnd="8dp" />

                        <TextView
                            android:id="@+id/tvTotalReviews"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="(0 مراجعة)"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:fontFamily="@font/cairo_regular" />

                    </LinearLayout>

                    <TextView
                        android:id="@+id/tvRatingDescription"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="لا توجد تقييمات"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:fontFamily="@font/cairo_regular"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvPositiveRating"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="كن أول من يقيم هذا المنتج"
                        android:textColor="@color/success_green"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Reviews List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvReviews"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp" />

            <!-- No Reviews Message -->
            <TextView
                android:id="@+id/tvNoReviews"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="لا توجد مراجعات لهذا المنتج بعد.\nكن أول من يشارك رأيه!"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:fontFamily="@font/cairo_regular"
                android:gravity="center"
                android:padding="20dp"
                android:background="@drawable/rounded_background"
                android:layout_marginBottom="12dp"
                android:visibility="gone" />

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnAddReview"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="⭐ أضف مراجعة"
                    android:textColor="@color/primary_blue"
                    android:textSize="14sp"
                    android:fontFamily="@font/cairo_bold"
                    app:cornerRadius="12dp"
                    app:strokeColor="@color/primary_blue" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnViewAllReviews"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="📋 جميع المراجعات"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:fontFamily="@font/cairo_regular"
                    app:cornerRadius="12dp" />

            </LinearLayout>

        </LinearLayout>

        <!-- Add to Cart Button -->
        <Button
            android:id="@+id/btnAddToCart"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:text="@string/add_to_cart" />

    </LinearLayout>

</ScrollView>
