<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/rounded_corner_background">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_lock"
            android:tint="@color/primary_blue"
            android:layout_marginEnd="12dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="تغيير كلمة المرور"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary" />

    </LinearLayout>

    <!-- Old Password -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="كلمة المرور الحالية"
        app:startIconDrawable="@drawable/ic_lock"
        app:endIconMode="password_toggle"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etOldPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- New Password -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="كلمة المرور الجديدة"
        app:startIconDrawable="@drawable/ic_lock"
        app:endIconMode="password_toggle"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etNewPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Confirm Password -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="تأكيد كلمة المرور الجديدة"
        app:startIconDrawable="@drawable/ic_lock"
        app:endIconMode="password_toggle"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etConfirmPassword"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Password Requirements -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="متطلبات كلمة المرور:\n• 6 أحرف على الأقل\n• يُنصح باستخدام أحرف وأرقام ورموز"
        android:textSize="12sp"
        android:textColor="@color/text_secondary"
        android:background="@drawable/info_background"
        android:padding="12dp" />

</LinearLayout>
