<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Edit Disabled Warning -->
        <TextView
            android:id="@+id/tvEditDisabled"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="@drawable/warning_background"
            android:padding="8dp"
            android:text="⚠️ التعديل معطل لهذا الطلب"
            android:textColor="@color/warning_yellow"
            android:textSize="12sp"
            android:visibility="gone"
            tools:visibility="visible" />

        <!-- Product Info Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <!-- Product Image Placeholder -->
            <ImageView
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/rounded_corner_background"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_product_placeholder" />

            <!-- Product Details -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- Product Name -->
                <TextView
                    android:id="@+id/tvProductName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="مفك براغي احترافي مع مجموعة رؤوس" />

                <!-- Unit Price -->
                <TextView
                    android:id="@+id/tvUnitPrice"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="15000 د.ع / قطعة" />

            </LinearLayout>

            <!-- Remove Button -->
            <ImageButton
                android:id="@+id/btnRemove"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="center_vertical"
                android:background="@drawable/circle_button_background"
                android:backgroundTint="@color/error_red"
                android:src="@drawable/ic_delete"
                android:tint="@color/white"
                android:contentDescription="حذف المنتج" />

        </LinearLayout>

        <!-- Quantity Controls Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- Quantity Label -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="الكمية:"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginEnd="8dp" />

            <!-- Decrease Button -->
            <ImageButton
                android:id="@+id/btnDecrease"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="@drawable/circle_button_background"
                android:backgroundTint="@color/accent_orange"
                android:src="@drawable/ic_remove"
                android:tint="@color/white"
                android:contentDescription="تقليل الكمية" />

            <!-- Quantity Input -->
            <EditText
                android:id="@+id/etQuantity"
                android:layout_width="60dp"
                android:layout_height="36dp"
                android:layout_marginHorizontal="8dp"
                android:background="@drawable/rounded_corner_background"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="3"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="2" />

            <!-- Increase Button -->
            <ImageButton
                android:id="@+id/btnIncrease"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="@drawable/circle_button_background"
                android:backgroundTint="@color/success_green"
                android:src="@drawable/ic_add"
                android:tint="@color/white"
                android:contentDescription="زيادة الكمية" />

            <!-- Spacer -->
            <View
                android:layout_width="0dp"
                android:layout_height="1dp"
                android:layout_weight="1" />

            <!-- Total Price -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="end">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="المجموع:"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tvTotalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/primary_blue"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="30000 د.ع" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
