package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Category(
    val id: String = "",
    val name: String = "",
    val imageUrl: String = "",
    val productCount: Int = 0
) : Parcelable

object CategoryConstants {
    const val ELECTRICAL_APPLIANCES = "electrical_appliances"
    const val HOME_APPLIANCES = "home_appliances"
    const val HAND_TOOLS = "hand_tools"
    
    fun getDefaultCategories(): List<Category> {
        return listOf(
            Category(
                id = ELECTRICAL_APPLIANCES,
                name = "الأجهزة الكهربائية",
                imageUrl = "",
                productCount = 0
            ),
            Category(
                id = HOME_APPLIANCES,
                name = "الأجهزة المنزلية",
                imageUrl = "",
                productCount = 0
            ),
            Category(
                id = HAND_TOOLS,
                name = "العدد اليدوية",
                imageUrl = "",
                productCount = 0
            )
        )
    }
}
