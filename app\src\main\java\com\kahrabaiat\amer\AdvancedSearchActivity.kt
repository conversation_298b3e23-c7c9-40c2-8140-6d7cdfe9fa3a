package com.kahrabaiat.amer

import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.ProductAdapter
import com.kahrabaiat.amer.databinding.ActivityAdvancedSearchBinding
import com.kahrabaiat.amer.models.CategoryConstants
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.FirebaseHelper
import kotlinx.coroutines.launch

class AdvancedSearchActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdvancedSearchBinding
    private lateinit var firebaseHelper: FirebaseHelper
    private lateinit var productAdapter: ProductAdapter
    private var allProducts = mutableListOf<Product>()
    private var searchResults = mutableListOf<Product>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityAdvancedSearchBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupRecyclerView()
            setupClickListeners()
            setupCategorySpinner()
            loadProducts()
        } catch (e: Exception) {
            android.util.Log.e("AdvancedSearchActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة البحث المتقدم: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        firebaseHelper = FirebaseHelper()
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "البحث المتقدم"
            }
        } catch (e: Exception) {
            android.util.Log.e("AdvancedSearchActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        try {
            productAdapter = ProductAdapter(
                onProductClick = { product ->
                    // Handle product click - navigate to details
                    val intent = android.content.Intent(this, ProductDetailActivity::class.java)
                    intent.putExtra("product", product)
                    startActivity(intent)
                },
                onAddToCartClick = { product ->
                    // For admin search, we don't need cart functionality
                    showToast("ميزة السلة غير متاحة في وضع الإدارة")
                }
            )

            binding.rvSearchResults.apply {
                layoutManager = LinearLayoutManager(this@AdvancedSearchActivity)
                adapter = productAdapter
            }
        } catch (e: Exception) {
            android.util.Log.e("AdvancedSearchActivity", "Error setting up RecyclerView", e)
            showToast("خطأ في إعداد قائمة النتائج")
        }
    }

    private fun setupClickListeners() {
        binding.btnSearch.setOnClickListener {
            performAdvancedSearch()
        }

        binding.btnClearFilters.setOnClickListener {
            clearAllFilters()
        }
    }

    private fun setupCategorySpinner() {
        val categories = arrayOf(
            "جميع الفئات",
            "الأجهزة المنزلية",
            "الأجهزة الكهربائية",
            "العدد اليدوية"
        )

        val adapter = android.widget.ArrayAdapter(
            this,
            android.R.layout.simple_spinner_item,
            categories
        )
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerCategory.adapter = adapter
    }

    private fun loadProducts() {
        lifecycleScope.launch {
            try {
                binding.progressBar.visibility = View.VISIBLE

                val products = firebaseHelper.getAllProducts()
                allProducts.clear()
                allProducts.addAll(products)

                // Show all products initially
                searchResults.clear()
                searchResults.addAll(allProducts)
                updateUI()

            } catch (e: Exception) {
                android.util.Log.e("AdvancedSearchActivity", "Error loading products", e)
                showToast("خطأ في تحميل المنتجات: ${e.message}")
            } finally {
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun performAdvancedSearch() {
        val searchQuery = binding.etSearchQuery.text.toString().trim()
        val selectedCategory = getSelectedCategory()
        val minPrice = binding.etMinPrice.text.toString().toDoubleOrNull()
        val maxPrice = binding.etMaxPrice.text.toString().toDoubleOrNull()
        val availableOnly = binding.cbAvailableOnly.isChecked
        val discountOnly = binding.cbDiscountOnly.isChecked

        searchResults.clear()

        var filteredProducts = allProducts.toList()

        // Apply text search
        if (searchQuery.isNotEmpty()) {
            filteredProducts = filteredProducts.filter { product ->
                product.name.contains(searchQuery, ignoreCase = true) ||
                product.description.contains(searchQuery, ignoreCase = true)
            }
        }

        // Apply category filter
        if (selectedCategory != "ALL") {
            filteredProducts = filteredProducts.filter { it.category == selectedCategory }
        }

        // Apply price range filter
        if (minPrice != null) {
            filteredProducts = filteredProducts.filter { it.price >= minPrice }
        }
        if (maxPrice != null) {
            filteredProducts = filteredProducts.filter { it.price <= maxPrice }
        }

        // Apply availability filter
        if (availableOnly) {
            filteredProducts = filteredProducts.filter { it.available }
        }

        // Apply discount filter
        if (discountOnly) {
            filteredProducts = filteredProducts.filter { it.discount > 0 }
        }

        searchResults.addAll(filteredProducts)
        updateUI()

        showToast("تم العثور على ${searchResults.size} منتج")
    }

    private fun getSelectedCategory(): String {
        return when (binding.spinnerCategory.selectedItemPosition) {
            0 -> "ALL"
            1 -> CategoryConstants.HOME_APPLIANCES
            2 -> CategoryConstants.ELECTRICAL_APPLIANCES
            3 -> CategoryConstants.HAND_TOOLS
            else -> "ALL"
        }
    }

    private fun clearAllFilters() {
        binding.etSearchQuery.text?.clear()
        binding.spinnerCategory.setSelection(0)
        binding.etMinPrice.text?.clear()
        binding.etMaxPrice.text?.clear()
        binding.cbAvailableOnly.isChecked = false
        binding.cbDiscountOnly.isChecked = false

        // Show all products
        searchResults.clear()
        searchResults.addAll(allProducts)
        updateUI()

        showToast("تم مسح جميع المرشحات")
    }

    private fun updateUI() {
        if (searchResults.isEmpty()) {
            binding.rvSearchResults.visibility = View.GONE
            binding.tvEmptyState.visibility = View.VISIBLE
            binding.tvEmptyState.text = "لم يتم العثور على منتجات مطابقة\nجرب تغيير معايير البحث"
        } else {
            binding.rvSearchResults.visibility = View.VISIBLE
            binding.tvEmptyState.visibility = View.GONE
            productAdapter.submitList(searchResults)
        }

        // Update results count
        binding.tvResultsCount.text = "النتائج: ${searchResults.size} من ${allProducts.size} منتج"
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
