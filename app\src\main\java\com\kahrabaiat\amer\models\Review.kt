package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.*

@Parcelize
data class Review(
    val id: String = "",
    val productId: Int = 0,
    val customerName: String = "",
    val customerPhone: String = "",
    val rating: Float = 0f, // من 1 إلى 5
    val comment: String = "",
    val reviewDate: Long = System.currentTimeMillis(),
    val isVerifiedPurchase: Boolean = false, // هل اشترى المنتج فعلاً
    val isApproved: Boolean = true, // موافقة المدير على المراجعة
    val helpfulCount: Int = 0, // عدد الأشخاص الذين وجدوا المراجعة مفيدة
    val reportCount: Int = 0 // عدد البلاغات على المراجعة
) : Parcelable {

    /**
     * تنسيق تاريخ المراجعة
     */
    fun getFormattedDate(): String {
        val date = Date(reviewDate)
        val formatter = SimpleDateFormat("dd/MM/yyyy", Locale("ar"))
        return formatter.format(date)
    }

    /**
     * تنسيق تاريخ ووقت المراجعة
     */
    fun getFormattedDateTime(): String {
        val date = Date(reviewDate)
        val formatter = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
        return formatter.format(date)
    }

    /**
     * الحصول على نص التقييم
     */
    fun getRatingText(): String {
        return when {
            rating >= 4.5f -> "ممتاز"
            rating >= 3.5f -> "جيد جداً"
            rating >= 2.5f -> "جيد"
            rating >= 1.5f -> "مقبول"
            else -> "ضعيف"
        }
    }

    /**
     * الحصول على لون التقييم
     */
    fun getRatingColor(): String {
        return when {
            rating >= 4.0f -> "#4CAF50" // أخضر
            rating >= 3.0f -> "#FF9800" // برتقالي
            rating >= 2.0f -> "#FFC107" // أصفر
            else -> "#F44336" // أحمر
        }
    }

    /**
     * التحقق من صحة المراجعة
     */
    fun isValid(): Boolean {
        return customerName.isNotBlank() &&
                rating > 0 &&
                rating <= 5 &&
                comment.isNotBlank() &&
                comment.length >= 10
    }

    /**
     * الحصول على ملخص المراجعة
     */
    fun getSummary(): String {
        val maxLength = 100
        return if (comment.length <= maxLength) {
            comment
        } else {
            comment.substring(0, maxLength) + "..."
        }
    }

    /**
     * تحديد ما إذا كانت المراجعة حديثة (أقل من أسبوع)
     */
    fun isRecent(): Boolean {
        val oneWeekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L)
        return reviewDate > oneWeekAgo
    }

    /**
     * تحديد ما إذا كانت المراجعة مفيدة (نسبة عالية من الإعجاب)
     */
    fun isHelpful(): Boolean {
        return helpfulCount > 2 && reportCount == 0
    }

    companion object {
        /**
         * إنشاء مراجعة جديدة
         */
        fun create(
            productId: Int,
            customerName: String,
            customerPhone: String,
            rating: Float,
            comment: String,
            isVerifiedPurchase: Boolean = false
        ): Review {
            return Review(
                id = generateReviewId(),
                productId = productId,
                customerName = customerName,
                customerPhone = customerPhone,
                rating = rating,
                comment = comment,
                reviewDate = System.currentTimeMillis(),
                isVerifiedPurchase = isVerifiedPurchase,
                isApproved = true // موافقة تلقائية، يمكن تغييرها لاحقاً
            )
        }

        /**
         * توليد معرف فريد للمراجعة
         */
        private fun generateReviewId(): String {
            return "review_${System.currentTimeMillis()}_${Random().nextInt(1000)}"
        }

        /**
         * حساب متوسط التقييمات
         */
        fun calculateAverageRating(reviews: List<Review>): Float {
            if (reviews.isEmpty()) return 0f
            val approvedReviews = reviews.filter { it.isApproved }
            if (approvedReviews.isEmpty()) return 0f
            
            val totalRating = approvedReviews.sumOf { it.rating.toDouble() }
            return (totalRating / approvedReviews.size).toFloat()
        }

        /**
         * حساب توزيع التقييمات
         */
        fun calculateRatingDistribution(reviews: List<Review>): Map<Int, Int> {
            val approvedReviews = reviews.filter { it.isApproved }
            val distribution = mutableMapOf<Int, Int>()
            
            for (i in 1..5) {
                distribution[i] = 0
            }
            
            approvedReviews.forEach { review ->
                val ratingInt = review.rating.toInt()
                distribution[ratingInt] = distribution[ratingInt]!! + 1
            }
            
            return distribution
        }

        /**
         * فلترة المراجعات حسب التقييم
         */
        fun filterByRating(reviews: List<Review>, minRating: Float): List<Review> {
            return reviews.filter { it.isApproved && it.rating >= minRating }
        }

        /**
         * ترتيب المراجعات (الأحدث أولاً، ثم الأكثر فائدة)
         */
        fun sortReviews(reviews: List<Review>): List<Review> {
            return reviews.filter { it.isApproved }
                .sortedWith(compareByDescending<Review> { it.isHelpful() }
                    .thenByDescending { it.reviewDate })
        }
    }
}

/**
 * إحصائيات المراجعات للمنتج
 */
@Parcelize
data class ProductReviewStats(
    val productId: Int,
    val totalReviews: Int = 0,
    val averageRating: Float = 0f,
    val ratingDistribution: Map<Int, Int> = emptyMap(), // توزيع التقييمات 1-5
    val verifiedPurchaseCount: Int = 0,
    val recentReviewsCount: Int = 0 // المراجعات في آخر 30 يوم
) : Parcelable {

    /**
     * الحصول على نص متوسط التقييم
     */
    fun getAverageRatingText(): String {
        return String.format(Locale("ar"), "%.1f", averageRating)
    }

    /**
     * الحصول على نسبة التقييمات الإيجابية (4 نجوم فأكثر)
     */
    fun getPositiveRatingPercentage(): Int {
        if (totalReviews == 0) return 0
        val positiveCount = (ratingDistribution[4] ?: 0) + (ratingDistribution[5] ?: 0)
        return ((positiveCount.toFloat() / totalReviews) * 100).toInt()
    }

    /**
     * تحديد ما إذا كان المنتج يحتوي على مراجعات كافية
     */
    fun hasSufficientReviews(): Boolean {
        return totalReviews >= 3
    }

    /**
     * الحصول على نص وصفي للتقييم العام
     */
    fun getOverallRatingDescription(): String {
        return when {
            averageRating >= 4.5f -> "ممتاز (${getAverageRatingText()})"
            averageRating >= 3.5f -> "جيد جداً (${getAverageRatingText()})"
            averageRating >= 2.5f -> "جيد (${getAverageRatingText()})"
            averageRating >= 1.5f -> "مقبول (${getAverageRatingText()})"
            averageRating > 0f -> "ضعيف (${getAverageRatingText()})"
            else -> "لا توجد تقييمات"
        }
    }
}
