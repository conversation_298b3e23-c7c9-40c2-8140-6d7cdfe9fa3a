<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="24dp"
    tools:context=".AdminLoginActivity">

    <!-- Logo/Icon -->
    <androidx.cardview.widget.CardView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        app:cardCornerRadius="60dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@color/white">

        <ImageView
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_admin"
            android:tint="@color/primary_blue" />

    </androidx.cardview.widget.CardView>

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="@string/admin_login"
        android:textColor="@color/white"
        android:textSize="28sp"
        android:textStyle="bold"
        android:letterSpacing="-0.02" />

    <!-- Login Form Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="20dp"
        app:cardElevation="12dp"
        app:cardBackgroundColor="@color/white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Username Field -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="اسم المستخدم"
                app:startIconDrawable="@drawable/ic_person">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etUsername"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password Field -->
            <com.google.android.material.textfield.TextInputLayout
                style="@style/InputField"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="كلمة المرور"
                app:startIconDrawable="@drawable/ic_lock"
                app:endIconMode="password_toggle">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Login Button -->
            <Button
                android:id="@+id/btnLogin"
                style="@style/PrimaryButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/login" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Back Button -->
    <TextView
        android:id="@+id/tvBack"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:background="?attr/selectableItemBackground"
        android:padding="16dp"
        android:text="العودة للرئيسية"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:drawableStart="@drawable/ic_arrow_back"
        android:drawableTint="@color/white"
        android:drawablePadding="8dp" />

</LinearLayout>
