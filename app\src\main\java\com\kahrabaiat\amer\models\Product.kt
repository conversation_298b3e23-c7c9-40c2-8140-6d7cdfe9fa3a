package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Product(
    val id: Int = 0,
    val name: String = "",
    val price: Double = 0.0,
    val description: String = "",
    val imageUrl: String = "",
    val category: String = "",
    val stock: Int = 0,
    val available: Boolean = true,
    val discount: Int = 0, // Discount percentage
    val createdAt: Long = System.currentTimeMillis()
) : Parcelable {

    fun getDiscountedPrice(): Double {
        return if (discount > 0) {
            price * (100 - discount) / 100
        } else {
            price
        }
    }

    fun isInStock(): Boolean {
        return available && stock > 0
    }

    fun getFormattedPrice(): String {
        return "${getDiscountedPrice().toInt()} دينار عراقي"
    }

    fun getOriginalFormattedPrice(): String {
        return "${price.toInt()} دينار عراقي"
    }
}
