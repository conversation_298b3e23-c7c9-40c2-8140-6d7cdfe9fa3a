<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <!-- Product Image -->
    <ImageView
        android:id="@+id/ivProductImage"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:background="@color/light_gray"
        android:scaleType="centerCrop"
        tools:src="@drawable/ic_product_placeholder" />

    <!-- Product Info -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- Product Name -->
        <TextView
            android:id="@+id/tvProductName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="ثلاجة سامسونج 18 قدم" />

        <!-- Quantity and Price -->
        <TextView
            android:id="@+id/tvQuantityPrice"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            tools:text="الكمية: 2 × 1200 دينار عراقي" />

    </LinearLayout>

    <!-- Total Price -->
    <TextView
        android:id="@+id/tvTotalPrice"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:textColor="@color/primary_blue"
        android:textSize="14sp"
        android:textStyle="bold"
        tools:text="2400 د.ع" />

</LinearLayout>
