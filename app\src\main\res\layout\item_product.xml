<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="12dp"
    app:cardCornerRadius="20dp"
    app:cardElevation="8dp"
    app:cardBackgroundColor="@color/background_card"
    app:strokeWidth="1dp"
    app:strokeColor="@color/border_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Product Image -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="180dp">

            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/light_gray"
                android:scaleType="centerCrop"
                tools:src="@drawable/ic_product_placeholder" />

            <!-- Discount Badge -->
            <TextView
                android:id="@+id/tvDiscount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@drawable/discount_badge_background"
                android:padding="4dp"
                android:text="خصم 20%"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible" />

            <!-- Stock Status -->
            <TextView
                android:id="@+id/tvStockStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:layout_margin="8dp"
                android:background="@drawable/stock_badge_background"
                android:padding="4dp"
                android:text="@string/available"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold" />

        </FrameLayout>

        <!-- Product Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="vertical">

            <!-- Product Name -->
            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="2"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="ثلاجة سامسونج 18 قدم" />

            <!-- Price Section -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- Current Price -->
                <TextView
                    android:id="@+id/tvCurrentPrice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/primary_blue"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="1200 دينار عراقي" />

                <!-- Original Price (if discounted) -->
                <TextView
                    android:id="@+id/tvOriginalPrice"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:visibility="gone"
                    tools:text="1500 دينار عراقي"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- Add to Cart Button -->
                <Button
                    android:id="@+id/btnAddToCart"
                    style="@style/PrimaryButton"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="@string/add_to_cart"
                    android:drawableStart="@drawable/ic_shopping_cart"
                    android:drawableTint="@color/white"
                    android:drawablePadding="8dp" />

                <!-- Favorite Button -->
                <ImageButton
                    android:id="@+id/btnFavorite"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/favorite_button_background"
                    android:src="@drawable/ic_heart"
                    android:tint="@color/medium_gray"
                    android:contentDescription="إضافة للمفضلة" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
