package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.provider.Settings
import android.view.MenuItem
import androidx.appcompat.app.AppCompatActivity
import com.kahrabaiat.amer.databinding.ActivityNotificationSettingsBinding
import com.kahrabaiat.amer.utils.NotificationHelper

class NotificationSettingsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityNotificationSettingsBinding
    private lateinit var notificationHelper: NotificationHelper

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityNotificationSettingsBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            loadSettings()
            setupClickListeners()
        } catch (e: Exception) {
            android.util.Log.e("NotificationSettingsActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل إعدادات الإشعارات: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        notificationHelper = NotificationHelper(this)
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "إعدادات الإشعارات"
            }
        } catch (e: Exception) {
            android.util.Log.e("NotificationSettingsActivity", "Error setting up toolbar", e)
        }
    }

    private fun loadSettings() {
        try {
            // Load current notification settings
            binding.switchNewOrders.isChecked = notificationHelper.isNotificationEnabled(NotificationHelper.PREF_NEW_ORDERS)
            binding.switchOrderUpdates.isChecked = notificationHelper.isNotificationEnabled(NotificationHelper.PREF_ORDER_UPDATES)
            binding.switchGeneral.isChecked = notificationHelper.isNotificationEnabled(NotificationHelper.PREF_GENERAL)

            updateSummaryTexts()
        } catch (e: Exception) {
            android.util.Log.e("NotificationSettingsActivity", "Error loading settings", e)
        }
    }

    private fun setupClickListeners() {
        // New Orders Switch
        binding.switchNewOrders.setOnCheckedChangeListener { _, isChecked ->
            notificationHelper.setNotificationEnabled(NotificationHelper.PREF_NEW_ORDERS, isChecked)
            updateSummaryTexts()

            if (isChecked) {
                showToast("تم تفعيل إشعارات الطلبات الجديدة")
            } else {
                showToast("تم إلغاء إشعارات الطلبات الجديدة")
            }
        }

        // Order Updates Switch
        binding.switchOrderUpdates.setOnCheckedChangeListener { _, isChecked ->
            notificationHelper.setNotificationEnabled(NotificationHelper.PREF_ORDER_UPDATES, isChecked)
            updateSummaryTexts()

            if (isChecked) {
                showToast("تم تفعيل إشعارات تحديثات الطلبات")
            } else {
                showToast("تم إلغاء إشعارات تحديثات الطلبات")
            }
        }

        // General Switch
        binding.switchGeneral.setOnCheckedChangeListener { _, isChecked ->
            notificationHelper.setNotificationEnabled(NotificationHelper.PREF_GENERAL, isChecked)
            updateSummaryTexts()

            if (isChecked) {
                showToast("تم تفعيل الإشعارات العامة")
            } else {
                showToast("تم إلغاء الإشعارات العامة")
            }
        }

        // Test Notifications
        binding.btnTestNewOrder.setOnClickListener {
            testNewOrderNotification()
        }

        binding.btnTestStatusUpdate.setOnClickListener {
            testStatusUpdateNotification()
        }

        binding.btnTestGeneral.setOnClickListener {
            testGeneralNotification()
        }

        // System Settings
        binding.btnSystemSettings.setOnClickListener {
            openSystemNotificationSettings()
        }

        // Clear All Notifications
        binding.btnClearAll.setOnClickListener {
            notificationHelper.clearAllNotifications()
            showToast("تم مسح جميع الإشعارات")
        }
    }

    private fun updateSummaryTexts() {
        // Update summary texts based on current settings
        binding.tvNewOrdersSummary.text = if (binding.switchNewOrders.isChecked) {
            "سيتم إرسال إشعار عند وصول طلب جديد"
        } else {
            "لن يتم إرسال إشعارات للطلبات الجديدة"
        }

        binding.tvOrderUpdatesSummary.text = if (binding.switchOrderUpdates.isChecked) {
            "سيتم إرسال إشعار عند تغيير حالة الطلب"
        } else {
            "لن يتم إرسال إشعارات لتحديثات الطلبات"
        }

        binding.tvGeneralSummary.text = if (binding.switchGeneral.isChecked) {
            "سيتم إرسال الإشعارات العامة والتذكيرات"
        } else {
            "لن يتم إرسال الإشعارات العامة"
        }
    }

    private fun testNewOrderNotification() {
        try {
            // Create a test order for notification
            val testOrder = com.kahrabaiat.amer.models.Order(
                id = 999,
                customerName = "أحمد محمد",
                customerPhone = "07901234567",
                customerAddress = "بغداد - الكرادة",
                items = mutableListOf(),
                total = 150000.0,
                status = "pending",
                orderDate = System.currentTimeMillis()
            )

            notificationHelper.sendNewOrderNotification(testOrder)
            showToast("تم إرسال إشعار تجريبي للطلب الجديد")
        } catch (e: Exception) {
            showToast("خطأ في إرسال الإشعار التجريبي: ${e.message}")
        }
    }

    private fun testStatusUpdateNotification() {
        try {
            // Create a test order for status update notification
            val testOrder = com.kahrabaiat.amer.models.Order(
                id = 998,
                customerName = "فاطمة علي",
                customerPhone = "07701234567",
                customerAddress = "البصرة - المعقل",
                items = mutableListOf(),
                total = 200000.0,
                status = "delivered",
                orderDate = System.currentTimeMillis()
            )

            notificationHelper.sendOrderStatusUpdateNotification(
                testOrder,
                "shipped",
                "delivered"
            )
            showToast("تم إرسال إشعار تجريبي لتحديث الحالة")
        } catch (e: Exception) {
            showToast("خطأ في إرسال الإشعار التجريبي: ${e.message}")
        }
    }

    private fun testGeneralNotification() {
        try {
            notificationHelper.sendGeneralNotification(
                "إشعار تجريبي",
                "هذا إشعار تجريبي للتأكد من عمل النظام بشكل صحيح. جميع الإعدادات تعمل بشكل طبيعي.",
                false
            )
            showToast("تم إرسال إشعار عام تجريبي")
        } catch (e: Exception) {
            showToast("خطأ في إرسال الإشعار التجريبي: ${e.message}")
        }
    }

    private fun openSystemNotificationSettings() {
        try {
            val intent = Intent().apply {
                action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
                putExtra(Settings.EXTRA_APP_PACKAGE, packageName)
            }
            startActivity(intent)
        } catch (e: Exception) {
            // Fallback to general settings
            try {
                val intent = Intent(Settings.ACTION_SETTINGS)
                startActivity(intent)
            } catch (ex: Exception) {
                showToast("لا يمكن فتح إعدادات النظام")
            }
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
