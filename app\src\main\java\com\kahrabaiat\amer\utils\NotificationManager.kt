package com.kahrabaiat.amer.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.messaging.FirebaseMessaging
import com.kahrabaiat.amer.MainActivity
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.models.Notification
import com.kahrabaiat.amer.models.NotificationAudience
import com.kahrabaiat.amer.models.NotificationPriority
import com.kahrabaiat.amer.models.NotificationSettings
import com.kahrabaiat.amer.models.NotificationType
import kotlinx.coroutines.tasks.await

/**
 * مدير الإشعارات المتقدم
 */
class AppNotificationManager private constructor(private val context: Context) {

    private val firestore = FirebaseFirestore.getInstance()
    private val systemNotificationManager = NotificationManagerCompat.from(context)
    private val fcm = FirebaseMessaging.getInstance()
    
    companion object {
        private const val TAG = "NotificationManager"
        private const val NOTIFICATIONS_COLLECTION = "notifications"
        private const val NOTIFICATION_SETTINGS_COLLECTION = "notification_settings"
        
        // قنوات الإشعارات
        private const val CHANNEL_NEW_PRODUCTS = "new_products"
        private const val CHANNEL_ORDER_UPDATES = "order_updates"
        private const val CHANNEL_PROMOTIONS = "promotions"
        private const val CHANNEL_REVIEWS = "reviews"
        private const val CHANNEL_STOCK_ALERTS = "stock_alerts"
        private const val CHANNEL_SYSTEM = "system"
        private const val CHANNEL_GENERAL = "general"
        
        @Volatile
        private var INSTANCE: AppNotificationManager? = null

        fun getInstance(context: Context): AppNotificationManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AppNotificationManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    init {
        createNotificationChannels()
        subscribeToFCMTopics()
    }

    /**
     * إنشاء قنوات الإشعارات
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channels = listOf(
                NotificationChannel(
                    CHANNEL_NEW_PRODUCTS,
                    "منتجات جديدة",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "إشعارات المنتجات الجديدة"
                },
                NotificationChannel(
                    CHANNEL_ORDER_UPDATES,
                    "تحديثات الطلبات",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "إشعارات حالة الطلبات"
                },
                NotificationChannel(
                    CHANNEL_PROMOTIONS,
                    "العروض والخصومات",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "إشعارات العروض والخصومات"
                },
                NotificationChannel(
                    CHANNEL_REVIEWS,
                    "المراجعات",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "إشعارات المراجعات الجديدة"
                },
                NotificationChannel(
                    CHANNEL_STOCK_ALERTS,
                    "تنبيهات المخزون",
                    NotificationManager.IMPORTANCE_HIGH
                ).apply {
                    description = "تنبيهات نفاد المخزون"
                },
                NotificationChannel(
                    CHANNEL_SYSTEM,
                    "إشعارات النظام",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "إشعارات النظام والتحديثات"
                },
                NotificationChannel(
                    CHANNEL_GENERAL,
                    "إشعارات عامة",
                    NotificationManager.IMPORTANCE_DEFAULT
                ).apply {
                    description = "إشعارات عامة"
                }
            )

            val androidNotificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
            channels.forEach { channel ->
                androidNotificationManager.createNotificationChannel(channel)
            }
        }
    }

    /**
     * الاشتراك في مواضيع FCM
     */
    private fun subscribeToFCMTopics() {
        try {
            fcm.subscribeToTopic("all_customers")
            fcm.subscribeToTopic("new_products")
            fcm.subscribeToTopic("promotions")
            Log.d(TAG, "Subscribed to FCM topics")
        } catch (e: Exception) {
            Log.e(TAG, "Error subscribing to FCM topics", e)
        }
    }

    /**
     * إرسال إشعار محلي
     */
    fun sendLocalNotification(notification: Notification) {
        try {
            val channelId = getChannelId(notification.type)
            val intent = createNotificationIntent(notification)
            val pendingIntent = PendingIntent.getActivity(
                context,
                notification.id.hashCode(),
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val notificationBuilder = NotificationCompat.Builder(context, channelId)
                .setSmallIcon(R.drawable.ic_notification)
                .setContentTitle(notification.title)
                .setContentText(notification.message)
                .setStyle(NotificationCompat.BigTextStyle().bigText(notification.message))
                .setPriority(getNotificationPriority(notification.priority))
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setShowWhen(true)
                .setWhen(notification.createdAt)

            // إضافة أيقونة كبيرة إذا كانت متوفرة
            if (notification.imageUrl.isNotEmpty()) {
                try {
                    // يمكن تحسين هذا لاحقاً لتحميل الصورة من URL
                    val largeIcon = BitmapFactory.decodeResource(context.resources, R.drawable.ic_product_placeholder)
                    notificationBuilder.setLargeIcon(largeIcon)
                } catch (e: Exception) {
                    Log.w(TAG, "Could not load notification image", e)
                }
            }

            // إضافة إجراءات حسب نوع الإشعار
            addNotificationActions(notificationBuilder, notification)

            systemNotificationManager.notify(notification.id.hashCode(), notificationBuilder.build())
            Log.d(TAG, "Local notification sent: ${notification.title}")

        } catch (e: Exception) {
            Log.e(TAG, "Error sending local notification", e)
        }
    }

    /**
     * حفظ الإشعار في Firebase
     */
    suspend fun saveNotification(notification: Notification): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val notificationData = hashMapOf(
                    "title" to notification.title,
                    "message" to notification.message,
                    "type" to notification.type.name,
                    "targetAudience" to notification.targetAudience.name,
                    "relatedId" to notification.relatedId,
                    "imageUrl" to notification.imageUrl,
                    "actionUrl" to notification.actionUrl,
                    "isRead" to notification.isRead,
                    "isActive" to notification.isActive,
                    "createdAt" to notification.createdAt,
                    "scheduledAt" to notification.scheduledAt,
                    "expiresAt" to notification.expiresAt,
                    "priority" to notification.priority.name,
                    "data" to notification.data
                )

                firestore.collection(NOTIFICATIONS_COLLECTION)
                    .document(notification.id)
                    .set(notificationData)
                    .await()
            }

            Log.i(TAG, "Notification saved: ${notification.id}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error saving notification", e)
            false
        }
    }

    /**
     * إرسال إشعار شامل (محلي + Firebase)
     */
    suspend fun sendNotification(notification: Notification): Boolean {
        return try {
            // حفظ في Firebase
            val saved = saveNotification(notification)
            
            // إرسال محلي
            sendLocalNotification(notification)
            
            // إرسال FCM للمستخدمين الآخرين (إذا كان مطلوب)
            if (notification.targetAudience != NotificationAudience.SPECIFIC_CUSTOMER) {
                sendFCMNotification(notification)
            }
            
            saved
        } catch (e: Exception) {
            Log.e(TAG, "Error sending notification", e)
            false
        }
    }

    /**
     * إرسال إشعار FCM
     */
    private suspend fun sendFCMNotification(notification: Notification) {
        try {
            // هذا يتطلب إعداد Firebase Functions أو خدمة خلفية
            // للآن سنكتفي بالإشعارات المحلية
            Log.d(TAG, "FCM notification would be sent: ${notification.title}")
        } catch (e: Exception) {
            Log.e(TAG, "Error sending FCM notification", e)
        }
    }

    /**
     * الحصول على جميع الإشعارات
     */
    suspend fun getAllNotifications(limit: Int = 50): List<Notification> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(NOTIFICATIONS_COLLECTION)
                    .whereEqualTo("isActive", true)
                    .orderBy("createdAt", Query.Direction.DESCENDING)
                    .limit(limit.toLong())
                    .get()
                    .await()

                snapshot.documents.mapNotNull { doc ->
                    try {
                        Notification(
                            id = doc.id,
                            title = doc.getString("title") ?: "",
                            message = doc.getString("message") ?: "",
                            type = NotificationType.valueOf(doc.getString("type") ?: "GENERAL"),
                            targetAudience = NotificationAudience.valueOf(doc.getString("targetAudience") ?: "ALL"),
                            relatedId = doc.getString("relatedId") ?: "",
                            imageUrl = doc.getString("imageUrl") ?: "",
                            actionUrl = doc.getString("actionUrl") ?: "",
                            isRead = doc.getBoolean("isRead") ?: false,
                            isActive = doc.getBoolean("isActive") ?: true,
                            createdAt = doc.getLong("createdAt") ?: 0L,
                            scheduledAt = doc.getLong("scheduledAt") ?: 0L,
                            expiresAt = doc.getLong("expiresAt") ?: 0L,
                            priority = NotificationPriority.valueOf(doc.getString("priority") ?: "NORMAL"),
                            data = doc.get("data") as? Map<String, String> ?: emptyMap()
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing notification", e)
                        null
                    }
                }
            } else {
                // إرجاع إشعارات تجريبية
                getSampleNotifications()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting notifications", e)
            emptyList()
        }
    }

    /**
     * تحديد قناة الإشعار حسب النوع
     */
    private fun getChannelId(type: NotificationType): String {
        return when (type) {
            NotificationType.NEW_PRODUCT -> CHANNEL_NEW_PRODUCTS
            NotificationType.ORDER_STATUS -> CHANNEL_ORDER_UPDATES
            NotificationType.PROMOTION -> CHANNEL_PROMOTIONS
            NotificationType.REVIEW -> CHANNEL_REVIEWS
            NotificationType.STOCK_ALERT -> CHANNEL_STOCK_ALERTS
            NotificationType.SYSTEM -> CHANNEL_SYSTEM
            NotificationType.GENERAL -> CHANNEL_GENERAL
        }
    }

    /**
     * تحويل أولوية الإشعار
     */
    private fun getNotificationPriority(priority: NotificationPriority): Int {
        return when (priority) {
            NotificationPriority.LOW -> NotificationCompat.PRIORITY_LOW
            NotificationPriority.NORMAL -> NotificationCompat.PRIORITY_DEFAULT
            NotificationPriority.HIGH -> NotificationCompat.PRIORITY_HIGH
            NotificationPriority.URGENT -> NotificationCompat.PRIORITY_MAX
        }
    }

    /**
     * إنشاء Intent للإشعار
     */
    private fun createNotificationIntent(notification: Notification): Intent {
        val intent = Intent(context, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        
        // إضافة بيانات إضافية حسب نوع الإشعار
        intent.putExtra("notification_id", notification.id)
        intent.putExtra("action_url", notification.actionUrl)
        intent.putExtra("related_id", notification.relatedId)
        
        return intent
    }

    /**
     * إضافة إجراءات للإشعار
     */
    private fun addNotificationActions(
        builder: NotificationCompat.Builder,
        notification: Notification
    ) {
        when (notification.type) {
            NotificationType.ORDER_STATUS -> {
                // إضافة زر "عرض الطلب"
                val viewIntent = createNotificationIntent(notification)
                val viewPendingIntent = PendingIntent.getActivity(
                    context,
                    (notification.id + "_view").hashCode(),
                    viewIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                builder.addAction(R.drawable.ic_visibility, "عرض الطلب", viewPendingIntent)
            }
            NotificationType.PROMOTION -> {
                // إضافة زر "عرض العرض"
                val promoIntent = createNotificationIntent(notification)
                val promoPendingIntent = PendingIntent.getActivity(
                    context,
                    (notification.id + "_promo").hashCode(),
                    promoIntent,
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
                builder.addAction(R.drawable.ic_visibility, "عرض العرض", promoPendingIntent)
            }
            else -> {
                // إجراء افتراضي
            }
        }
    }

    /**
     * إشعارات تجريبية للاختبار
     */
    private fun getSampleNotifications(): List<Notification> {
        return listOf(
            Notification.createNewProductNotification(
                "مثقاب كهربائي جديد",
                "1"
            ),
            Notification.createPromotionNotification(
                "خصم 20% على جميع المنتجات!",
                "استخدم كود SAVE20 واحصل على خصم 20% على جميع المنتجات",
                "SAVE20"
            ),
            Notification.createOrderStatusNotification(
                "12345",
                "07901234567",
                "confirmed"
            )
        )
    }
}
