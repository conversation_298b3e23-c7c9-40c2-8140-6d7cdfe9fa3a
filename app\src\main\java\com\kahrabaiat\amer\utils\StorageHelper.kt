package com.kahrabaiat.amer.utils

import android.content.Context
import android.net.Uri
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.StorageReference
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.delay
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.util.*

class StorageHelper {

    private val storage = FirebaseStorage.getInstance()
    private val storageRef = storage.reference

    companion object {
        private const val PRODUCTS_FOLDER = "products"
        private const val CATEGORIES_FOLDER = "categories"
        private const val USERS_FOLDER = "users"
    }

    /**
     * رفع صورة منتج إلى Firebase Storage
     */
    suspend fun uploadProductImage(imageFile: File, productId: String, context: Context? = null): Result<String> {
        return try {
            if (!imageFile.exists()) {
                android.util.Log.e("StorageHelper", "Image file does not exist: ${imageFile.absolutePath}")
                return Result.failure(Exception("Image file not found"))
            }

            val fileName = "product_${productId}_${System.currentTimeMillis()}.jpg"
            val imageRef = storageRef.child("$PRODUCTS_FOLDER/$fileName")

            val uri = Uri.fromFile(imageFile)
            val uploadTask = imageRef.putFile(uri).await()

            val downloadUrl = imageRef.downloadUrl.await()
            android.util.Log.d("StorageHelper", "Image uploaded successfully: ${downloadUrl}")
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            android.util.Log.e("StorageHelper", "Error uploading product image", e)

            // Fallback to local storage in case of Firebase error
            if (context != null) {
                try {
                    val internalDir = File(context.filesDir, "product_images")
                    if (!internalDir.exists()) {
                        internalDir.mkdirs()
                    }

                    val fileName = "product_${productId}_${System.currentTimeMillis()}.jpg"
                    val destinationFile = File(internalDir, fileName)

                    withContext(Dispatchers.IO) {
                        FileInputStream(imageFile).use { input ->
                            FileOutputStream(destinationFile).use { output ->
                                input.copyTo(output)
                            }
                        }
                    }

                    val localImageUrl = "file://${destinationFile.absolutePath}"
                    android.util.Log.d("StorageHelper", "Fallback to local storage: $localImageUrl")
                    Result.success(localImageUrl)
                } catch (localError: Exception) {
                    android.util.Log.e("StorageHelper", "Error in local fallback", localError)
                    Result.failure(e)
                }
            } else {
                Result.failure(e)
            }
        }
    }

    /**
     * رفع صورة فئة إلى Firebase Storage
     */
    suspend fun uploadCategoryImage(imageFile: File, categoryId: String): Result<String> {
        return try {
            val fileName = "category_${categoryId}_${System.currentTimeMillis()}.jpg"
            val imageRef = storageRef.child("$CATEGORIES_FOLDER/$fileName")

            val uri = Uri.fromFile(imageFile)
            val uploadTask = imageRef.putFile(uri).await()

            val downloadUrl = imageRef.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            android.util.Log.e("StorageHelper", "Error uploading category image", e)
            Result.failure(e)
        }
    }

    /**
     * رفع صورة مستخدم إلى Firebase Storage
     */
    suspend fun uploadUserImage(imageFile: File, userId: String): Result<String> {
        return try {
            val fileName = "user_${userId}_${System.currentTimeMillis()}.jpg"
            val imageRef = storageRef.child("$USERS_FOLDER/$fileName")

            val uri = Uri.fromFile(imageFile)
            val uploadTask = imageRef.putFile(uri).await()

            val downloadUrl = imageRef.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            android.util.Log.e("StorageHelper", "Error uploading user image", e)
            Result.failure(e)
        }
    }

    /**
     * حذف صورة من Firebase Storage
     */
    suspend fun deleteImage(imageUrl: String): Result<Boolean> {
        return try {
            val imageRef = storage.getReferenceFromUrl(imageUrl)
            imageRef.delete().await()
            Result.success(true)
        } catch (e: Exception) {
            android.util.Log.e("StorageHelper", "Error deleting image", e)
            Result.failure(e)
        }
    }

    /**
     * رفع صورة عامة مع مسار مخصص
     */
    suspend fun uploadImage(imageFile: File, path: String, fileName: String): Result<String> {
        return try {
            val imageRef = storageRef.child("$path/$fileName")

            val uri = Uri.fromFile(imageFile)
            val uploadTask = imageRef.putFile(uri).await()

            val downloadUrl = imageRef.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            android.util.Log.e("StorageHelper", "Error uploading image", e)
            Result.failure(e)
        }
    }

    /**
     * الحصول على مرجع لمجلد المنتجات
     */
    fun getProductsReference(): StorageReference {
        return storageRef.child(PRODUCTS_FOLDER)
    }

    /**
     * الحصول على مرجع لمجلد الفئات
     */
    fun getCategoriesReference(): StorageReference {
        return storageRef.child(CATEGORIES_FOLDER)
    }

    /**
     * الحصول على مرجع لمجلد المستخدمين
     */
    fun getUsersReference(): StorageReference {
        return storageRef.child(USERS_FOLDER)
    }
}
