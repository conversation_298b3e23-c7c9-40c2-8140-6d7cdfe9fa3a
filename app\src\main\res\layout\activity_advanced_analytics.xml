<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".AdvancedAnalyticsActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="32dp"
                    android:visibility="gone" />

                <!-- Analytics Tabs -->
                <com.google.android.material.chip.ChipGroup
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:chipSpacingHorizontal="8dp"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipSalesAnalytics"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="تحليلات المبيعات"
                        app:chipIcon="@drawable/ic_chart" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipCustomerAnalytics"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تحليلات العملاء"
                        app:chipIcon="@drawable/ic_people" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipProductAnalytics"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تحليلات المنتجات"
                        app:chipIcon="@drawable/ic_inventory" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chipTrendAnalytics"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="تحليلات الاتجاهات"
                        app:chipIcon="@drawable/ic_trending_up" />

                </com.google.android.material.chip.ChipGroup>

                <!-- Sales Analytics Section -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardSalesAnalytics"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:text="📊 تحليلات المبيعات"
                            android:textColor="@color/primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- Sales Metrics -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalRevenueSales"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    tools:text="2,500,000 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي الإيرادات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvRevenueGrowth"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    tools:text="+15%" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalOrdersSales"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    tools:text="150" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي الطلبات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/tvOrderGrowth"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="10sp"
                                    tools:text="+8%" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvAverageOrderValueSales"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/accent_teal"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="16,667 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="متوسط قيمة الطلب"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvConversionRate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/accent_orange"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="85%" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="معدل التحويل"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Charts -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="حجم المبيعات اليومية"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/lineChartSalesVolume"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="نمو الإيرادات الشهرية"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/barChartRevenueGrowth"
                            android:layout_width="match_parent"
                            android:layout_height="200dp"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="معدل التحويل"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/pieChartSalesConversion"
                            android:layout_width="match_parent"
                            android:layout_height="200dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
