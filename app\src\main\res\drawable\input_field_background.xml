<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Focused State -->
    <item android:state_focused="true">
        <layer-list>
            <!-- Glow Effect -->
            <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="2dp">
                <shape android:shape="rectangle">
                    <solid android:color="#201976D2" />
                    <corners android:radius="14dp" />
                </shape>
            </item>

            <!-- Input Background -->
            <item android:top="4dp" android:left="4dp" android:right="4dp" android:bottom="4dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:angle="135"
                        android:startColor="#FFFFFF"
                        android:endColor="#F8FAFC"
                        android:type="linear" />
                    <stroke
                        android:width="2dp"
                        android:color="@color/primary_blue" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- Normal State -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:angle="135"
                android:startColor="#FFFFFF"
                android:endColor="#FAFAFA"
                android:type="linear" />
            <stroke
                android:width="1dp"
                android:color="@color/border_light" />
            <corners android:radius="12dp" />
        </shape>
    </item>

</selector>
