<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <application
        android:name=".KahrabaiatAmerApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@drawable/ic_app_logo_alt"
        android:label="@string/app_name"
        android:roundIcon="@drawable/ic_app_logo_alt"
        android:supportsRtl="true"
        android:theme="@style/Theme.KahrabaiatAmer"
        android:hardwareAccelerated="true"
        tools:targetApi="31">

        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ProductsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".ProductDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".CartActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".CheckoutActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".AdminLoginActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".AdminActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".AddProductActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".ManageProductsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".InventorySettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".SecuritySettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".PerformanceMonitorActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".OrdersActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".ExportActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".OrderDetailsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".EditOrderActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".AdvancedSearchActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".StatisticsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />



        <activity
            android:name=".NotificationSettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".CustomReportsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".AdvancedAnalyticsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".FirebaseTestActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".AddReviewActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".NotificationsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".CouponsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <activity
            android:name=".UserManagementActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.KahrabaiatAmer.NoActionBar" />

        <service
            android:name=".services.MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>
