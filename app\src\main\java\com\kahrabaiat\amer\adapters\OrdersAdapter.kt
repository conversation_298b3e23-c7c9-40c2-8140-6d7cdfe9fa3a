package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemOrderBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class OrdersAdapter(
    private val onOrderClick: (Order) -> Unit,
    private val onStatusChange: (Order, String) -> Unit,
    private val onDeleteOrder: (Order) -> Unit
) : RecyclerView.Adapter<OrdersAdapter.OrderViewHolder>() {

    private var orders = mutableListOf<Order>()

    fun updateOrders(newOrders: List<Order>) {
        orders.clear()
        orders.addAll(newOrders)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderViewHolder {
        val binding = ItemOrderBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OrderViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderViewHolder, position: Int) {
        holder.bind(orders[position])
    }

    override fun getItemCount(): Int = orders.size

    inner class OrderViewHolder(
        private val binding: ItemOrderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(order: Order) {
            with(binding) {
                // Order basic info
                tvOrderNumber.text = "طلب #${order.id}"
                tvCustomerName.text = order.customerName
                tvCustomerPhone.text = order.customerPhone
                tvCustomerAddress.text = order.customerAddress

                // Format amount
                val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
                tvTotalAmount.text = "${numberFormat.format(order.total.toInt())} د.ع"

                // Format date
                val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("ar"))
                tvOrderDate.text = dateFormat.format(Date(order.orderDate))

                // Status
                updateStatusDisplay(order.status)

                // Click listeners
                btnViewDetails.setOnClickListener { onOrderClick(order) }
                btnChangeStatus.setOnClickListener { showStatusChangeDialog(order) }
                btnDelete.setOnClickListener { onDeleteOrder(order) }

                // Card click for details
                root.setOnClickListener { onOrderClick(order) }
            }
        }

        private fun updateStatusDisplay(status: String) {
            with(binding.tvStatus) {
                when (status) {
                    "pending" -> {
                        text = "معلق"
                        setBackgroundResource(R.drawable.bg_status_pending)
                    }
                    "confirmed" -> {
                        text = "مؤكد"
                        setBackgroundResource(R.drawable.bg_status_confirmed)
                    }
                    "processing" -> {
                        text = "قيد المعالجة"
                        setBackgroundResource(R.drawable.bg_status_confirmed)
                    }
                    "shipped" -> {
                        text = "مشحون"
                        setBackgroundResource(R.drawable.bg_status_shipped)
                    }
                    "delivered" -> {
                        text = "مسلم"
                        setBackgroundResource(R.drawable.bg_status_delivered)
                    }
                    "cancelled" -> {
                        text = "ملغي"
                        setBackgroundResource(R.drawable.bg_status_cancelled)
                    }
                    else -> {
                        text = "غير محدد"
                        setBackgroundResource(R.drawable.bg_status_pending)
                    }
                }
            }
        }

        private fun showStatusChangeDialog(order: Order) {
            val context = binding.root.context
            val statusOptions = arrayOf(
                "معلق",
                "مؤكد",
                "قيد المعالجة",
                "مشحون",
                "مسلم",
                "ملغي"
            )

            val statusValues = arrayOf(
                "pending",
                "confirmed",
                "processing",
                "shipped",
                "delivered",
                "cancelled"
            )

            val currentIndex = statusValues.indexOf(order.status)

            AlertDialog.Builder(context)
                .setTitle("تغيير حالة الطلب")
                .setSingleChoiceItems(statusOptions, currentIndex) { dialog, which ->
                    val newStatus = statusValues[which]
                    if (newStatus != order.status) {
                        onStatusChange(order, newStatus)
                    }
                    dialog.dismiss()
                }
                .setNegativeButton("إلغاء", null)
                .show()
        }
    }
}
