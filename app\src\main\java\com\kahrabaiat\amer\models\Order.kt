package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
data class Order(
    val id: Int = 0,
    val customerName: String = "",
    val customerPhone: String = "",
    val customerAddress: String = "",
    val items: List<OrderItem> = emptyList(),
    val total: Double = 0.0,
    val status: String = "pending",
    val orderDate: Long = System.currentTimeMillis(),
    val notes: String = ""
) : Parcelable {

    fun getFormattedTotal(): String {
        return "${total.toInt()} دينار عراقي"
    }

    fun getFormattedDate(): String {
        val date = java.util.Date(orderDate)
        val formatter = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale("ar"))
        return formatter.format(date)
    }

    fun generateOrderNumber(): String {
        val year = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
        val timestamp = System.currentTimeMillis().toString().takeLast(6)
        return "$year-$timestamp"
    }

    @Parcelize
    data class OrderItem(
        val productId: Int,
        val productName: String,
        val price: Double,
        val quantity: Int,
        val total: Double = price * quantity
    ) : Parcelable
}

enum class OrderStatus {
    PENDING,
    CONFIRMED,
    PROCESSING,
    SHIPPED,
    DELIVERED,
    CANCELLED
}
