<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- Stats and Actions Bar -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Stats -->
                <TextView
                    android:id="@+id/tvCouponStats"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="الكوبونات المتاحة: 0"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:gravity="center"
                    android:layout_marginBottom="12dp"
                    android:visibility="gone" />

                <!-- Filter Buttons (Admin Only) -->
                <LinearLayout
                    android:id="@+id/layoutFilters"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center"
                    android:layout_marginBottom="12dp"
                    android:visibility="gone">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnFilterAll"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="36dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="4dp"
                        android:text="الكل"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        app:cornerRadius="18dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnFilterActive"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="36dp"
                        android:layout_weight="1"
                        android:layout_marginHorizontal="4dp"
                        android:text="نشط"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        app:cornerRadius="18dp" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btnFilterExpired"
                        style="@style/Widget.Material3.Button.OutlinedButton"
                        android:layout_width="0dp"
                        android:layout_height="36dp"
                        android:layout_weight="1"
                        android:layout_marginStart="4dp"
                        android:text="منتهي"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        app:cornerRadius="18dp" />

                </LinearLayout>

                <!-- Refresh Button -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnRefresh"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:text="🔄 تحديث"
                    android:textColor="@color/primary_blue"
                    android:textSize="14sp"
                    android:fontFamily="@font/cairo_regular"
                    app:cornerRadius="20dp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Content Area -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

            <!-- Loading -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <!-- Coupons List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCoupons"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingBottom="80dp" />

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/layoutEmpty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:padding="32dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎫"
                    android:textSize="64sp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:id="@+id/tvEmptyTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="لا توجد كوبونات"
                    android:textColor="@color/text_primary"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:fontFamily="@font/cairo_bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/tvEmptyMessage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="لم يتم العثور على أي كوبونات.\nستظهر الكوبونات الجديدة هنا."
                    android:textColor="@color/text_secondary"
                    android:textSize="14sp"
                    android:fontFamily="@font/cairo_regular"
                    android:gravity="center"
                    android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

    <!-- Floating Action Button (Admin Only) -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddCoupon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@color/white"
        app:backgroundTint="@color/accent_orange"
        android:contentDescription="إضافة كوبون جديد"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
