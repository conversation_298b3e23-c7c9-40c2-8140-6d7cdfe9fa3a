<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="16dp"
    app:cardElevation="4dp"
    android:background="@drawable/card_background_enhanced">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Image Placeholder -->
        <View
            android:layout_width="match_parent"
            android:layout_height="120dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shimmer_background" />

        <!-- Title Placeholder -->
        <View
            android:layout_width="match_parent"
            android:layout_height="16dp"
            android:layout_marginBottom="4dp"
            android:background="@drawable/shimmer_background" />

        <!-- Subtitle Placeholder -->
        <View
            android:layout_width="200dp"
            android:layout_height="12dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shimmer_background" />

        <!-- Price Placeholder -->
        <View
            android:layout_width="150dp"
            android:layout_height="14dp"
            android:layout_marginBottom="8dp"
            android:background="@drawable/shimmer_background" />

        <!-- Button Placeholder -->
        <View
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:background="@drawable/shimmer_background" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
