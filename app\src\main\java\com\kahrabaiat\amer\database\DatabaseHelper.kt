package com.kahrabaiat.amer.database

import android.content.ContentValues
import android.content.Context
import android.database.sqlite.SQLiteDatabase
import android.database.sqlite.SQLiteOpenHelper
import android.util.Log
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.Product

class DatabaseHelper private constructor(context: Context) : SQLiteOpenHelper(context, DATABASE_NAME, null, DATABASE_VERSION) {

    companion object {
        private const val DATABASE_NAME = "kahrabaiat_amer.db"
        private const val DATABASE_VERSION = 2

        // Products table
        private const val TABLE_PRODUCTS = "products"
        private const val COLUMN_PRODUCT_ID = "id"
        private const val COLUMN_PRODUCT_NAME = "name"
        private const val COLUMN_PRODUCT_DESCRIPTION = "description"
        private const val COLUMN_PRODUCT_PRICE = "price"
        private const val COLUMN_PRODUCT_STOCK = "stock"
        private const val COLUMN_PRODUCT_CATEGORY = "category"
        private const val COLUMN_PRODUCT_IMAGE = "image"
        private const val COLUMN_PRODUCT_AVAILABLE = "available"
        private const val COLUMN_PRODUCT_DISCOUNT = "discount"
        private const val COLUMN_PRODUCT_CREATED_AT = "created_at"

        // Orders table
        private const val TABLE_ORDERS = "orders"
        private const val COLUMN_ORDER_ID = "id"
        private const val COLUMN_ORDER_CUSTOMER_NAME = "customer_name"
        private const val COLUMN_ORDER_CUSTOMER_PHONE = "customer_phone"
        private const val COLUMN_ORDER_CUSTOMER_ADDRESS = "customer_address"
        private const val COLUMN_ORDER_ITEMS = "items"
        private const val COLUMN_ORDER_TOTAL = "total"
        private const val COLUMN_ORDER_STATUS = "status"
        private const val COLUMN_ORDER_DATE = "order_date"
        private const val COLUMN_ORDER_NOTES = "notes"

        @Volatile
        private var INSTANCE: DatabaseHelper? = null

        fun getInstance(context: Context): DatabaseHelper {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: DatabaseHelper(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val gson = Gson()

    override fun onCreate(db: SQLiteDatabase) {
        // Create products table
        val createProductsTable = """
            CREATE TABLE $TABLE_PRODUCTS (
                $COLUMN_PRODUCT_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                $COLUMN_PRODUCT_NAME TEXT NOT NULL,
                $COLUMN_PRODUCT_DESCRIPTION TEXT,
                $COLUMN_PRODUCT_PRICE REAL NOT NULL,
                $COLUMN_PRODUCT_STOCK INTEGER NOT NULL DEFAULT 0,
                $COLUMN_PRODUCT_CATEGORY TEXT,
                $COLUMN_PRODUCT_IMAGE TEXT,
                $COLUMN_PRODUCT_AVAILABLE INTEGER NOT NULL DEFAULT 1,
                $COLUMN_PRODUCT_DISCOUNT INTEGER NOT NULL DEFAULT 0,
                $COLUMN_PRODUCT_CREATED_AT INTEGER NOT NULL DEFAULT (strftime('%s','now'))
            )
        """.trimIndent()

        // Create orders table
        val createOrdersTable = """
            CREATE TABLE $TABLE_ORDERS (
                $COLUMN_ORDER_ID INTEGER PRIMARY KEY AUTOINCREMENT,
                $COLUMN_ORDER_CUSTOMER_NAME TEXT NOT NULL,
                $COLUMN_ORDER_CUSTOMER_PHONE TEXT,
                $COLUMN_ORDER_CUSTOMER_ADDRESS TEXT,
                $COLUMN_ORDER_ITEMS TEXT NOT NULL,
                $COLUMN_ORDER_TOTAL REAL NOT NULL,
                $COLUMN_ORDER_STATUS TEXT NOT NULL DEFAULT 'pending',
                $COLUMN_ORDER_DATE INTEGER NOT NULL DEFAULT (strftime('%s','now')),
                $COLUMN_ORDER_NOTES TEXT
            )
        """.trimIndent()

        try {
            db.execSQL(createProductsTable)
            db.execSQL(createOrdersTable)

            // Insert sample data
            insertSampleData(db)

            Log.i("DatabaseHelper", "Database created successfully")
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error creating database", e)
        }
    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        try {
            Log.i("DatabaseHelper", "Upgrading database from version $oldVersion to $newVersion")

            if (oldVersion < 2) {
                // إضافة عمودي available و discount للمنتجات
                try {
                    db.execSQL("ALTER TABLE $TABLE_PRODUCTS ADD COLUMN $COLUMN_PRODUCT_AVAILABLE INTEGER NOT NULL DEFAULT 1")
                    db.execSQL("ALTER TABLE $TABLE_PRODUCTS ADD COLUMN $COLUMN_PRODUCT_DISCOUNT INTEGER NOT NULL DEFAULT 0")
                    Log.i("DatabaseHelper", "Added available and discount columns to products table")
                } catch (e: Exception) {
                    Log.w("DatabaseHelper", "Columns might already exist, recreating table", e)
                    // إذا فشل في إضافة العمود، أعد إنشاء الجدول
                    db.execSQL("DROP TABLE IF EXISTS $TABLE_PRODUCTS")
                    db.execSQL("DROP TABLE IF EXISTS $TABLE_ORDERS")
                    onCreate(db)
                }
            }

            Log.i("DatabaseHelper", "Database upgraded successfully")
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error upgrading database", e)
            // في حالة فشل الترقية، أعد إنشاء قاعدة البيانات
            db.execSQL("DROP TABLE IF EXISTS $TABLE_PRODUCTS")
            db.execSQL("DROP TABLE IF EXISTS $TABLE_ORDERS")
            onCreate(db)
        }
    }

    private fun insertSampleData(db: SQLiteDatabase) {
        try {
            // Sample products
            val sampleProducts = listOf(
                Product(
                    id = 1,
                    name = "مفتاح كهرباء عادي",
                    description = "مفتاح كهرباء عادي أبيض اللون، جودة عالية",
                    price = 15.0,
                    stock = 50,
                    category = "مفاتيح",
                    imageUrl = ""
                ),
                Product(
                    id = 2,
                    name = "مقبس كهرباء مزدوج",
                    description = "مقبس كهرباء مزدوج مع حماية للأطفال",
                    price = 25.0,
                    stock = 30,
                    category = "مقابس",
                    imageUrl = ""
                ),
                Product(
                    id = 3,
                    name = "سلك كهرباء 2.5 مم",
                    description = "سلك كهرباء نحاس خالص 2.5 مم، طول 100 متر",
                    price = 120.0,
                    stock = 20,
                    category = "أسلاك",
                    imageUrl = ""
                ),
                Product(
                    id = 4,
                    name = "لمبة LED 12 واط",
                    description = "لمبة LED موفرة للطاقة، ضوء أبيض دافئ",
                    price = 35.0,
                    stock = 100,
                    category = "إضاءة",
                    imageUrl = ""
                ),
                Product(
                    id = 5,
                    name = "قاطع كهرباء 20 أمبير",
                    description = "قاطع كهرباء أوتوماتيكي 20 أمبير للحماية",
                    price = 45.0,
                    stock = 25,
                    category = "قواطع",
                    imageUrl = ""
                )
            )

            for (product in sampleProducts) {
                val productToInsert = product.copy(id = 0) // Let database auto-generate ID
                insertProduct(productToInsert, db)
            }

            Log.i("DatabaseHelper", "Sample data inserted successfully")
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error inserting sample data", e)
        }
    }

    // Products operations
    fun insertProduct(product: Product, db: SQLiteDatabase? = null): Long {
        val database = db ?: writableDatabase
        return try {
            val values = ContentValues().apply {
                put(COLUMN_PRODUCT_NAME, product.name)
                put(COLUMN_PRODUCT_DESCRIPTION, product.description)
                put(COLUMN_PRODUCT_PRICE, product.price)
                put(COLUMN_PRODUCT_STOCK, product.stock)
                put(COLUMN_PRODUCT_CATEGORY, product.category)
                put(COLUMN_PRODUCT_IMAGE, product.imageUrl)
                put(COLUMN_PRODUCT_AVAILABLE, if (product.available) 1 else 0)
                put(COLUMN_PRODUCT_DISCOUNT, product.discount)
                put(COLUMN_PRODUCT_CREATED_AT, System.currentTimeMillis())
            }

            val result = database.insert(TABLE_PRODUCTS, null, values)
            Log.i("DatabaseHelper", "Product inserted with ID: $result")
            result
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error inserting product", e)
            -1
        } finally {
            if (db == null) database.close()
        }
    }

    fun getAllProducts(): List<Product> {
        val products = mutableListOf<Product>()
        val database = readableDatabase

        return try {
            val cursor = database.query(
                TABLE_PRODUCTS,
                null,
                null,
                null,
                null,
                null,
                "$COLUMN_PRODUCT_CREATED_AT DESC"
            )

            cursor.use {
                while (it.moveToNext()) {
                    val product = Product(
                        id = it.getInt(it.getColumnIndexOrThrow(COLUMN_PRODUCT_ID)),
                        name = it.getString(it.getColumnIndexOrThrow(COLUMN_PRODUCT_NAME)),
                        description = it.getString(it.getColumnIndexOrThrow(COLUMN_PRODUCT_DESCRIPTION)) ?: "",
                        price = it.getDouble(it.getColumnIndexOrThrow(COLUMN_PRODUCT_PRICE)),
                        stock = it.getInt(it.getColumnIndexOrThrow(COLUMN_PRODUCT_STOCK)),
                        category = it.getString(it.getColumnIndexOrThrow(COLUMN_PRODUCT_CATEGORY)) ?: "",
                        imageUrl = it.getString(it.getColumnIndexOrThrow(COLUMN_PRODUCT_IMAGE)) ?: "",
                        available = it.getInt(it.getColumnIndexOrThrow(COLUMN_PRODUCT_AVAILABLE)) == 1,
                        discount = it.getInt(it.getColumnIndexOrThrow(COLUMN_PRODUCT_DISCOUNT)),
                        createdAt = it.getLong(it.getColumnIndexOrThrow(COLUMN_PRODUCT_CREATED_AT))
                    )
                    products.add(product)
                }
            }

            Log.i("DatabaseHelper", "Retrieved ${products.size} products")
            products
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error getting products", e)
            emptyList()
        } finally {
            database.close()
        }
    }

    fun updateProduct(product: Product): Boolean {
        val database = writableDatabase
        return try {
            val values = ContentValues().apply {
                put(COLUMN_PRODUCT_NAME, product.name)
                put(COLUMN_PRODUCT_DESCRIPTION, product.description)
                put(COLUMN_PRODUCT_PRICE, product.price)
                put(COLUMN_PRODUCT_STOCK, product.stock)
                put(COLUMN_PRODUCT_CATEGORY, product.category)
                put(COLUMN_PRODUCT_IMAGE, product.imageUrl)
                put(COLUMN_PRODUCT_AVAILABLE, if (product.available) 1 else 0)
                put(COLUMN_PRODUCT_DISCOUNT, product.discount)
            }

            val result = database.update(
                TABLE_PRODUCTS,
                values,
                "$COLUMN_PRODUCT_ID = ?",
                arrayOf(product.id.toString())
            )

            Log.i("DatabaseHelper", "Product updated: $result rows affected")
            result > 0
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error updating product", e)
            false
        } finally {
            database.close()
        }
    }

    fun updateProductAvailability(productId: Int, available: Boolean): Boolean {
        val database = writableDatabase
        return try {
            val values = ContentValues().apply {
                put(COLUMN_PRODUCT_AVAILABLE, if (available) 1 else 0)
            }

            val result = database.update(
                TABLE_PRODUCTS,
                values,
                "$COLUMN_PRODUCT_ID = ?",
                arrayOf(productId.toString())
            )

            Log.i("DatabaseHelper", "Product availability updated: $result rows affected")
            result > 0
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error updating product availability", e)
            false
        } finally {
            database.close()
        }
    }

    fun deleteProduct(productId: Int): Boolean {
        val database = writableDatabase
        return try {
            val result = database.delete(
                TABLE_PRODUCTS,
                "$COLUMN_PRODUCT_ID = ?",
                arrayOf(productId.toString())
            )

            Log.i("DatabaseHelper", "Product deleted: $result rows affected")
            result > 0
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error deleting product", e)
            false
        } finally {
            database.close()
        }
    }

    // Orders operations
    fun insertOrder(order: Order): Long {
        val database = writableDatabase
        return try {
            val itemsJson = gson.toJson(order.items)

            val values = ContentValues().apply {
                put(COLUMN_ORDER_CUSTOMER_NAME, order.customerName)
                put(COLUMN_ORDER_CUSTOMER_PHONE, order.customerPhone)
                put(COLUMN_ORDER_CUSTOMER_ADDRESS, order.customerAddress)
                put(COLUMN_ORDER_ITEMS, itemsJson)
                put(COLUMN_ORDER_TOTAL, order.total)
                put(COLUMN_ORDER_STATUS, order.status)
                put(COLUMN_ORDER_DATE, order.orderDate)
                put(COLUMN_ORDER_NOTES, order.notes)
            }

            val result = database.insert(TABLE_ORDERS, null, values)
            Log.i("DatabaseHelper", "Order inserted with ID: $result")
            result
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error inserting order", e)
            -1
        } finally {
            database.close()
        }
    }

    fun getAllOrders(): List<Order> {
        val orders = mutableListOf<Order>()
        val database = readableDatabase

        return try {
            val cursor = database.query(
                TABLE_ORDERS,
                null,
                null,
                null,
                null,
                null,
                "$COLUMN_ORDER_DATE DESC"
            )

            cursor.use {
                while (it.moveToNext()) {
                    val itemsJson = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_ITEMS))
                    val itemsType = object : TypeToken<List<Order.OrderItem>>() {}.type
                    val items = gson.fromJson<List<Order.OrderItem>>(itemsJson, itemsType) ?: emptyList()

                    val order = Order(
                        id = it.getInt(it.getColumnIndexOrThrow(COLUMN_ORDER_ID)),
                        customerName = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_CUSTOMER_NAME)),
                        customerPhone = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_CUSTOMER_PHONE)) ?: "",
                        customerAddress = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_CUSTOMER_ADDRESS)) ?: "",
                        items = items,
                        total = it.getDouble(it.getColumnIndexOrThrow(COLUMN_ORDER_TOTAL)),
                        status = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_STATUS)),
                        orderDate = it.getLong(it.getColumnIndexOrThrow(COLUMN_ORDER_DATE)),
                        notes = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_NOTES)) ?: ""
                    )
                    orders.add(order)
                }
            }

            Log.i("DatabaseHelper", "Retrieved ${orders.size} orders")
            orders
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error getting orders", e)
            emptyList()
        } finally {
            database.close()
        }
    }

    fun updateOrderStatus(orderId: Int, status: String): Boolean {
        val database = writableDatabase
        return try {
            val values = ContentValues().apply {
                put(COLUMN_ORDER_STATUS, status)
            }

            val result = database.update(
                TABLE_ORDERS,
                values,
                "$COLUMN_ORDER_ID = ?",
                arrayOf(orderId.toString())
            )

            Log.i("DatabaseHelper", "Order status updated: $result rows affected")
            result > 0
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error updating order status", e)
            false
        } finally {
            database.close()
        }
    }

    fun updateOrderDetails(order: Order): Boolean {
        val database = writableDatabase
        return try {
            // Update order basic info only (items are stored as JSON in the order)
            val orderValues = ContentValues().apply {
                put(COLUMN_ORDER_CUSTOMER_NAME, order.customerName)
                put(COLUMN_ORDER_CUSTOMER_PHONE, order.customerPhone)
                put(COLUMN_ORDER_CUSTOMER_ADDRESS, order.customerAddress)
                put(COLUMN_ORDER_TOTAL, order.total)
                put(COLUMN_ORDER_NOTES, order.notes)
                // Update items as JSON string
                val itemsJson = com.google.gson.Gson().toJson(order.items)
                put(COLUMN_ORDER_ITEMS, itemsJson)
            }

            val result = database.update(
                TABLE_ORDERS,
                orderValues,
                "$COLUMN_ORDER_ID = ?",
                arrayOf(order.id.toString())
            )

            Log.i("DatabaseHelper", "Order updated: $result rows affected")
            result > 0

        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error updating order details", e)
            false
        } finally {
            database.close()
        }
    }

    fun deleteOrder(orderId: Int): Boolean {
        val database = writableDatabase
        return try {
            val result = database.delete(
                TABLE_ORDERS,
                "$COLUMN_ORDER_ID = ?",
                arrayOf(orderId.toString())
            )

            Log.i("DatabaseHelper", "Order deleted: $result rows affected")
            result > 0
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error deleting order", e)
            false
        } finally {
            database.close()
        }
    }

    fun getOrdersByStatus(status: String): List<Order> {
        val orders = mutableListOf<Order>()
        val database = readableDatabase

        return try {
            val cursor = database.query(
                TABLE_ORDERS,
                null,
                "$COLUMN_ORDER_STATUS = ?",
                arrayOf(status),
                null,
                null,
                "$COLUMN_ORDER_DATE DESC"
            )

            cursor.use {
                while (it.moveToNext()) {
                    val itemsJson = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_ITEMS))
                    val itemsType = object : TypeToken<List<Order.OrderItem>>() {}.type
                    val items = gson.fromJson<List<Order.OrderItem>>(itemsJson, itemsType) ?: emptyList()

                    val order = Order(
                        id = it.getInt(it.getColumnIndexOrThrow(COLUMN_ORDER_ID)),
                        customerName = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_CUSTOMER_NAME)),
                        customerPhone = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_CUSTOMER_PHONE)) ?: "",
                        customerAddress = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_CUSTOMER_ADDRESS)) ?: "",
                        items = items,
                        total = it.getDouble(it.getColumnIndexOrThrow(COLUMN_ORDER_TOTAL)),
                        status = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_STATUS)),
                        orderDate = it.getLong(it.getColumnIndexOrThrow(COLUMN_ORDER_DATE)),
                        notes = it.getString(it.getColumnIndexOrThrow(COLUMN_ORDER_NOTES)) ?: ""
                    )
                    orders.add(order)
                }
            }

            Log.i("DatabaseHelper", "Retrieved ${orders.size} orders with status: $status")
            orders
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error getting orders by status", e)
            emptyList()
        } finally {
            database.close()
        }
    }

    // Statistics
    fun getProductCount(): Int {
        val database = readableDatabase
        return try {
            val cursor = database.rawQuery("SELECT COUNT(*) FROM $TABLE_PRODUCTS", null)
            cursor.use {
                if (it.moveToFirst()) {
                    it.getInt(0)
                } else {
                    0
                }
            }
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error getting product count", e)
            0
        } finally {
            database.close()
        }
    }

    fun getOrderCount(): Int {
        val database = readableDatabase
        return try {
            val cursor = database.rawQuery("SELECT COUNT(*) FROM $TABLE_ORDERS", null)
            cursor.use {
                if (it.moveToFirst()) {
                    it.getInt(0)
                } else {
                    0
                }
            }
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error getting order count", e)
            0
        } finally {
            database.close()
        }
    }

    fun getTotalSales(): Double {
        val database = readableDatabase
        return try {
            val cursor = database.rawQuery("SELECT SUM($COLUMN_ORDER_TOTAL) FROM $TABLE_ORDERS WHERE $COLUMN_ORDER_STATUS != 'cancelled'", null)
            cursor.use {
                if (it.moveToFirst()) {
                    it.getDouble(0)
                } else {
                    0.0
                }
            }
        } catch (e: Exception) {
            Log.e("DatabaseHelper", "Error getting total sales", e)
            0.0
        } finally {
            database.close()
        }
    }
}
