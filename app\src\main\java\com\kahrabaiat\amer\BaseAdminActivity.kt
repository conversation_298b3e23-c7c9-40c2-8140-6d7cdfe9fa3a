package com.kahrabaiat.amer 
 
import android.content.Intent 
import android.os.Bundle 
import android.view.MenuItem 
import androidx.appcompat.app.AppCompatActivity 
import com.kahrabaiat.amer.utils.AuthManager 
 
abstract class BaseAdminActivity : AppCompatActivity() { 
    protected lateinit var authManager: AuthManager 
 
    override fun onCreate(savedInstanceState: Bundle?) { 
        super.onCreate(savedInstanceState) 
        authManager = AuthManager.getInstance(this) 
    } 
 
    override fun onOptionsItemSelected(item: MenuItem): Boolean { 
        return when (item.itemId) { 
            R.id.action_go_to_main -> { 
                startActivity(Intent(this, MainActivity::class.java)) 
                true 
            } 
            else -> super.onOptionsItemSelected(item) 
        } 
    } 
} 
