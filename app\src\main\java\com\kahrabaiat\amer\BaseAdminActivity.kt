package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.kahrabaiat.amer.utils.AuthManager

abstract class BaseAdminActivity : AppCompatActivity() {
    
    protected lateinit var authManager: AuthManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        authManager = AuthManager.getInstance(this)
        
        // التحقق من صلاحيات الوصول
        if (!authManager.hasAdminPermission()) {
            redirectToLogin()
            return
        }
        
        // تمديد الجلسة عند النشاط
        authManager.extendSession()
    }
    
    override fun onResume() {
        super.onResume()
        
        // التحقق من صحة الجلسة عند العودة للنشاط
        if (!authManager.hasAdminPermission()) {
            redirectToLogin()
            return
        }
        
        // تحذير إذا كانت الجلسة ستنتهي قريباً
        if (authManager.isSessionExpiringSoon()) {
            showSessionExpiryWarning()
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.admin_menu, menu)
        return true
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_logout -> {
                showLogoutConfirmation()
                true
            }
            R.id.action_extend_session -> {
                extendSession()
                true
            }
            R.id.action_user_info -> {
                showUserInfo()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
    
    private fun redirectToLogin() {
        val intent = Intent(this, AdminLoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
    
    private fun showLogoutConfirmation() {
        AlertDialog.Builder(this)
            .setTitle("تسجيل الخروج")
            .setMessage("هل أنت متأكد من تسجيل الخروج؟")
            .setPositiveButton("نعم") { _, _ ->
                logout()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }
    
    private fun logout() {
        authManager.logout()
        redirectToLogin()
    }
    
    private fun extendSession() {
        authManager.extendSession()
        android.widget.Toast.makeText(this, "تم تمديد الجلسة لـ 24 ساعة إضافية", android.widget.Toast.LENGTH_SHORT).show()
    }
    
    private fun showUserInfo() {
        val userType = authManager.getCurrentUserType()
        val userName = authManager.getCurrentUserName()
        val expiryTime = authManager.getSessionExpiryTime()
        val expiryDate = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
            .format(java.util.Date(expiryTime))
        
        val message = """
            المستخدم: $userName
            نوع الحساب: ${getUserTypeDisplayName(userType)}
            انتهاء الجلسة: $expiryDate
        """.trimIndent()
        
        AlertDialog.Builder(this)
            .setTitle("معلومات المستخدم")
            .setMessage(message)
            .setPositiveButton("موافق", null)
            .setNeutralButton("تمديد الجلسة") { _, _ ->
                extendSession()
            }
            .show()
    }
    
    private fun showSessionExpiryWarning() {
        val expiryTime = authManager.getSessionExpiryTime()
        val currentTime = System.currentTimeMillis()
        val remainingMinutes = (expiryTime - currentTime) / (60 * 1000)
        
        AlertDialog.Builder(this)
            .setTitle("تحذير انتهاء الجلسة")
            .setMessage("ستنتهي جلستك خلال $remainingMinutes دقيقة. هل تريد تمديد الجلسة؟")
            .setPositiveButton("تمديد") { _, _ ->
                extendSession()
            }
            .setNegativeButton("تسجيل خروج") { _, _ ->
                logout()
            }
            .setNeutralButton("تذكيرني لاحقاً", null)
            .show()
    }
    
    private fun getUserTypeDisplayName(userType: AuthManager.UserType): String {
        return when (userType) {
            AuthManager.UserType.ADMIN -> "مدير النظام"
            AuthManager.UserType.OWNER -> "المالك"
            AuthManager.UserType.GUEST -> "زائر"
        }
    }
    
    /**
     * التحقق من صلاحيات المالك فقط
     */
    protected fun requireOwnerPermission(): Boolean {
        if (!authManager.hasOwnerPermission()) {
            AlertDialog.Builder(this)
                .setTitle("صلاحيات غير كافية")
                .setMessage("هذه العملية تتطلب صلاحيات المالك فقط")
                .setPositiveButton("موافق", null)
                .show()
            return false
        }
        return true
    }
    
    /**
     * الحصول على نوع المستخدم الحالي
     */
    protected fun getCurrentUserType(): AuthManager.UserType {
        return authManager.getCurrentUserType()
    }
    
    /**
     * التحقق من صلاحيات الإدارة
     */
    protected fun hasAdminPermission(): Boolean {
        return authManager.hasAdminPermission()
    }
}
