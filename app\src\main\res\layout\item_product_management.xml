<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Product Image -->
        <androidx.cardview.widget.CardView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp">

            <ImageView
                android:id="@+id/ivProductImage"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/background_secondary"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_image_placeholder"
                tools:src="@drawable/ic_image_placeholder" />

        </androidx.cardview.widget.CardView>

        <!-- Product Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Product Name -->
            <TextView
                android:id="@+id/tvProductName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:maxLines="2"
                android:ellipsize="end"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="ثلاجة سامسونج 18 قدم" />

            <!-- Category -->
            <TextView
                android:id="@+id/tvCategory"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:background="@drawable/bg_category_chip"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:textColor="@color/primary"
                android:textSize="12sp"
                tools:text="الأجهزة المنزلية" />

            <!-- Price and Stock Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tvPrice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/primary"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="1,500 د.ع" />

                <TextView
                    android:id="@+id/tvStock"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="المخزون: 5" />

            </LinearLayout>

            <!-- Status and Discount Row -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Availability Status -->
                <TextView
                    android:id="@+id/tvStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@drawable/bg_status_available"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    tools:text="متاح" />

                <!-- Discount Badge -->
                <TextView
                    android:id="@+id/tvDiscount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:background="@drawable/bg_discount_badge"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:textColor="@color/white"
                    android:textSize="11sp"
                    android:visibility="gone"
                    tools:text="خصم 10%"
                    tools:visibility="visible" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:orientation="vertical">

            <!-- Edit Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnEdit"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginBottom="4dp"
                android:contentDescription="تعديل"
                app:icon="@drawable/ic_edit"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="20dp"
                app:iconTint="@color/primary" />

            <!-- Delete Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginBottom="4dp"
                android:contentDescription="حذف"
                app:icon="@drawable/ic_delete"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="20dp"
                app:iconTint="@color/error" />

            <!-- Toggle Availability Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnToggleAvailability"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:contentDescription="تغيير الحالة"
                app:icon="@drawable/ic_visibility"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="20dp"
                app:iconTint="@color/text_secondary" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
