<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/gradient_background_main"
    android:layoutDirection="rtl"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Enhanced Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_primary_enhanced"
            android:orientation="vertical"
            android:padding="24dp"
            android:elevation="12dp">

            <!-- Top Bar -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <!-- App Icon -->
                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_marginEnd="12dp"
                    android:src="@drawable/ic_shopping_cart"
                    android:tint="@color/white" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/app_name"
                    android:textColor="@color/white"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:letterSpacing="-0.02" />

                <!-- Notifications Icon -->
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp">

                    <ImageView
                        android:id="@+id/ivNotifications"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/cart_button_background"
                        android:padding="8dp"
                        android:src="@drawable/ic_notifications"
                        android:tint="@color/primary_blue" />

                    <TextView
                        android:id="@+id/tvNotificationCount"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="top|end"
                        android:layout_marginTop="-4dp"
                        android:layout_marginEnd="-4dp"
                        android:background="@drawable/cart_badge_background"
                        android:gravity="center"
                        android:text="3"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                </FrameLayout>

                <!-- Cart Icon -->
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="12dp">

                    <ImageView
                        android:id="@+id/ivCart"
                        android:layout_width="40dp"
                        android:layout_height="40dp"
                        android:background="@drawable/cart_button_background"
                        android:padding="8dp"
                        android:src="@drawable/ic_shopping_cart"
                        android:tint="@color/primary_blue" />

                    <TextView
                        android:id="@+id/tvCartCount"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_gravity="top|end"
                        android:layout_marginTop="-4dp"
                        android:layout_marginEnd="-4dp"
                        android:background="@drawable/cart_badge_background"
                        android:gravity="center"
                        android:text="2"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        android:textStyle="bold" />

                </FrameLayout>

                <ImageView
                    android:id="@+id/ivAdmin"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:background="@drawable/admin_button_background"
                    android:padding="8dp"
                    android:src="@drawable/ic_admin"
                    android:tint="@color/primary_blue" />

            </LinearLayout>

            <!-- Welcome Message -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/welcome_message"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:alpha="0.9" />

            <!-- Search Bar -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                app:boxBackgroundColor="@color/white"
                app:boxCornerRadiusBottomEnd="12dp"
                app:boxCornerRadiusBottomStart="12dp"
                app:boxCornerRadiusTopEnd="12dp"
                app:boxCornerRadiusTopStart="12dp"
                app:startIconDrawable="@drawable/ic_search"
                app:startIconTint="@color/text_secondary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etSearch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="@string/search_hint"
                    android:imeOptions="actionSearch"
                    android:inputType="text"
                    android:maxLines="1"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Categories Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="@string/categories"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:textStyle="normal"
                android:gravity="end" />

            <!-- Categories Grid -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Hand Tools Category - Enhanced -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardHandTools"
                    android:layout_width="0dp"
                    android:layout_height="160dp"
                    android:layout_marginEnd="6dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="8dp"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="20dp"
                        android:background="@drawable/card_background_elevated">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginBottom="16dp"
                            android:src="@drawable/ic_hand_tools_modern"
                            android:elevation="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="العدد اليدوية"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:letterSpacing="-0.01"
                            android:fontFamily="@font/cairo_bold"
                            android:maxLines="2"
                            android:lineSpacingExtra="2dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Home Appliances Category - Enhanced -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardHomeAppliances"
                    android:layout_width="0dp"
                    android:layout_height="160dp"
                    android:layout_marginStart="3dp"
                    android:layout_marginEnd="3dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="8dp"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="20dp"
                        android:background="@drawable/card_background_elevated">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginBottom="16dp"
                            android:src="@drawable/ic_home_appliances_modern"
                            android:elevation="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الأجهزة المنزلية"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:letterSpacing="-0.01"
                            android:fontFamily="@font/cairo_bold"
                            android:maxLines="2"
                            android:lineSpacingExtra="2dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Electrical Appliances Category - Enhanced -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardElectricalAppliances"
                    android:layout_width="0dp"
                    android:layout_height="160dp"
                    android:layout_marginStart="6dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="24dp"
                    app:cardElevation="8dp"
                    android:foreground="?android:attr/selectableItemBackground">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="20dp"
                        android:background="@drawable/card_background_elevated">

                        <ImageView
                            android:layout_width="64dp"
                            android:layout_height="64dp"
                            android:layout_marginBottom="16dp"
                            android:src="@drawable/ic_electrical_modern"
                            android:elevation="2dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="الأجهزة الكهربائية"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:gravity="center"
                            android:letterSpacing="-0.01"
                            android:fontFamily="@font/cairo_bold"
                            android:maxLines="2"
                            android:lineSpacingExtra="2dp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </LinearLayout>

        <!-- Special Offers Section -->
        <androidx.cardview.widget.CardView
            android:id="@+id/layoutSpecialOffers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="4dp"
            android:visibility="visible"
            android:foreground="?android:attr/selectableItemBackground">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="16dp"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎫"
                    android:textSize="32sp"
                    android:layout_marginEnd="16dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="الكوبونات والعروض"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold"
                        android:gravity="start" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="اكتشف أحدث العروض والخصومات"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:fontFamily="@font/cairo_regular"
                        android:gravity="start"
                        android:layout_marginTop="4dp" />

                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="👈"
                    android:textSize="20sp"
                    android:textColor="@color/accent_orange" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Latest Products Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/latest_products"
                    android:textColor="@color/text_primary"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:gravity="end" />

                <TextView
                    android:id="@+id/tvViewAllProducts"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackground"
                    android:padding="8dp"
                    android:text="عرض الكل"
                    android:textColor="@color/primary_blue"
                    android:textSize="14sp"
                    android:drawableStart="@drawable/ic_arrow_back"
                    android:drawableTint="@color/primary_blue"
                    android:drawablePadding="4dp" />

            </LinearLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvLatestProducts"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                tools:listitem="@layout/item_product" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
