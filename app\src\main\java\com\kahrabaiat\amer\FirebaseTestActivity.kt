package com.kahrabaiat.amer

import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.kahrabaiat.amer.databinding.ActivityFirebaseTestBinding
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.FirebaseHelper
import kotlinx.coroutines.launch

class FirebaseTestActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityFirebaseTestBinding
    private lateinit var firebaseHelper: FirebaseHelper
    
    companion object {
        private const val TAG = "FirebaseTest"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFirebaseTestBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        firebaseHelper = FirebaseHelper()
        
        setupUI()
        setupClickListeners()
    }
    
    private fun setupUI() {
        binding.tvTitle.text = "اختبار اتصال Firebase"
        binding.tvStatus.text = "جاري الاختبار..."
        
        // بدء الاختبار تلقائياً
        testFirebaseConnection()
    }
    
    private fun setupClickListeners() {
        binding.btnTestConnection.setOnClickListener {
            testFirebaseConnection()
        }
        
        binding.btnAddTestProduct.setOnClickListener {
            addTestProduct()
        }
        
        binding.btnLoadProducts.setOnClickListener {
            loadProducts()
        }
        
        binding.btnBack.setOnClickListener {
            finish()
        }
    }
    
    private fun testFirebaseConnection() {
        binding.tvStatus.text = "🔄 جاري اختبار الاتصال..."
        
        lifecycleScope.launch {
            try {
                // اختبار قراءة البيانات
                val products = firebaseHelper.getAllProducts()
                
                val status = if (products.isEmpty()) {
                    "✅ الاتصال ناجح - قاعدة البيانات فارغة"
                } else {
                    "✅ الاتصال ناجح - تم العثور على ${products.size} منتج"
                }
                
                binding.tvStatus.text = status
                binding.tvDetails.text = "Firebase Firestore متصل بنجاح\nالمنطقة: asia-southeast1"
                
                Log.d(TAG, "Firebase connection successful")
                showToast("✅ Firebase متصل بنجاح!")
                
            } catch (e: Exception) {
                val errorMsg = "❌ خطأ في الاتصال: ${e.message}"
                binding.tvStatus.text = errorMsg
                binding.tvDetails.text = "تأكد من:\n1. إعداد Firestore\n2. قواعد الأمان\n3. الاتصال بالإنترنت"
                
                Log.e(TAG, "Firebase connection failed", e)
                showToast("❌ فشل الاتصال بـ Firebase")
            }
        }
    }
    
    private fun addTestProduct() {
        binding.tvStatus.text = "🔄 جاري إضافة منتج تجريبي..."
        
        lifecycleScope.launch {
            try {
                val testProduct = Product(
                    name = "منتج تجريبي - ${System.currentTimeMillis()}",
                    price = 100.0,
                    description = "هذا منتج تجريبي لاختبار Firebase",
                    category = "hand_tools",
                    stock = 5,
                    available = true,
                    discount = 10
                )
                
                val success = firebaseHelper.addProduct(testProduct)
                
                if (success) {
                    binding.tvStatus.text = "✅ تم إضافة المنتج التجريبي بنجاح"
                    binding.tvDetails.text = "المنتج: ${testProduct.name}\nالسعر: ${testProduct.price} دينار"
                    showToast("✅ تم إضافة المنتج بنجاح!")
                } else {
                    binding.tvStatus.text = "❌ فشل في إضافة المنتج"
                    showToast("❌ فشل في إضافة المنتج")
                }
                
            } catch (e: Exception) {
                binding.tvStatus.text = "❌ خطأ في إضافة المنتج: ${e.message}"
                Log.e(TAG, "Error adding test product", e)
                showToast("❌ خطأ في إضافة المنتج")
            }
        }
    }
    
    private fun loadProducts() {
        binding.tvStatus.text = "🔄 جاري تحميل المنتجات..."
        
        lifecycleScope.launch {
            try {
                val products = firebaseHelper.getAllProducts()
                
                if (products.isNotEmpty()) {
                    val productsList = products.joinToString("\n") { 
                        "• ${it.name} - ${it.price} دينار" 
                    }
                    
                    binding.tvStatus.text = "✅ تم تحميل ${products.size} منتج"
                    binding.tvDetails.text = "المنتجات:\n$productsList"
                    showToast("✅ تم تحميل ${products.size} منتج")
                } else {
                    binding.tvStatus.text = "📦 لا توجد منتجات في قاعدة البيانات"
                    binding.tvDetails.text = "أضف منتجات جديدة لاختبار النظام"
                    showToast("📦 لا توجد منتجات")
                }
                
            } catch (e: Exception) {
                binding.tvStatus.text = "❌ خطأ في تحميل المنتجات: ${e.message}"
                Log.e(TAG, "Error loading products", e)
                showToast("❌ خطأ في تحميل المنتجات")
            }
        }
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
