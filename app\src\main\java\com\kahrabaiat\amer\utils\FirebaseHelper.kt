package com.kahrabaiat.amer.utils

import android.net.Uri
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.storage.FirebaseStorage
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.Product
import kotlinx.coroutines.tasks.await
import java.io.File
import java.util.*

class FirebaseHelper {
    
    private val firestore = FirebaseFirestore.getInstance()
    private val storage = FirebaseStorage.getInstance()
    
    companion object {
        private const val TAG = "FirebaseHelper"
        private const val PRODUCTS_COLLECTION = "products"
        private const val ORDERS_COLLECTION = "orders"
    }

    // Products operations
    suspend fun getAllProducts(): List<Product> {
        return try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id.toIntOrNull() ?: 0)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting products", e)
            emptyList()
        }
    }

    suspend fun getLatestProducts(limit: Int): List<Product> {
        return try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .limit(limit.toLong())
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id.toIntOrNull() ?: 0)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting latest products", e)
            emptyList()
        }
    }

    suspend fun getDiscountedProducts(): List<Product> {
        return try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereGreaterThan("discount", 0)
                .orderBy("discount", Query.Direction.DESCENDING)
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id.toIntOrNull() ?: 0)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting discounted products", e)
            emptyList()
        }
    }

    suspend fun searchProducts(query: String): List<Product> {
        return try {
            // البحث في الاسم
            val nameResults = firestore.collection(PRODUCTS_COLLECTION)
                .orderBy("name")
                .startAt(query)
                .endAt(query + "\uf8ff")
                .get()
                .await()
            
            nameResults.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id.toIntOrNull() ?: 0)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error searching products", e)
            emptyList()
        }
    }

    suspend fun getProductsByCategory(category: String): List<Product> {
        return try {
            val snapshot = firestore.collection(PRODUCTS_COLLECTION)
                .whereEqualTo("category", category)
                .orderBy("createdAt", Query.Direction.DESCENDING)
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(Product::class.java)?.copy(id = doc.id.toIntOrNull() ?: 0)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting products by category", e)
            emptyList()
        }
    }

    suspend fun addProduct(product: Product): Boolean {
        return try {
            val productData = hashMapOf(
                "name" to product.name,
                "price" to product.price,
                "description" to product.description,
                "imageUrl" to product.imageUrl,
                "category" to product.category,
                "stock" to product.stock,
                "available" to product.available,
                "discount" to product.discount,
                "createdAt" to System.currentTimeMillis()
            )
            
            firestore.collection(PRODUCTS_COLLECTION)
                .add(productData)
                .await()
            
            Log.i(TAG, "Product added successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding product", e)
            false
        }
    }

    suspend fun updateProduct(product: Product): Boolean {
        return try {
            val productData = hashMapOf(
                "name" to product.name,
                "price" to product.price,
                "description" to product.description,
                "imageUrl" to product.imageUrl,
                "category" to product.category,
                "stock" to product.stock,
                "available" to product.available,
                "discount" to product.discount
            )
            
            firestore.collection(PRODUCTS_COLLECTION)
                .document(product.id.toString())
                .update(productData as Map<String, Any>)
                .await()
            
            Log.i(TAG, "Product updated successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating product", e)
            false
        }
    }

    suspend fun deleteProduct(productId: Int): Boolean {
        return try {
            firestore.collection(PRODUCTS_COLLECTION)
                .document(productId.toString())
                .delete()
                .await()
            
            Log.i(TAG, "Product deleted successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting product", e)
            false
        }
    }

    // Orders operations
    suspend fun getAllOrders(): List<Order> {
        return try {
            val snapshot = firestore.collection(ORDERS_COLLECTION)
                .orderBy("orderDate", Query.Direction.DESCENDING)
                .get()
                .await()
            
            snapshot.documents.mapNotNull { doc ->
                doc.toObject(Order::class.java)?.copy(id = doc.id.toIntOrNull() ?: 0)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting orders", e)
            emptyList()
        }
    }

    suspend fun addOrder(order: Order): Boolean {
        return try {
            val orderData = hashMapOf(
                "customerName" to order.customerName,
                "customerPhone" to order.customerPhone,
                "customerAddress" to order.customerAddress,
                "items" to order.items.map { item ->
                    hashMapOf(
                        "productId" to item.productId,
                        "productName" to item.productName,
                        "price" to item.price,
                        "quantity" to item.quantity,
                        "total" to item.total
                    )
                },
                "total" to order.total,
                "status" to order.status,
                "orderDate" to System.currentTimeMillis(),
                "notes" to order.notes
            )
            
            firestore.collection(ORDERS_COLLECTION)
                .add(orderData)
                .await()
            
            Log.i(TAG, "Order added successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error adding order", e)
            false
        }
    }

    suspend fun updateOrderStatus(orderId: Int, status: String): Boolean {
        return try {
            firestore.collection(ORDERS_COLLECTION)
                .document(orderId.toString())
                .update("status", status)
                .await()
            
            Log.i(TAG, "Order status updated successfully")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating order status", e)
            false
        }
    }

    // Image upload functionality
    suspend fun uploadProductImage(imageFile: File): String? {
        return try {
            val fileName = "product_${System.currentTimeMillis()}.jpg"
            val imageRef = storage.reference.child("products/$fileName")

            val uploadTask = imageRef.putFile(Uri.fromFile(imageFile)).await()
            val downloadUrl = uploadTask.storage.downloadUrl.await()

            Log.i(TAG, "Image uploaded successfully: $downloadUrl")
            downloadUrl.toString()
        } catch (e: Exception) {
            Log.e(TAG, "Error uploading image", e)
            null
        }
    }

    suspend fun deleteProductImage(imageUrl: String): Boolean {
        return try {
            if (imageUrl.isNotEmpty() && imageUrl.contains("firebase")) {
                val imageRef = storage.getReferenceFromUrl(imageUrl)
                imageRef.delete().await()
                Log.i(TAG, "Image deleted successfully")
                true
            } else {
                true // No image to delete
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting image", e)
            false
        }
    }

    // Initialize with sample data if empty
    suspend fun initializeSampleData(): Boolean {
        return try {
            val productsSnapshot = firestore.collection(PRODUCTS_COLLECTION).get().await()

            if (productsSnapshot.isEmpty) {
                Log.i(TAG, "Adding sample products...")
                addSampleProducts()
            }

            true
        } catch (e: Exception) {
            Log.e(TAG, "Error initializing sample data", e)
            false
        }
    }

    private suspend fun addSampleProducts() {
        val sampleProducts = listOf(
            Product(
                name = "مثقاب كهربائي بوش",
                price = 150.0,
                description = "مثقاب كهربائي قوي من بوش بقوة 650 واط",
                category = "hand_tools",
                stock = 10,
                available = true,
                discount = 15
            ),
            Product(
                name = "ثلاجة سامسونج 18 قدم",
                price = 1500.0,
                description = "ثلاجة سامسونج بسعة 18 قدم مكعب مع تقنية التبريد المتقدمة",
                category = "home_appliances",
                stock = 5,
                available = true,
                discount = 10
            ),
            Product(
                name = "مكيف هواء LG 1.5 طن",
                price = 800.0,
                description = "مكيف هواء LG بقوة 1.5 طن مع تقنية الانفرتر",
                category = "electrical_appliances",
                stock = 8,
                available = true,
                discount = 0
            ),
            Product(
                name = "مفك براغي كهربائي",
                price = 75.0,
                description = "مفك براغي كهربائي متعدد الاستخدامات",
                category = "hand_tools",
                stock = 15,
                available = true,
                discount = 5
            )
        )

        sampleProducts.forEach { product ->
            addProduct(product)
        }
    }
}
