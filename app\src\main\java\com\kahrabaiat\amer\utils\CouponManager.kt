package com.kahrabaiat.amer.utils

import android.content.Context
import android.util.Log
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.kahrabaiat.amer.models.Coupon
import com.kahrabaiat.amer.models.CouponApplication
import com.kahrabaiat.amer.models.CouponStatus
import com.kahrabaiat.amer.models.CouponType
import com.kahrabaiat.amer.models.Product
import kotlinx.coroutines.tasks.await

/**
 * مدير الكوبونات والخصومات
 */
class CouponManager private constructor(private val context: Context) {
    
    private val firestore = FirebaseFirestore.getInstance()
    private val sharedPrefs = context.getSharedPreferences("coupon_prefs", Context.MODE_PRIVATE)
    
    companion object {
        private const val TAG = "CouponManager"
        private const val COUPONS_COLLECTION = "coupons"
        private const val COUPON_USAGE_COLLECTION = "coupon_usage"
        
        @Volatile
        private var INSTANCE: CouponManager? = null
        
        fun getInstance(context: Context): CouponManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: CouponManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    /**
     * إنشاء كوبون جديد
     */
    suspend fun createCoupon(coupon: Coupon): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val couponData = hashMapOf(
                    "code" to coupon.code,
                    "title" to coupon.title,
                    "description" to coupon.description,
                    "type" to coupon.type.name,
                    "value" to coupon.value,
                    "minimumOrderAmount" to coupon.minimumOrderAmount,
                    "maximumDiscountAmount" to coupon.maximumDiscountAmount,
                    "usageLimit" to coupon.usageLimit,
                    "usedCount" to coupon.usedCount,
                    "userUsageLimit" to coupon.userUsageLimit,
                    "applicableCategories" to coupon.applicableCategories,
                    "applicableProducts" to coupon.applicableProducts,
                    "isActive" to coupon.isActive,
                    "isFirstTimeOnly" to coupon.isFirstTimeOnly,
                    "createdAt" to coupon.createdAt,
                    "validFrom" to coupon.validFrom,
                    "validUntil" to coupon.validUntil,
                    "createdBy" to coupon.createdBy,
                    "imageUrl" to coupon.imageUrl,
                    "termsAndConditions" to coupon.termsAndConditions,
                    "metadata" to coupon.metadata
                )

                firestore.collection(COUPONS_COLLECTION)
                    .document(coupon.id)
                    .set(couponData)
                    .await()
            }

            Log.i(TAG, "Coupon created: ${coupon.code}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error creating coupon", e)
            false
        }
    }

    /**
     * الحصول على جميع الكوبونات
     */
    suspend fun getAllCoupons(): List<Coupon> {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(COUPONS_COLLECTION)
                    .orderBy("createdAt", Query.Direction.DESCENDING)
                    .get()
                    .await()

                snapshot.documents.mapNotNull { doc ->
                    try {
                        Coupon(
                            id = doc.id,
                            code = doc.getString("code") ?: "",
                            title = doc.getString("title") ?: "",
                            description = doc.getString("description") ?: "",
                            type = CouponType.valueOf(doc.getString("type") ?: "PERCENTAGE"),
                            value = doc.getDouble("value") ?: 0.0,
                            minimumOrderAmount = doc.getDouble("minimumOrderAmount") ?: 0.0,
                            maximumDiscountAmount = doc.getDouble("maximumDiscountAmount") ?: 0.0,
                            usageLimit = doc.getLong("usageLimit")?.toInt() ?: 0,
                            usedCount = doc.getLong("usedCount")?.toInt() ?: 0,
                            userUsageLimit = doc.getLong("userUsageLimit")?.toInt() ?: 1,
                            applicableCategories = doc.get("applicableCategories") as? List<String> ?: emptyList(),
                            applicableProducts = doc.get("applicableProducts") as? List<String> ?: emptyList(),
                            isActive = doc.getBoolean("isActive") ?: true,
                            isFirstTimeOnly = doc.getBoolean("isFirstTimeOnly") ?: false,
                            createdAt = doc.getLong("createdAt") ?: 0L,
                            validFrom = doc.getLong("validFrom") ?: 0L,
                            validUntil = doc.getLong("validUntil") ?: 0L,
                            createdBy = doc.getString("createdBy") ?: "",
                            imageUrl = doc.getString("imageUrl") ?: "",
                            termsAndConditions = doc.getString("termsAndConditions") ?: "",
                            metadata = doc.get("metadata") as? Map<String, String> ?: emptyMap()
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing coupon", e)
                        null
                    }
                }
            } else {
                // إرجاع كوبونات تجريبية
                getSampleCoupons()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting coupons", e)
            emptyList()
        }
    }

    /**
     * الحصول على الكوبونات النشطة فقط
     */
    suspend fun getActiveCoupons(): List<Coupon> {
        return getAllCoupons().filter { it.isValid() }
    }

    /**
     * البحث عن كوبون بالكود
     */
    suspend fun getCouponByCode(code: String): Coupon? {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(COUPONS_COLLECTION)
                    .whereEqualTo("code", code.uppercase())
                    .limit(1)
                    .get()
                    .await()

                if (snapshot.documents.isNotEmpty()) {
                    val doc = snapshot.documents.first()
                    Coupon(
                        id = doc.id,
                        code = doc.getString("code") ?: "",
                        title = doc.getString("title") ?: "",
                        description = doc.getString("description") ?: "",
                        type = CouponType.valueOf(doc.getString("type") ?: "PERCENTAGE"),
                        value = doc.getDouble("value") ?: 0.0,
                        minimumOrderAmount = doc.getDouble("minimumOrderAmount") ?: 0.0,
                        maximumDiscountAmount = doc.getDouble("maximumDiscountAmount") ?: 0.0,
                        usageLimit = doc.getLong("usageLimit")?.toInt() ?: 0,
                        usedCount = doc.getLong("usedCount")?.toInt() ?: 0,
                        userUsageLimit = doc.getLong("userUsageLimit")?.toInt() ?: 1,
                        applicableCategories = doc.get("applicableCategories") as? List<String> ?: emptyList(),
                        applicableProducts = doc.get("applicableProducts") as? List<String> ?: emptyList(),
                        isActive = doc.getBoolean("isActive") ?: true,
                        isFirstTimeOnly = doc.getBoolean("isFirstTimeOnly") ?: false,
                        createdAt = doc.getLong("createdAt") ?: 0L,
                        validFrom = doc.getLong("validFrom") ?: 0L,
                        validUntil = doc.getLong("validUntil") ?: 0L,
                        createdBy = doc.getString("createdBy") ?: "",
                        imageUrl = doc.getString("imageUrl") ?: "",
                        termsAndConditions = doc.getString("termsAndConditions") ?: "",
                        metadata = doc.get("metadata") as? Map<String, String> ?: emptyMap()
                    )
                } else {
                    null
                }
            } else {
                // البحث في الكوبونات التجريبية
                getSampleCoupons().find { it.code.equals(code, ignoreCase = true) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting coupon by code", e)
            null
        }
    }

    /**
     * تطبيق كوبون على الطلب
     */
    suspend fun applyCoupon(
        couponCode: String,
        orderAmount: Double,
        cartItems: List<Product>,
        customerPhone: String = ""
    ): CouponApplication {
        try {
            val coupon = getCouponByCode(couponCode)
                ?: return CouponApplication(
                    isValid = false,
                    errorMessage = "كود الكوبون غير صحيح"
                )

            // التحقق من صحة الكوبون
            if (!coupon.isValid()) {
                return CouponApplication(
                    isValid = false,
                    errorMessage = when (coupon.getStatus()) {
                        CouponStatus.EXPIRED -> "انتهت صلاحية هذا الكوبون"
                        CouponStatus.USED_UP -> "تم استنفاد هذا الكوبون"
                        CouponStatus.INACTIVE -> "هذا الكوبون غير نشط"
                        CouponStatus.NOT_STARTED -> "لم يبدأ هذا الكوبون بعد"
                        else -> "هذا الكوبون غير صالح"
                    }
                )
            }

            // التحقق من الحد الأدنى للطلب
            if (orderAmount < coupon.minimumOrderAmount) {
                return CouponApplication(
                    isValid = false,
                    errorMessage = "الحد الأدنى للطلب ${coupon.minimumOrderAmount.toInt()} دينار"
                )
            }

            // التحقق من استخدام العميل للكوبون
            if (customerPhone.isNotEmpty()) {
                val userUsageCount = getUserCouponUsage(coupon.id, customerPhone)
                if (userUsageCount >= coupon.userUsageLimit) {
                    return CouponApplication(
                        isValid = false,
                        errorMessage = "لقد استخدمت هذا الكوبون من قبل"
                    )
                }
            }

            // التحقق من تطبيق الكوبون على المنتجات
            val applicableItems = cartItems.filter { product ->
                coupon.isApplicableToProduct(product.id.toString(), product.category)
            }

            if (applicableItems.isEmpty() && coupon.applicableProducts.isNotEmpty()) {
                return CouponApplication(
                    isValid = false,
                    errorMessage = "هذا الكوبون غير قابل للتطبيق على المنتجات المحددة"
                )
            }

            // حساب الخصم
            val applicableAmount = if (coupon.applicableProducts.isNotEmpty() || coupon.applicableCategories.isNotEmpty()) {
                applicableItems.sumOf { it.price.toDouble() }
            } else {
                orderAmount
            }

            val discountAmount = coupon.calculateDiscount(applicableAmount)
            val freeShipping = coupon.type == CouponType.FREE_SHIPPING

            return CouponApplication(
                isValid = true,
                discountAmount = discountAmount,
                freeShipping = freeShipping,
                coupon = coupon
            )

        } catch (e: Exception) {
            Log.e(TAG, "Error applying coupon", e)
            return CouponApplication(
                isValid = false,
                errorMessage = "حدث خطأ في تطبيق الكوبون"
            )
        }
    }

    /**
     * تسجيل استخدام الكوبون
     */
    suspend fun recordCouponUsage(couponId: String, customerPhone: String, orderAmount: Double): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val usageData = hashMapOf(
                    "couponId" to couponId,
                    "customerPhone" to customerPhone,
                    "orderAmount" to orderAmount,
                    "usedAt" to System.currentTimeMillis()
                )

                firestore.collection(COUPON_USAGE_COLLECTION)
                    .add(usageData)
                    .await()

                // تحديث عداد الاستخدام
                firestore.collection(COUPONS_COLLECTION)
                    .document(couponId)
                    .update("usedCount", com.google.firebase.firestore.FieldValue.increment(1))
                    .await()
            }

            Log.i(TAG, "Coupon usage recorded: $couponId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error recording coupon usage", e)
            false
        }
    }

    /**
     * الحصول على عدد مرات استخدام العميل للكوبون
     */
    private suspend fun getUserCouponUsage(couponId: String, customerPhone: String): Int {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                val snapshot = firestore.collection(COUPON_USAGE_COLLECTION)
                    .whereEqualTo("couponId", couponId)
                    .whereEqualTo("customerPhone", customerPhone)
                    .get()
                    .await()

                snapshot.documents.size
            } else {
                0 // للاختبار
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting user coupon usage", e)
            0
        }
    }

    /**
     * حذف كوبون
     */
    suspend fun deleteCoupon(couponId: String): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(COUPONS_COLLECTION)
                    .document(couponId)
                    .delete()
                    .await()
            }

            Log.i(TAG, "Coupon deleted: $couponId")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting coupon", e)
            false
        }
    }

    /**
     * تحديث حالة الكوبون
     */
    suspend fun updateCouponStatus(couponId: String, isActive: Boolean): Boolean {
        return try {
            if (AppConfig.USE_REAL_FIREBASE) {
                firestore.collection(COUPONS_COLLECTION)
                    .document(couponId)
                    .update("isActive", isActive)
                    .await()
            }

            Log.i(TAG, "Coupon status updated: $couponId -> $isActive")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Error updating coupon status", e)
            false
        }
    }

    /**
     * إنشاء كوبون ترحيبي تلقائي للعميل الجديد
     */
    suspend fun createWelcomeCouponForNewCustomer(customerPhone: String): Coupon? {
        return try {
            val welcomeCoupon = Coupon.createWelcomeCoupon(customerPhone)
            val created = createCoupon(welcomeCoupon)
            
            if (created) {
                Log.i(TAG, "Welcome coupon created for: $customerPhone")
                welcomeCoupon
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating welcome coupon", e)
            null
        }
    }

    /**
     * الحصول على إحصائيات الكوبونات
     */
    suspend fun getCouponStats(): Map<String, Int> {
        return try {
            val coupons = getAllCoupons()
            mapOf(
                "total" to coupons.size,
                "active" to coupons.count { it.getStatus() == CouponStatus.ACTIVE },
                "expired" to coupons.count { it.getStatus() == CouponStatus.EXPIRED },
                "used_up" to coupons.count { it.getStatus() == CouponStatus.USED_UP },
                "inactive" to coupons.count { it.getStatus() == CouponStatus.INACTIVE }
            )
        } catch (e: Exception) {
            Log.e(TAG, "Error getting coupon stats", e)
            emptyMap()
        }
    }

    /**
     * كوبونات تجريبية للاختبار
     */
    private fun getSampleCoupons(): List<Coupon> {
        return listOf(
            Coupon(
                id = "welcome_sample",
                code = "WELCOME10",
                title = "مرحباً بك!",
                description = "خصم ترحيبي للعملاء الجدد",
                type = CouponType.PERCENTAGE,
                value = 10.0,
                minimumOrderAmount = 50.0,
                maximumDiscountAmount = 20.0,
                usageLimit = 100,
                usedCount = 15,
                isFirstTimeOnly = true,
                validUntil = System.currentTimeMillis() + (30 * 24 * 60 * 60 * 1000L)
            ),
            Coupon(
                id = "save20_sample",
                code = "SAVE20",
                title = "وفر 20 دينار",
                description = "خصم 20 دينار على الطلبات فوق 100 دينار",
                type = CouponType.FIXED_AMOUNT,
                value = 20.0,
                minimumOrderAmount = 100.0,
                usageLimit = 50,
                usedCount = 8,
                validUntil = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L)
            ),
            Coupon(
                id = "freeship_sample",
                code = "FREESHIP",
                title = "شحن مجاني",
                description = "احصل على شحن مجاني للطلبات فوق 75 دينار",
                type = CouponType.FREE_SHIPPING,
                value = 0.0,
                minimumOrderAmount = 75.0,
                usageLimit = 200,
                usedCount = 45,
                validUntil = System.currentTimeMillis() + (14 * 24 * 60 * 60 * 1000L)
            )
        )
    }
}
