<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".UserManagementActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="32dp"
                android:visibility="gone" />

            <!-- Filter Chips -->
            <com.google.android.material.chip.ChipGroup
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="16dp"
                app:chipSpacingHorizontal="8dp"
                app:singleSelection="true">

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipAllUsers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="جميع المستخدمين"
                    app:chipIcon="@drawable/ic_people" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipAdmins"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="المديرين"
                    app:chipIcon="@drawable/ic_admin" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipManagers"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="المدراء"
                    app:chipIcon="@drawable/ic_manager" />

                <com.google.android.material.chip.Chip
                    android:id="@+id/chipEmployees"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الموظفين"
                    app:chipIcon="@drawable/ic_employee" />

            </com.google.android.material.chip.ChipGroup>

            <!-- Users List -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerViewUsers"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:padding="16dp"
                tools:listitem="@layout/item_user" />

            <!-- Empty State -->
            <TextView
                android:id="@+id/tvEmptyState"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:text="لا يوجد مستخدمين"
                android:textColor="@color/text_secondary"
                android:textSize="16sp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Add User FAB -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddUser"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
