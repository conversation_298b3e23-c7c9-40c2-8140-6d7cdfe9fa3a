<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cardReview"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header: Customer Info and Date -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <TextView
                android:id="@+id/tvCustomerName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="اسم العميل"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:fontFamily="@font/cairo_bold"
                android:gravity="start" />

            <TextView
                android:id="@+id/tvReviewDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="01/01/2024"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular" />

        </LinearLayout>

        <!-- Customer Phone (Admin Only) -->
        <TextView
            android:id="@+id/tvCustomerPhone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="الهاتف: 07901234567"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:fontFamily="@font/cairo_regular"
            android:layout_marginBottom="8dp"
            android:visibility="gone" />

        <!-- Rating Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="12dp">

            <RatingBar
                android:id="@+id/ratingBar"
                style="?android:attr/ratingBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:numStars="5"
                android:rating="5"
                android:stepSize="0.5"
                android:isIndicator="true"
                android:progressTint="@color/accent_orange"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tvRatingValue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="5.0"
                android:textColor="@color/accent_orange"
                android:textSize="14sp"
                android:textStyle="bold"
                android:fontFamily="@font/cairo_bold"
                android:layout_marginEnd="8dp" />

            <TextView
                android:id="@+id/tvRatingText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ممتاز"
                android:textColor="@color/accent_orange"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular" />

        </LinearLayout>

        <!-- Badges -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <com.google.android.material.chip.Chip
                android:id="@+id/chipVerifiedPurchase"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="✓ شراء مُتحقق منه"
                android:textSize="10sp"
                android:textColor="@color/success_green"
                app:chipBackgroundColor="@color/success_light"
                app:chipStrokeColor="@color/success_green"
                app:chipStrokeWidth="1dp"
                android:layout_marginEnd="4dp"
                android:visibility="gone" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipRecent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="🆕 جديد"
                android:textSize="10sp"
                android:textColor="@color/primary_blue"
                app:chipBackgroundColor="@color/primary_light"
                app:chipStrokeColor="@color/primary_blue"
                app:chipStrokeWidth="1dp"
                android:layout_marginEnd="4dp"
                android:visibility="gone" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chipHelpful"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="👍 مفيد"
                android:textSize="10sp"
                android:textColor="@color/accent_orange"
                app:chipBackgroundColor="@color/accent_light"
                app:chipStrokeColor="@color/accent_orange"
                app:chipStrokeWidth="1dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Review Comment -->
        <TextView
            android:id="@+id/tvComment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="نص المراجعة يظهر هنا..."
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:fontFamily="@font/cairo_regular"
            android:lineSpacingExtra="4dp"
            android:layout_marginBottom="12dp"
            android:gravity="start" />

        <!-- Helpful Count -->
        <TextView
            android:id="@+id/tvHelpfulCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="5 أشخاص وجدوا هذه المراجعة مفيدة"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:fontFamily="@font/cairo_regular"
            android:layout_marginBottom="12dp"
            android:visibility="gone" />

        <!-- Report Count (Admin Only) -->
        <TextView
            android:id="@+id/tvReportCount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="تم الإبلاغ عنها 2 مرة"
            android:textColor="@color/error_red"
            android:textSize="12sp"
            android:fontFamily="@font/cairo_regular"
            android:layout_marginBottom="8dp"
            android:visibility="gone" />

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnHelpful"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="👍 مفيد"
                android:textColor="@color/primary_blue"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginEnd="8dp"
                app:cornerRadius="18dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnReport"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="⚠️ إبلاغ"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                app:cornerRadius="18dp" />

        </LinearLayout>

        <!-- Admin Actions (Admin Only) -->
        <LinearLayout
            android:id="@+id/layoutAdminActions"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="8dp"
            android:padding="8dp"
            android:background="@drawable/rounded_background"
            android:visibility="gone">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnApprove"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="✓ موافقة"
                android:textColor="@color/success_green"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                android:layout_marginEnd="8dp"
                app:cornerRadius="18dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="🗑️ حذف"
                android:textColor="@color/error_red"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                app:cornerRadius="18dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
