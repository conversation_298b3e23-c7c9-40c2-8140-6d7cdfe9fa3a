<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".AdvancedSearchActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Search Filters Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="معايير البحث"
                        android:textColor="@color/primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- Search Query -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="كلمة البحث"
                        app:startIconDrawable="@drawable/ic_search">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etSearchQuery"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:layoutDirection="rtl" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Category Filter -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="الفئة"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <Spinner
                        android:id="@+id/spinnerCategory"
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginBottom="16dp"
                        android:background="@drawable/spinner_background"
                        android:layoutDirection="rtl" />

                    <!-- Price Range -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="نطاق السعر (دينار عراقي)"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:orientation="horizontal">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:hint="الحد الأدنى">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etMinPrice"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="numberDecimal"
                                android:layoutDirection="rtl" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:hint="الحد الأعلى">

                            <com.google.android.material.textfield.TextInputEditText
                                android:id="@+id/etMaxPrice"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="numberDecimal"
                                android:layoutDirection="rtl" />

                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <!-- Additional Filters -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="مرشحات إضافية"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                    <CheckBox
                        android:id="@+id/cbAvailableOnly"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:layoutDirection="rtl"
                        android:text="المنتجات المتاحة فقط"
                        android:textColor="@color/text_primary" />

                    <CheckBox
                        android:id="@+id/cbDiscountOnly"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:layoutDirection="rtl"
                        android:text="المنتجات المخفضة فقط"
                        android:textColor="@color/text_primary" />

                    <!-- Action Buttons -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnSearch"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="8dp"
                            android:layout_weight="1"
                            android:text="بحث"
                            app:icon="@drawable/ic_search"
                            app:iconGravity="textStart" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnClearFilters"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="8dp"
                            android:layout_weight="1"
                            android:text="مسح الكل"
                            app:icon="@drawable/ic_clear"
                            app:iconGravity="textStart" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Results Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <!-- Results Header -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/primary_light"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="نتائج البحث"
                            android:textColor="@color/primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvResultsCount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            tools:text="النتائج: 5 من 20 منتج" />

                    </LinearLayout>

                    <!-- Progress Bar -->
                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="32dp"
                        android:visibility="gone" />

                    <!-- Results RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvSearchResults"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        android:padding="8dp"
                        tools:listitem="@layout/item_product" />

                    <!-- Empty State -->
                    <TextView
                        android:id="@+id/tvEmptyState"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_margin="32dp"
                        android:drawableTop="@drawable/ic_search"
                        android:drawablePadding="16dp"
                        android:gravity="center"
                        android:text="لم يتم العثور على منتجات مطابقة\nجرب تغيير معايير البحث"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:visibility="gone" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
