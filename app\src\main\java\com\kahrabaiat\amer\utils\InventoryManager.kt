package com.kahrabaiat.amer.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.kahrabaiat.amer.models.Product
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow

class InventoryManager private constructor(private val context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("inventory_settings", Context.MODE_PRIVATE)
    
    private val _lowStockProducts = MutableStateFlow<List<Product>>(emptyList())
    val lowStockProducts: StateFlow<List<Product>> = _lowStockProducts.asStateFlow()
    
    private val _outOfStockProducts = MutableStateFlow<List<Product>>(emptyList())
    val outOfStockProducts: StateFlow<List<Product>> = _outOfStockProducts.asStateFlow()
    
    companion object {
        private const val TAG = "InventoryManager"
        private const val KEY_LOW_STOCK_THRESHOLD = "low_stock_threshold"
        private const val KEY_CRITICAL_STOCK_THRESHOLD = "critical_stock_threshold"
        private const val KEY_NOTIFICATIONS_ENABLED = "notifications_enabled"
        private const val KEY_LAST_CHECK_TIME = "last_check_time"
        
        // الحدود الافتراضية
        private const val DEFAULT_LOW_STOCK_THRESHOLD = 10
        private const val DEFAULT_CRITICAL_STOCK_THRESHOLD = 3
        
        @Volatile
        private var INSTANCE: InventoryManager? = null
        
        fun getInstance(context: Context): InventoryManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: InventoryManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * فحص حالة المخزون لجميع المنتجات
     */
    suspend fun checkInventoryStatus(products: List<Product>) {
        try {
            val lowStockThreshold = getLowStockThreshold()
            val criticalStockThreshold = getCriticalStockThreshold()
            
            val lowStockItems = mutableListOf<Product>()
            val outOfStockItems = mutableListOf<Product>()
            
            products.forEach { product ->
                when {
                    product.stock <= 0 -> {
                        outOfStockItems.add(product)
                        Log.w(TAG, "نفد المخزون: ${product.name}")
                    }
                    product.stock <= criticalStockThreshold -> {
                        lowStockItems.add(product)
                        Log.w(TAG, "مخزون حرج: ${product.name} - الكمية: ${product.stock}")
                    }
                    product.stock <= lowStockThreshold -> {
                        lowStockItems.add(product)
                        Log.i(TAG, "مخزون منخفض: ${product.name} - الكمية: ${product.stock}")
                    }
                }
            }
            
            _lowStockProducts.value = lowStockItems
            _outOfStockProducts.value = outOfStockItems
            
            // حفظ وقت آخر فحص
            saveLastCheckTime()
            
            // إرسال إشعارات إذا كانت مفعلة
            if (areNotificationsEnabled()) {
                sendInventoryNotifications(lowStockItems, outOfStockItems)
            }
            
            Log.d(TAG, "تم فحص المخزون: ${lowStockItems.size} منتج مخزون منخفض، ${outOfStockItems.size} منتج نفد")
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في فحص المخزون", e)
        }
    }
    
    /**
     * الحصول على حد المخزون المنخفض
     */
    fun getLowStockThreshold(): Int {
        return sharedPreferences.getInt(KEY_LOW_STOCK_THRESHOLD, DEFAULT_LOW_STOCK_THRESHOLD)
    }
    
    /**
     * تعيين حد المخزون المنخفض
     */
    fun setLowStockThreshold(threshold: Int) {
        sharedPreferences.edit()
            .putInt(KEY_LOW_STOCK_THRESHOLD, threshold)
            .apply()
        Log.d(TAG, "تم تعيين حد المخزون المنخفض: $threshold")
    }
    
    /**
     * الحصول على حد المخزون الحرج
     */
    fun getCriticalStockThreshold(): Int {
        return sharedPreferences.getInt(KEY_CRITICAL_STOCK_THRESHOLD, DEFAULT_CRITICAL_STOCK_THRESHOLD)
    }
    
    /**
     * تعيين حد المخزون الحرج
     */
    fun setCriticalStockThreshold(threshold: Int) {
        sharedPreferences.edit()
            .putInt(KEY_CRITICAL_STOCK_THRESHOLD, threshold)
            .apply()
        Log.d(TAG, "تم تعيين حد المخزون الحرج: $threshold")
    }
    
    /**
     * التحقق من تفعيل الإشعارات
     */
    fun areNotificationsEnabled(): Boolean {
        return sharedPreferences.getBoolean(KEY_NOTIFICATIONS_ENABLED, true)
    }
    
    /**
     * تفعيل/إلغاء تفعيل الإشعارات
     */
    fun setNotificationsEnabled(enabled: Boolean) {
        sharedPreferences.edit()
            .putBoolean(KEY_NOTIFICATIONS_ENABLED, enabled)
            .apply()
        Log.d(TAG, "تم ${if (enabled) "تفعيل" else "إلغاء تفعيل"} إشعارات المخزون")
    }
    
    /**
     * الحصول على وقت آخر فحص
     */
    fun getLastCheckTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_CHECK_TIME, 0)
    }
    
    /**
     * حفظ وقت آخر فحص
     */
    private fun saveLastCheckTime() {
        sharedPreferences.edit()
            .putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis())
            .apply()
    }
    
    /**
     * إرسال إشعارات المخزون
     */
    private fun sendInventoryNotifications(lowStockItems: List<Product>, outOfStockItems: List<Product>) {
        try {
            val notificationHelper = NotificationHelper(context)
            
            // إشعار نفاد المخزون (أولوية عالية)
            if (outOfStockItems.isNotEmpty()) {
                notificationHelper.sendOutOfStockNotification(outOfStockItems)
            }
            
            // إشعار المخزون المنخفض (أولوية متوسطة)
            if (lowStockItems.isNotEmpty()) {
                notificationHelper.sendLowStockNotification(lowStockItems)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في إرسال إشعارات المخزون", e)
        }
    }
    
    /**
     * الحصول على تقرير حالة المخزون
     */
    fun getInventoryReport(): InventoryReport {
        val lowStockCount = _lowStockProducts.value.size
        val outOfStockCount = _outOfStockProducts.value.size
        val lastCheckTime = getLastCheckTime()
        
        return InventoryReport(
            lowStockCount = lowStockCount,
            outOfStockCount = outOfStockCount,
            lastCheckTime = lastCheckTime,
            lowStockThreshold = getLowStockThreshold(),
            criticalStockThreshold = getCriticalStockThreshold(),
            notificationsEnabled = areNotificationsEnabled()
        )
    }
    
    /**
     * فحص منتج واحد
     */
    fun checkSingleProduct(product: Product): StockStatus {
        val lowThreshold = getLowStockThreshold()
        val criticalThreshold = getCriticalStockThreshold()
        
        return when {
            product.stock <= 0 -> StockStatus.OUT_OF_STOCK
            product.stock <= criticalThreshold -> StockStatus.CRITICAL
            product.stock <= lowThreshold -> StockStatus.LOW
            else -> StockStatus.NORMAL
        }
    }
    
    /**
     * الحصول على لون حالة المخزون
     */
    fun getStockStatusColor(status: StockStatus): Int {
        return when (status) {
            StockStatus.OUT_OF_STOCK -> android.graphics.Color.RED
            StockStatus.CRITICAL -> android.graphics.Color.parseColor("#FF5722")
            StockStatus.LOW -> android.graphics.Color.parseColor("#FF9800")
            StockStatus.NORMAL -> android.graphics.Color.parseColor("#4CAF50")
        }
    }
    
    /**
     * الحصول على نص حالة المخزون
     */
    fun getStockStatusText(status: StockStatus): String {
        return when (status) {
            StockStatus.OUT_OF_STOCK -> "نفد المخزون"
            StockStatus.CRITICAL -> "مخزون حرج"
            StockStatus.LOW -> "مخزون منخفض"
            StockStatus.NORMAL -> "متوفر"
        }
    }
    
    /**
     * تصدير تقرير المخزون
     */
    fun exportInventoryReport(): String {
        val report = getInventoryReport()
        val lowStockProducts = _lowStockProducts.value
        val outOfStockProducts = _outOfStockProducts.value
        
        return buildString {
            appendLine("=== تقرير حالة المخزون ===")
            appendLine("تاريخ التقرير: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date())}")
            appendLine("آخر فحص: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date(report.lastCheckTime))}")
            appendLine()
            appendLine("الإعدادات:")
            appendLine("- حد المخزون المنخفض: ${report.lowStockThreshold}")
            appendLine("- حد المخزون الحرج: ${report.criticalStockThreshold}")
            appendLine("- الإشعارات: ${if (report.notificationsEnabled) "مفعلة" else "معطلة"}")
            appendLine()
            appendLine("الملخص:")
            appendLine("- منتجات نفد مخزونها: ${report.outOfStockCount}")
            appendLine("- منتجات مخزون منخفض: ${report.lowStockCount}")
            appendLine()
            
            if (outOfStockProducts.isNotEmpty()) {
                appendLine("=== منتجات نفد مخزونها ===")
                outOfStockProducts.forEach { product ->
                    appendLine("- ${product.name} (الكمية: ${product.stock})")
                }
                appendLine()
            }
            
            if (lowStockProducts.isNotEmpty()) {
                appendLine("=== منتجات مخزون منخفض ===")
                lowStockProducts.forEach { product ->
                    appendLine("- ${product.name} (الكمية: ${product.stock})")
                }
            }
        }
    }
    
    // Data classes
    data class InventoryReport(
        val lowStockCount: Int,
        val outOfStockCount: Int,
        val lastCheckTime: Long,
        val lowStockThreshold: Int,
        val criticalStockThreshold: Int,
        val notificationsEnabled: Boolean
    )
    
    enum class StockStatus {
        NORMAL,      // مخزون طبيعي
        LOW,         // مخزون منخفض
        CRITICAL,    // مخزون حرج
        OUT_OF_STOCK // نفد المخزون
    }
}
