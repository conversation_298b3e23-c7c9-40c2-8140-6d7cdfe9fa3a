package com.kahrabaiat.amer.adapters

import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.databinding.ItemEditOrderBinding
import com.kahrabaiat.amer.models.Order
import java.text.NumberFormat
import java.util.*

class EditOrderItemsAdapter(
    private val onQuantityChanged: (Order.OrderItem, Int) -> Unit,
    private val onRemoveItem: (Order.OrderItem) -> Unit
) : ListAdapter<Order.OrderItem, EditOrderItemsAdapter.EditOrderItemViewHolder>(OrderItemDiffCallback()) {

    private var isEditingEnabled = true

    fun setEditingEnabled(enabled: <PERSON><PERSON><PERSON>) {
        isEditingEnabled = enabled
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): EditOrderItemViewHolder {
        val binding = ItemEditOrderBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return EditOrderItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: EditOrderItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class EditOrderItemViewHolder(
        private val binding: ItemEditOrderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        private var currentItem: Order.OrderItem? = null
        private var isUpdating = false

        fun bind(orderItem: Order.OrderItem) {
            currentItem = orderItem
            val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
            
            with(binding) {
                // Product information
                tvProductName.text = orderItem.productName
                tvUnitPrice.text = "${numberFormat.format(orderItem.price.toInt())} د.ع"
                
                // Quantity
                isUpdating = true
                etQuantity.setText(orderItem.quantity.toString())
                isUpdating = false
                
                // Total price
                updateTotalPrice(orderItem)
                
                // Enable/disable editing
                etQuantity.isEnabled = isEditingEnabled
                btnDecrease.isEnabled = isEditingEnabled
                btnIncrease.isEnabled = isEditingEnabled
                btnRemove.isEnabled = isEditingEnabled
                
                // Set up quantity change listeners
                setupQuantityListeners(orderItem)
                
                // Remove button
                btnRemove.setOnClickListener {
                    if (isEditingEnabled) {
                        onRemoveItem(orderItem)
                    }
                }
                
                // Visual feedback for editing state
                if (isEditingEnabled) {
                    root.alpha = 1.0f
                    tvEditDisabled.visibility = android.view.View.GONE
                } else {
                    root.alpha = 0.7f
                    tvEditDisabled.visibility = android.view.View.VISIBLE
                }
            }
        }

        private fun setupQuantityListeners(orderItem: Order.OrderItem) {
            with(binding) {
                // Decrease button
                btnDecrease.setOnClickListener {
                    if (isEditingEnabled) {
                        val currentQuantity = etQuantity.text.toString().toIntOrNull() ?: 1
                        if (currentQuantity > 1) {
                            val newQuantity = currentQuantity - 1
                            etQuantity.setText(newQuantity.toString())
                            onQuantityChanged(orderItem, newQuantity)
                        }
                    }
                }

                // Increase button
                btnIncrease.setOnClickListener {
                    if (isEditingEnabled) {
                        val currentQuantity = etQuantity.text.toString().toIntOrNull() ?: 1
                        val newQuantity = currentQuantity + 1
                        etQuantity.setText(newQuantity.toString())
                        onQuantityChanged(orderItem, newQuantity)
                    }
                }

                // Text change listener
                etQuantity.addTextChangedListener(object : TextWatcher {
                    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                    
                    override fun afterTextChanged(s: Editable?) {
                        if (!isUpdating && isEditingEnabled) {
                            val newQuantity = s.toString().toIntOrNull()
                            if (newQuantity != null && newQuantity > 0 && newQuantity != orderItem.quantity) {
                                onQuantityChanged(orderItem, newQuantity)
                            } else if (newQuantity != null && newQuantity <= 0) {
                                etQuantity.setText("1")
                            }
                        }
                    }
                })
            }
        }

        private fun updateTotalPrice(orderItem: Order.OrderItem) {
            val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
            val total = orderItem.price * orderItem.quantity
            binding.tvTotalPrice.text = "${numberFormat.format(total.toInt())} د.ع"
        }
    }

    private class OrderItemDiffCallback : DiffUtil.ItemCallback<Order.OrderItem>() {
        override fun areItemsTheSame(oldItem: Order.OrderItem, newItem: Order.OrderItem): Boolean {
            return oldItem.productId == newItem.productId
        }

        override fun areContentsTheSame(oldItem: Order.OrderItem, newItem: Order.OrderItem): Boolean {
            return oldItem == newItem
        }
    }
}
