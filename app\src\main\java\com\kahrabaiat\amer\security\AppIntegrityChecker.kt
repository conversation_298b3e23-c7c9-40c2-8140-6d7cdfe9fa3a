package com.kahrabaiat.amer.security

import android.content.Context
import android.content.pm.PackageManager
import android.content.pm.Signature
import android.os.Build
import android.provider.Settings
import android.util.Log
import java.security.MessageDigest

object AppIntegrityChecker {
    
    private const val TAG = "AppIntegrityChecker"
    
    // التوقيع المتوقع للتطبيق (يجب تحديثه بالتوقيع الفعلي)
    private const val EXPECTED_SIGNATURE = "YOUR_APP_SIGNATURE_HASH"
    
    /**
     * التحقق من سلامة التطبيق
     */
    fun checkAppIntegrity(context: Context): IntegrityResult {
        val results = mutableListOf<IntegrityCheck>()
        
        // فحص التوقيع
        results.add(checkAppSignature(context))
        
        // فحص التطبيقات الضارة
        results.add(checkForMaliciousApps(context))
        
        // فحص Root
        results.add(checkRootAccess())
        
        // فحص Developer Options
        results.add(checkDeveloperOptions(context))
        
        // فحص USB Debugging
        results.add(checkUsbDebugging(context))
        
        // فحص Emulator
        results.add(checkEmulator())
        
        val isSecure = results.all { it.passed }
        val securityLevel = calculateSecurityLevel(results)
        
        return IntegrityResult(
            isSecure = isSecure,
            securityLevel = securityLevel,
            checks = results
        )
    }
    
    /**
     * فحص توقيع التطبيق
     */
    private fun checkAppSignature(context: Context): IntegrityCheck {
        return try {
            val packageInfo = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNING_CERTIFICATES
                )
            } else {
                @Suppress("DEPRECATION")
                context.packageManager.getPackageInfo(
                    context.packageName,
                    PackageManager.GET_SIGNATURES
                )
            }
            
            val signatures = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                packageInfo.signingInfo?.apkContentsSigners
            } else {
                @Suppress("DEPRECATION")
                packageInfo.signatures
            }
            
            if (signatures != null && signatures.isNotEmpty()) {
                val signature = signatures[0]
                val signatureHash = getSignatureHash(signature)
                
                // في بيئة التطوير، نقبل أي توقيع
                // في الإنتاج، يجب التحقق من التوقيع المحدد
                val isValid = true // signatureHash == EXPECTED_SIGNATURE
                
                IntegrityCheck(
                    name = "App Signature",
                    passed = isValid,
                    message = if (isValid) "App signature is valid" else "App signature is invalid",
                    severity = if (isValid) Severity.INFO else Severity.CRITICAL
                )
            } else {
                IntegrityCheck(
                    name = "App Signature",
                    passed = false,
                    message = "No signature found",
                    severity = Severity.CRITICAL
                )
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error checking app signature", e)
            IntegrityCheck(
                name = "App Signature",
                passed = false,
                message = "Error checking signature: ${e.message}",
                severity = Severity.HIGH
            )
        }
    }
    
    /**
     * فحص التطبيقات الضارة المعروفة
     */
    private fun checkForMaliciousApps(context: Context): IntegrityCheck {
        val maliciousApps = listOf(
            "com.noshufou.android.su",
            "com.noshufou.android.su.elite",
            "eu.chainfire.supersu",
            "com.koushikdutta.superuser",
            "com.thirdparty.superuser",
            "com.yellowes.su",
            "com.topjohnwu.magisk",
            "com.kingroot.kinguser",
            "com.kingo.root",
            "com.smedialink.oneclickroot",
            "com.zhiqupk.root.global",
            "com.alephzain.framaroot"
        )
        
        val installedMaliciousApps = mutableListOf<String>()
        val packageManager = context.packageManager
        
        for (packageName in maliciousApps) {
            try {
                packageManager.getPackageInfo(packageName, 0)
                installedMaliciousApps.add(packageName)
            } catch (e: PackageManager.NameNotFoundException) {
                // التطبيق غير مثبت - هذا جيد
            }
        }
        
        val isClean = installedMaliciousApps.isEmpty()
        
        return IntegrityCheck(
            name = "Malicious Apps",
            passed = isClean,
            message = if (isClean) "No malicious apps detected" else "Detected malicious apps: ${installedMaliciousApps.joinToString()}",
            severity = if (isClean) Severity.INFO else Severity.HIGH
        )
    }
    
    /**
     * فحص صلاحيات Root
     */
    private fun checkRootAccess(): IntegrityCheck {
        val rootIndicators = listOf(
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su",
            "/su/bin/su"
        )
        
        val foundRootFiles = mutableListOf<String>()
        
        for (path in rootIndicators) {
            if (java.io.File(path).exists()) {
                foundRootFiles.add(path)
            }
        }
        
        // فحص إضافي للـ Root
        val hasRootAccess = try {
            Runtime.getRuntime().exec("su").waitFor() == 0
        } catch (e: Exception) {
            false
        }
        
        val isRooted = foundRootFiles.isNotEmpty() || hasRootAccess
        
        return IntegrityCheck(
            name = "Root Access",
            passed = !isRooted,
            message = if (!isRooted) "Device is not rooted" else "Device appears to be rooted",
            severity = if (!isRooted) Severity.INFO else Severity.MEDIUM
        )
    }
    
    /**
     * فحص خيارات المطور
     */
    private fun checkDeveloperOptions(context: Context): IntegrityCheck {
        val isDeveloperEnabled = try {
            Settings.Global.getInt(context.contentResolver, Settings.Global.DEVELOPMENT_SETTINGS_ENABLED, 0) == 1
        } catch (e: Exception) {
            false
        }
        
        return IntegrityCheck(
            name = "Developer Options",
            passed = !isDeveloperEnabled,
            message = if (!isDeveloperEnabled) "Developer options are disabled" else "Developer options are enabled",
            severity = if (!isDeveloperEnabled) Severity.INFO else Severity.LOW
        )
    }
    
    /**
     * فحص USB Debugging
     */
    private fun checkUsbDebugging(context: Context): IntegrityCheck {
        val isUsbDebuggingEnabled = try {
            Settings.Global.getInt(context.contentResolver, Settings.Global.ADB_ENABLED, 0) == 1
        } catch (e: Exception) {
            false
        }
        
        return IntegrityCheck(
            name = "USB Debugging",
            passed = !isUsbDebuggingEnabled,
            message = if (!isUsbDebuggingEnabled) "USB debugging is disabled" else "USB debugging is enabled",
            severity = if (!isUsbDebuggingEnabled) Severity.INFO else Severity.MEDIUM
        )
    }
    
    /**
     * فحص المحاكي
     */
    private fun checkEmulator(): IntegrityCheck {
        val emulatorIndicators = listOf(
            Build.FINGERPRINT.startsWith("generic"),
            Build.FINGERPRINT.startsWith("unknown"),
            Build.MODEL.contains("google_sdk"),
            Build.MODEL.contains("Emulator"),
            Build.MODEL.contains("Android SDK built for x86"),
            Build.MANUFACTURER.contains("Genymotion"),
            Build.BRAND.startsWith("generic") && Build.DEVICE.startsWith("generic"),
            "google_sdk" == Build.PRODUCT
        )
        
        val isEmulator = emulatorIndicators.any { it }
        
        return IntegrityCheck(
            name = "Emulator Detection",
            passed = !isEmulator,
            message = if (!isEmulator) "Running on real device" else "Running on emulator",
            severity = if (!isEmulator) Severity.INFO else Severity.LOW
        )
    }
    
    /**
     * حساب مستوى الأمان
     */
    private fun calculateSecurityLevel(checks: List<IntegrityCheck>): SecurityLevel {
        val criticalFailures = checks.count { !it.passed && it.severity == Severity.CRITICAL }
        val highFailures = checks.count { !it.passed && it.severity == Severity.HIGH }
        val mediumFailures = checks.count { !it.passed && it.severity == Severity.MEDIUM }
        
        return when {
            criticalFailures > 0 -> SecurityLevel.CRITICAL
            highFailures > 0 -> SecurityLevel.HIGH_RISK
            mediumFailures > 1 -> SecurityLevel.MEDIUM_RISK
            mediumFailures > 0 -> SecurityLevel.LOW_RISK
            else -> SecurityLevel.SECURE
        }
    }
    
    /**
     * الحصول على hash التوقيع
     */
    private fun getSignatureHash(signature: Signature): String {
        return try {
            val md = MessageDigest.getInstance("SHA-256")
            val hashBytes = md.digest(signature.toByteArray())
            hashBytes.joinToString("") { "%02x".format(it) }
        } catch (e: Exception) {
            ""
        }
    }
    
    // Data classes
    data class IntegrityResult(
        val isSecure: Boolean,
        val securityLevel: SecurityLevel,
        val checks: List<IntegrityCheck>
    )
    
    data class IntegrityCheck(
        val name: String,
        val passed: Boolean,
        val message: String,
        val severity: Severity
    )
    
    enum class SecurityLevel {
        SECURE,
        LOW_RISK,
        MEDIUM_RISK,
        HIGH_RISK,
        CRITICAL
    }
    
    enum class Severity {
        INFO,
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }
}
