package com.kahrabaiat.amer.adapters

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemCouponBinding
import com.kahrabaiat.amer.models.Coupon
import com.kahrabaiat.amer.models.CouponStatus

class CouponAdapter(
    private val onCouponClick: (Coupon) -> Unit = {},
    private val onCopyCode: (Coupon) -> Unit = {},
    private val onToggleStatus: (Coupon) -> Unit = {},
    private val onDeleteClick: (Coupon) -> Unit = {},
    private val showAdminActions: Boolean = false
) : ListAdapter<Coupon, CouponAdapter.CouponViewHolder>(CouponDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CouponViewHolder {
        val binding = ItemCouponBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CouponViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CouponViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CouponViewHolder(
        private val binding: ItemCouponBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(coupon: Coupon) {
            binding.apply {
                // المعلومات الأساسية
                tvCouponTitle.text = coupon.title
                tvCouponDescription.text = coupon.description
                tvCouponCode.text = coupon.code
                tvCouponValue.text = coupon.getFormattedValue()
                tvCouponValidUntil.text = "صالح حتى: ${coupon.getFormattedValidUntil()}"

                // أيقونة نوع الكوبون
                tvCouponIcon.text = coupon.getTypeIcon()

                // صورة الكوبون
                if (coupon.imageUrl.isNotEmpty()) {
                    Glide.with(itemView.context)
                        .load(coupon.imageUrl)
                        .placeholder(R.drawable.ic_visibility)
                        .error(R.drawable.ic_visibility)
                        .into(ivCouponImage)
                    ivCouponImage.visibility = View.VISIBLE
                } else {
                    ivCouponImage.visibility = View.GONE
                }

                // حالة الكوبون
                val status = coupon.getStatus()
                chipCouponStatus.text = coupon.getStatusText()
                try {
                    val statusColor = Color.parseColor(coupon.getStatusColor())
                    chipCouponStatus.setChipBackgroundColorResource(
                        when (status) {
                            CouponStatus.ACTIVE -> R.color.success_light
                            CouponStatus.EXPIRED -> R.color.error_background
                            CouponStatus.USED_UP -> R.color.warning_background
                            CouponStatus.INACTIVE -> R.color.border_light
                            CouponStatus.NOT_STARTED -> R.color.info_background
                        }
                    )
                    chipCouponStatus.setTextColor(statusColor)
                } catch (e: Exception) {
                    // استخدام الألوان الافتراضية
                }

                // شارات إضافية
                if (coupon.isNew()) {
                    chipNew.visibility = View.VISIBLE
                } else {
                    chipNew.visibility = View.GONE
                }

                if (coupon.isFirstTimeOnly) {
                    chipFirstTime.visibility = View.VISIBLE
                } else {
                    chipFirstTime.visibility = View.GONE
                }

                // معلومات الاستخدام
                if (coupon.usageLimit > 0) {
                    val remainingUses = coupon.usageLimit - coupon.usedCount
                    tvUsageInfo.text = "متبقي: $remainingUses من ${coupon.usageLimit}"
                    tvUsageInfo.visibility = View.VISIBLE
                    
                    // شريط التقدم
                    progressUsage.max = coupon.usageLimit
                    progressUsage.progress = coupon.usedCount
                    progressUsage.visibility = View.VISIBLE
                } else {
                    tvUsageInfo.text = "استخدام غير محدود"
                    tvUsageInfo.visibility = View.VISIBLE
                    progressUsage.visibility = View.GONE
                }

                // الحد الأدنى للطلب
                if (coupon.minimumOrderAmount > 0) {
                    tvMinimumOrder.text = "حد أدنى: ${coupon.minimumOrderAmount.toInt()} دينار"
                    tvMinimumOrder.visibility = View.VISIBLE
                } else {
                    tvMinimumOrder.visibility = View.GONE
                }

                // الأيام المتبقية
                val daysRemaining = coupon.getDaysRemaining()
                if (daysRemaining >= 0) {
                    tvDaysRemaining.text = when {
                        daysRemaining == 0 -> "ينتهي اليوم"
                        daysRemaining == 1 -> "ينتهي غداً"
                        daysRemaining <= 7 -> "ينتهي خلال $daysRemaining أيام"
                        else -> "ينتهي خلال $daysRemaining يوم"
                    }
                    tvDaysRemaining.visibility = View.VISIBLE
                    
                    // تغيير لون التحذير
                    if (daysRemaining <= 3) {
                        tvDaysRemaining.setTextColor(Color.parseColor("#F44336"))
                    } else if (daysRemaining <= 7) {
                        tvDaysRemaining.setTextColor(Color.parseColor("#FF9800"))
                    } else {
                        tvDaysRemaining.setTextColor(Color.parseColor("#4CAF50"))
                    }
                } else {
                    tvDaysRemaining.visibility = View.GONE
                }

                // الشروط والأحكام
                if (coupon.termsAndConditions.isNotEmpty()) {
                    tvTerms.text = coupon.termsAndConditions
                    tvTerms.visibility = View.VISIBLE
                } else {
                    tvTerms.visibility = View.GONE
                }

                // أزرار الإجراءات
                btnCopyCode.setOnClickListener {
                    onCopyCode(coupon)
                }

                // أزرار المدير
                if (showAdminActions) {
                    layoutAdminActions.visibility = View.VISIBLE
                    
                    btnToggleStatus.text = if (coupon.isActive) "إيقاف" else "تفعيل"
                    btnToggleStatus.setOnClickListener {
                        onToggleStatus(coupon)
                    }
                    
                    btnDelete.setOnClickListener {
                        onDeleteClick(coupon)
                    }
                } else {
                    layoutAdminActions.visibility = View.GONE
                }

                // تفعيل/تعطيل البطاقة حسب الحالة
                cardCoupon.alpha = if (status == CouponStatus.ACTIVE) 1.0f else 0.7f
                cardCoupon.isEnabled = status == CouponStatus.ACTIVE

                // النقر على البطاقة
                cardCoupon.setOnClickListener {
                    onCouponClick(coupon)
                }

                // انيميشن ظهور
                com.kahrabaiat.amer.ui.AnimationUtils.animateCardEntry(cardCoupon, adapterPosition * 100L)
            }
        }
    }

    class CouponDiffCallback : DiffUtil.ItemCallback<Coupon>() {
        override fun areItemsTheSame(oldItem: Coupon, newItem: Coupon): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Coupon, newItem: Coupon): Boolean {
            return oldItem == newItem
        }
    }
}

/**
 * محول مبسط للكوبونات في السلة
 */
class SimpleCouponAdapter(
    private val onCouponSelect: (Coupon) -> Unit = {}
) : ListAdapter<Coupon, SimpleCouponAdapter.SimpleCouponViewHolder>(CouponDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SimpleCouponViewHolder {
        val binding = ItemCouponBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SimpleCouponViewHolder(binding)
    }

    override fun onBindViewHolder(holder: SimpleCouponViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class SimpleCouponViewHolder(
        private val binding: ItemCouponBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(coupon: Coupon) {
            binding.apply {
                // إخفاء العناصر غير المطلوبة في العرض المبسط
                layoutAdminActions.visibility = View.GONE
                ivCouponImage.visibility = View.GONE
                tvTerms.visibility = View.GONE
                progressUsage.visibility = View.GONE
                chipFirstTime.visibility = View.GONE
                chipNew.visibility = View.GONE

                // عرض المعلومات الأساسية
                tvCouponTitle.text = coupon.title
                tvCouponDescription.text = coupon.getDisplayDescription()
                tvCouponCode.text = coupon.code
                tvCouponValue.text = coupon.getFormattedValue()
                tvCouponIcon.text = coupon.getTypeIcon()

                // حالة الكوبون
                chipCouponStatus.text = coupon.getStatusText()
                chipCouponStatus.visibility = View.VISIBLE

                // الحد الأدنى
                if (coupon.minimumOrderAmount > 0) {
                    tvMinimumOrder.text = "حد أدنى: ${coupon.minimumOrderAmount.toInt()} د"
                    tvMinimumOrder.visibility = View.VISIBLE
                } else {
                    tvMinimumOrder.visibility = View.GONE
                }

                // الأيام المتبقية
                val daysRemaining = coupon.getDaysRemaining()
                if (daysRemaining >= 0 && daysRemaining <= 7) {
                    tvDaysRemaining.text = "ينتهي خلال $daysRemaining أيام"
                    tvDaysRemaining.visibility = View.VISIBLE
                } else {
                    tvDaysRemaining.visibility = View.GONE
                }

                // إخفاء زر النسخ في العرض المبسط
                btnCopyCode.visibility = View.GONE

                // تفعيل/تعطيل حسب الحالة
                val isActive = coupon.getStatus() == CouponStatus.ACTIVE
                cardCoupon.alpha = if (isActive) 1.0f else 0.6f
                cardCoupon.isEnabled = isActive

                // النقر للاختيار
                cardCoupon.setOnClickListener {
                    if (isActive) {
                        onCouponSelect(coupon)
                    }
                }
            }
        }
    }

    class CouponDiffCallback : DiffUtil.ItemCallback<Coupon>() {
        override fun areItemsTheSame(oldItem: Coupon, newItem: Coupon): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Coupon, newItem: Coupon): Boolean {
            return oldItem == newItem
        }
    }
}
