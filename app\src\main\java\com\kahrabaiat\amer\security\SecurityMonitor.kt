package com.kahrabaiat.amer.security

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import java.text.SimpleDateFormat
import java.util.*

class SecurityMonitor private constructor(private val context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("security_monitor", Context.MODE_PRIVATE)
    
    companion object {
        private const val TAG = "SecurityMonitor"
        private const val KEY_LOGIN_ATTEMPTS = "login_attempts"
        private const val KEY_LAST_ATTEMPT_TIME = "last_attempt_time"
        private const val KEY_BLOCKED_UNTIL = "blocked_until"
        private const val KEY_SECURITY_EVENTS = "security_events"
        private const val KEY_SUSPICIOUS_ACTIVITY = "suspicious_activity"
        
        private const val MAX_LOGIN_ATTEMPTS = 5
        private const val BLOCK_DURATION_MS = 15 * 60 * 1000L // 15 دقيقة
        private const val ATTEMPT_RESET_TIME = 60 * 60 * 1000L // ساعة واحدة
        
        @Volatile
        private var INSTANCE: SecurityMonitor? = null
        
        fun getInstance(context: Context): SecurityMonitor {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SecurityMonitor(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * تسجيل محاولة دخول فاشلة
     */
    fun recordFailedLoginAttempt(username: String, ipAddress: String? = null) {
        val currentTime = System.currentTimeMillis()
        val attempts = getLoginAttempts()
        val lastAttemptTime = getLastAttemptTime()
        
        // إعادة تعيين العداد إذا مر وقت كافي
        val newAttempts = if (currentTime - lastAttemptTime > ATTEMPT_RESET_TIME) {
            1
        } else {
            attempts + 1
        }
        
        sharedPreferences.edit().apply {
            putInt(KEY_LOGIN_ATTEMPTS, newAttempts)
            putLong(KEY_LAST_ATTEMPT_TIME, currentTime)
            
            // حظر المستخدم إذا تجاوز الحد المسموح
            if (newAttempts >= MAX_LOGIN_ATTEMPTS) {
                putLong(KEY_BLOCKED_UNTIL, currentTime + BLOCK_DURATION_MS)
                logSecurityEvent(
                    SecurityEvent.ACCOUNT_BLOCKED,
                    "Account blocked after $newAttempts failed attempts",
                    mapOf(
                        "username" to username,
                        "attempts" to newAttempts.toString(),
                        "ip_address" to (ipAddress ?: "unknown")
                    )
                )
            } else {
                logSecurityEvent(
                    SecurityEvent.FAILED_LOGIN,
                    "Failed login attempt $newAttempts/$MAX_LOGIN_ATTEMPTS",
                    mapOf(
                        "username" to username,
                        "attempts" to newAttempts.toString(),
                        "ip_address" to (ipAddress ?: "unknown")
                    )
                )
            }
            
            apply()
        }
        
        Log.w(TAG, "Failed login attempt recorded: $newAttempts/$MAX_LOGIN_ATTEMPTS for user: $username")
    }
    
    /**
     * تسجيل دخول ناجح
     */
    fun recordSuccessfulLogin(username: String, userType: String) {
        // إعادة تعيين عداد المحاولات الفاشلة
        sharedPreferences.edit().apply {
            putInt(KEY_LOGIN_ATTEMPTS, 0)
            putLong(KEY_LAST_ATTEMPT_TIME, 0)
            putLong(KEY_BLOCKED_UNTIL, 0)
            apply()
        }
        
        logSecurityEvent(
            SecurityEvent.SUCCESSFUL_LOGIN,
            "Successful login",
            mapOf(
                "username" to username,
                "user_type" to userType
            )
        )
        
        Log.i(TAG, "Successful login recorded for user: $username")
    }
    
    /**
     * التحقق من حالة الحظر
     */
    fun isAccountBlocked(): Boolean {
        val blockedUntil = sharedPreferences.getLong(KEY_BLOCKED_UNTIL, 0)
        val currentTime = System.currentTimeMillis()
        
        return if (blockedUntil > currentTime) {
            true
        } else {
            // إلغاء الحظر إذا انتهت المدة
            if (blockedUntil > 0) {
                sharedPreferences.edit().apply {
                    putLong(KEY_BLOCKED_UNTIL, 0)
                    putInt(KEY_LOGIN_ATTEMPTS, 0)
                    apply()
                }
                logSecurityEvent(SecurityEvent.ACCOUNT_UNBLOCKED, "Account automatically unblocked")
            }
            false
        }
    }
    
    /**
     * الحصول على وقت انتهاء الحظر
     */
    fun getBlockedUntilTime(): Long {
        return sharedPreferences.getLong(KEY_BLOCKED_UNTIL, 0)
    }
    
    /**
     * الحصول على عدد المحاولات الفاشلة
     */
    fun getLoginAttempts(): Int {
        return sharedPreferences.getInt(KEY_LOGIN_ATTEMPTS, 0)
    }
    
    /**
     * الحصول على وقت آخر محاولة
     */
    private fun getLastAttemptTime(): Long {
        return sharedPreferences.getLong(KEY_LAST_ATTEMPT_TIME, 0)
    }
    
    /**
     * تسجيل حدث أمني
     */
    private fun logSecurityEvent(
        eventType: SecurityEvent,
        description: String,
        metadata: Map<String, String> = emptyMap()
    ) {
        val timestamp = System.currentTimeMillis()
        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        
        val event = SecurityEventLog(
            timestamp = timestamp,
            eventType = eventType,
            description = description,
            metadata = metadata
        )
        
        // حفظ الحدث في السجل
        saveSecurityEvent(event)
        
        Log.i(TAG, "Security event logged: ${eventType.name} - $description at ${dateFormat.format(Date(timestamp))}")
    }
    
    /**
     * حفظ حدث أمني في السجل
     */
    private fun saveSecurityEvent(event: SecurityEventLog) {
        try {
            val existingEvents = getSecurityEvents().toMutableList()
            existingEvents.add(event)
            
            // الاحتفاظ بآخر 100 حدث فقط
            if (existingEvents.size > 100) {
                existingEvents.removeAt(0)
            }
            
            val eventsJson = com.google.gson.Gson().toJson(existingEvents)
            sharedPreferences.edit().putString(KEY_SECURITY_EVENTS, eventsJson).apply()
        } catch (e: Exception) {
            Log.e(TAG, "Error saving security event", e)
        }
    }
    
    /**
     * الحصول على سجل الأحداث الأمنية
     */
    fun getSecurityEvents(): List<SecurityEventLog> {
        return try {
            val eventsJson = sharedPreferences.getString(KEY_SECURITY_EVENTS, "[]")
            val eventType = object : com.google.gson.reflect.TypeToken<List<SecurityEventLog>>() {}.type
            com.google.gson.Gson().fromJson(eventsJson, eventType) ?: emptyList()
        } catch (e: Exception) {
            Log.e(TAG, "Error loading security events", e)
            emptyList()
        }
    }
    
    /**
     * تسجيل نشاط مشبوه
     */
    fun recordSuspiciousActivity(activityType: String, description: String, metadata: Map<String, String> = emptyMap()) {
        logSecurityEvent(
            SecurityEvent.SUSPICIOUS_ACTIVITY,
            "$activityType: $description",
            metadata
        )
        
        // زيادة عداد النشاط المشبوه
        val suspiciousCount = sharedPreferences.getInt(KEY_SUSPICIOUS_ACTIVITY, 0) + 1
        sharedPreferences.edit().putInt(KEY_SUSPICIOUS_ACTIVITY, suspiciousCount).apply()
        
        Log.w(TAG, "Suspicious activity recorded: $activityType - $description")
    }
    
    /**
     * إلغاء الحظر يدوياً (للمدير فقط)
     */
    fun unblockAccount() {
        sharedPreferences.edit().apply {
            putLong(KEY_BLOCKED_UNTIL, 0)
            putInt(KEY_LOGIN_ATTEMPTS, 0)
            putLong(KEY_LAST_ATTEMPT_TIME, 0)
            apply()
        }
        
        logSecurityEvent(SecurityEvent.ACCOUNT_UNBLOCKED, "Account manually unblocked by admin")
        Log.i(TAG, "Account manually unblocked")
    }
    
    /**
     * مسح سجل الأحداث الأمنية
     */
    fun clearSecurityLog() {
        sharedPreferences.edit().apply {
            putString(KEY_SECURITY_EVENTS, "[]")
            putInt(KEY_SUSPICIOUS_ACTIVITY, 0)
            apply()
        }
        
        logSecurityEvent(SecurityEvent.LOG_CLEARED, "Security log cleared by admin")
        Log.i(TAG, "Security log cleared")
    }
    
    /**
     * الحصول على تقرير الأمان
     */
    fun getSecurityReport(): SecurityReport {
        val events = getSecurityEvents()
        val currentTime = System.currentTimeMillis()
        val last24Hours = currentTime - (24 * 60 * 60 * 1000L)
        
        val recentEvents = events.filter { it.timestamp > last24Hours }
        val failedLogins = recentEvents.count { it.eventType == SecurityEvent.FAILED_LOGIN }
        val suspiciousActivities = recentEvents.count { it.eventType == SecurityEvent.SUSPICIOUS_ACTIVITY }
        val totalSuspiciousCount = sharedPreferences.getInt(KEY_SUSPICIOUS_ACTIVITY, 0)
        
        return SecurityReport(
            totalEvents = events.size,
            recentEvents = recentEvents.size,
            failedLoginsLast24h = failedLogins,
            suspiciousActivitiesLast24h = suspiciousActivities,
            totalSuspiciousActivities = totalSuspiciousCount,
            isAccountBlocked = isAccountBlocked(),
            blockedUntil = getBlockedUntilTime(),
            currentLoginAttempts = getLoginAttempts()
        )
    }
    
    // Data classes
    data class SecurityEventLog(
        val timestamp: Long,
        val eventType: SecurityEvent,
        val description: String,
        val metadata: Map<String, String>
    )
    
    data class SecurityReport(
        val totalEvents: Int,
        val recentEvents: Int,
        val failedLoginsLast24h: Int,
        val suspiciousActivitiesLast24h: Int,
        val totalSuspiciousActivities: Int,
        val isAccountBlocked: Boolean,
        val blockedUntil: Long,
        val currentLoginAttempts: Int
    )
    
    enum class SecurityEvent {
        SUCCESSFUL_LOGIN,
        FAILED_LOGIN,
        ACCOUNT_BLOCKED,
        ACCOUNT_UNBLOCKED,
        SUSPICIOUS_ACTIVITY,
        LOG_CLEARED,
        PASSWORD_CHANGED,
        UNAUTHORIZED_ACCESS_ATTEMPT
    }
}
