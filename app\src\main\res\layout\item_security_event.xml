<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:gravity="center_vertical">

        <!-- Event Icon -->
        <ImageView
            android:id="@+id/ivEventIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:src="@drawable/ic_security"
            android:contentDescription="أيقونة الحدث" />

        <!-- Event Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Event Type and Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tvEventType"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="نوع الحدث"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <TextView
                    android:id="@+id/tvEventTime"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="الوقت"
                    android:textSize="12sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>

            <!-- Event Description -->
            <TextView
                android:id="@+id/tvEventDescription"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="وصف الحدث"
                android:textSize="13sp"
                android:textColor="@color/text_secondary" />

            <!-- Event Metadata (Optional) -->
            <TextView
                android:id="@+id/tvEventMetadata"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="بيانات إضافية"
                android:textSize="11sp"
                android:textColor="@color/text_hint"
                android:background="@drawable/info_background"
                android:padding="6dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
