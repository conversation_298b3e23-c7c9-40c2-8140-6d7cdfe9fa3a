package com.kahrabaiat.amer.utils

object AppConfig {
    // تبديل بين Firebase الحقيقي والبيانات التجريبية
    const val USE_REAL_FIREBASE = false // غير إلى false لاستخدام البيانات التجريبية

    // إعدادات Firebase
    const val FIREBASE_REGION = "asia-southeast1"

    // إعدادات التطبيق
    const val APP_VERSION = "1.0.0"
    const val DEBUG_MODE = true

    // إعدادات الصور
    const val MAX_IMAGE_SIZE_MB = 5
    const val IMAGE_QUALITY = 80

    // إعدادات الإشعارات
    const val ENABLE_NOTIFICATIONS = true
    const val NOTIFICATION_CHANNEL_ID = "kahrabaiat_amer_notifications"

    fun getFirebaseHelper(): FirebaseHelper {
        return FirebaseHelper()
    }
}
