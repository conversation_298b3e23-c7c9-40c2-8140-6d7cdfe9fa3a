package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemAdminOrderBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus

class AdminOrderAdapter(
    private val orders: MutableList<Order>,
    private val onOrderClick: (Order) -> Unit,
    private val onStatusChange: (Order, String) -> Unit
) : RecyclerView.Adapter<AdminOrderAdapter.AdminOrderViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AdminOrderViewHolder {
        val binding = ItemAdminOrderBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AdminOrderViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AdminOrderViewHolder, position: Int) {
        holder.bind(orders[position])
    }

    override fun getItemCount(): Int = orders.size

    inner class AdminOrderViewHolder(
        private val binding: ItemAdminOrderBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(order: Order) {
            binding.apply {
                tvOrderNumber.text = "طلب #${order.id}"
                tvCustomerName.text = "العميل: ${order.customerName}"
                tvOrderDate.text = order.getFormattedDate()
                tvOrderTotal.text = order.getFormattedTotal()

                // Set status
                val statusText = when (order.status) {
                    "pending" -> "جديد"
                    "confirmed" -> "مؤكد"
                    "processing" -> "قيد التحضير"
                    "shipped" -> "تم الشحن"
                    "delivered" -> "تم التسليم"
                    "cancelled" -> "ملغي"
                    else -> "غير محدد"
                }

                tvOrderStatus.text = statusText

                // Set status color
                val statusColor = when (order.status) {
                    "pending" -> R.color.warning_yellow
                    "confirmed" -> R.color.primary_blue
                    "processing" -> R.color.accent_orange
                    "shipped" -> R.color.primary_blue
                    "delivered" -> R.color.success_green
                    "cancelled" -> R.color.error_red
                    else -> R.color.text_secondary
                }

                tvOrderStatus.setBackgroundColor(
                    ContextCompat.getColor(itemView.context, statusColor)
                )

                root.setOnClickListener {
                    onOrderClick(order)
                }
            }
        }
    }

    private class OrderDiffCallback : DiffUtil.ItemCallback<Order>() {
        override fun areItemsTheSame(oldItem: Order, newItem: Order): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Order, newItem: Order): Boolean {
            return oldItem == newItem
        }
    }
}
