package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemOrderDetailBinding
import com.kahrabaiat.amer.models.Order
import java.text.NumberFormat
import java.util.*

class OrderItemsAdapter : ListAdapter<Order.OrderItem, OrderItemsAdapter.OrderItemViewHolder>(OrderItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OrderItemViewHolder {
        val binding = ItemOrderDetailBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OrderItemViewHolder(binding)
    }

    override fun onBindViewHolder(holder: OrderItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class OrderItemViewHolder(
        private val binding: ItemOrderDetailBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(orderItem: Order.OrderItem) {
            val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
            
            with(binding) {
                // Product information
                tvProductName.text = orderItem.productName
                tvQuantity.text = "الكمية: ${orderItem.quantity}"
                tvUnitPrice.text = "${numberFormat.format(orderItem.price.toInt())} د.ع"
                tvTotalPrice.text = "${numberFormat.format(orderItem.total.toInt())} د.ع"
                
                // Product image placeholder
                // يمكن تحسين هذا لاحقاً لعرض صورة المنتج الفعلية
                ivProductImage.setImageResource(R.drawable.ic_product_placeholder)
                
                // Calculate discount if any
                val originalTotal = orderItem.price * orderItem.quantity
                if (originalTotal > orderItem.total) {
                    val discount = originalTotal - orderItem.total
                    tvDiscount.text = "خصم: ${numberFormat.format(discount.toInt())} د.ع"
                    tvDiscount.visibility = android.view.View.VISIBLE
                } else {
                    tvDiscount.visibility = android.view.View.GONE
                }
            }
        }
    }

    private class OrderItemDiffCallback : DiffUtil.ItemCallback<Order.OrderItem>() {
        override fun areItemsTheSame(oldItem: Order.OrderItem, newItem: Order.OrderItem): Boolean {
            return oldItem.productId == newItem.productId
        }

        override fun areContentsTheSame(oldItem: Order.OrderItem, newItem: Order.OrderItem): Boolean {
            return oldItem == newItem
        }
    }
}
