# إعداد Firebase لتطبيق كهربائيات عامر

## الحالة الحالية
تم تعطيل Firebase مؤقتاً للسماح بتشغيل التطبيق بدون إعداد Firebase. التطبيق يعمل حالياً ببيانات تجريبية محلية.

## خطوات إعداد Firebase (اختيارية)

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. انقر على "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع: `kahrabaiat-amer`
4. اتبع الخطوات لإنشاء المشروع

### 2. إضافة تطبيق Android
1. في لوحة تحكم Firebase، انقر على أيقونة Android
2. أدخل package name: `com.kahrabaiat.amer`
3. أدخل اسم التطبيق: `كهربائيات عامر`
4. <PERSON><PERSON><PERSON> ملف `google-services.json`
5. ضع الملف في مجلد `app/`

### 3. تفعيل الخدمات المطلوبة

#### Firestore Database
1. اذهب إلى "Firestore Database" في القائمة الجانبية
2. انقر على "إنشاء قاعدة بيانات"
3. اختر "Start in test mode" للبداية
4. اختر موقع قاعدة البيانات (أقرب منطقة جغرافية)

#### Firebase Storage
1. اذهب إلى "Storage" في القائمة الجانبية
2. انقر على "البدء"
3. اختر "Start in test mode"

#### Firebase Cloud Messaging
1. اذهب إلى "Cloud Messaging" في القائمة الجانبية
2. الخدمة ستكون مفعلة تلقائياً

### 4. إعداد قواعد الأمان

#### Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Products - قراءة للجميع، كتابة للمدير فقط
    match /products/{productId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.token.admin == true;
    }
    
    // Orders - كتابة للجميع، قراءة للمدير فقط
    match /orders/{orderId} {
      allow create: if true;
      allow read, update, delete: if request.auth != null && request.auth.token.admin == true;
    }
  }
}
```

#### Storage Rules
```javascript
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read: if true;
      allow write: if request.auth != null;
    }
  }
}
```

### 5. تفعيل Firebase في التطبيق

بعد إضافة ملف `google-services.json`:

1. **في `app/build.gradle`:**
```gradle
plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
    id 'com.google.gms.google-services' // إلغاء التعليق
    id 'kotlin-parcelize'
}

dependencies {
    // Firebase - إلغاء التعليق
    implementation platform('com.google.firebase:firebase-bom:32.7.0')
    implementation 'com.google.firebase:firebase-firestore-ktx'
    implementation 'com.google.firebase:firebase-storage-ktx'
    implementation 'com.google.firebase:firebase-messaging-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    // ... باقي المكتبات
}
```

2. **في `FirebaseHelper.kt`:**
   - استبدال المحتوى بالإصدار الحقيقي الذي يستخدم Firebase

3. **في `NotificationHelper.kt`:**
   - إلغاء تعليق Firebase imports والدوال

4. **في `MyFirebaseMessagingService.kt`:**
   - إلغاء تعليق الكلاس والدوال

### 6. إضافة بيانات تجريبية

يمكنك إضافة منتجات تجريبية مباشرة في Firestore Console:

```json
// مجموعة products
{
  "name": "ثلاجة سامسونج 18 قدم",
  "price": 1500,
  "description": "ثلاجة سامسونج بسعة 18 قدم مكعب",
  "imageUrl": "",
  "category": "home_appliances",
  "stock": 5,
  "available": true,
  "discount": 10,
  "createdAt": "2025-01-15T10:00:00Z"
}
```

## التشغيل بدون Firebase

التطبيق يعمل حالياً ببيانات تجريبية محلية. جميع الوظائف تعمل عادي باستثناء:
- حفظ البيانات بشكل دائم
- الإشعارات عبر FCM
- مشاركة البيانات بين الأجهزة

## ملاحظات مهمة

- **كلمة مرور المدير:** `admin123456`
- **البيانات التجريبية:** تتضمن 4 منتجات في تصنيفات مختلفة
- **الفواتير:** يتم حفظها كملفات نصية بدلاً من PDF

---

**للدعم:** راجع [وثائق Firebase](https://firebase.google.com/docs/android/setup)
