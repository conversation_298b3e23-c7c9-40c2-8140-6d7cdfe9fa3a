<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:orientation="vertical"
    tools:context=".OrderDetailsActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_background"
        android:elevation="8dp"
        app:title="تفاصيل الطلب"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="16dp"
        android:visibility="gone" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Order Header Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- Order Number and Status -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tvOrderNumber"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:textColor="@color/primary_blue"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="طلب #2025-001234" />

                        <View
                            android:id="@+id/statusIndicator"
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginEnd="8dp"
                            android:background="@drawable/circle_shape"
                            android:backgroundTint="@color/success_green" />

                        <TextView
                            android:id="@+id/tvOrderStatus"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/success_green"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="مسلم" />

                    </LinearLayout>

                    <!-- Order Date -->
                    <TextView
                        android:id="@+id/tvOrderDate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="15/01/2025 14:30" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Customer Information Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="معلومات العميل"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Customer Name -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_person"
                            android:tint="@color/primary_blue" />

                        <TextView
                            android:id="@+id/tvCustomerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="أحمد محمد علي" />

                    </LinearLayout>

                    <!-- Customer Phone -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_phone"
                            android:tint="@color/primary_blue" />

                        <TextView
                            android:id="@+id/tvCustomerPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="07901234567" />

                    </LinearLayout>

                    <!-- Customer Address -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginEnd="8dp"
                            android:src="@drawable/ic_location"
                            android:tint="@color/primary_blue" />

                        <TextView
                            android:id="@+id/tvCustomerAddress"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="بغداد - الكرادة - شارع الرشيد" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Items Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="منتجات الطلب"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Items Count -->
                    <TextView
                        android:id="@+id/tvItemsCount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        tools:text="3 منتجات" />

                    <!-- Order Items RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvOrderItems"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_order_detail" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Summary Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="12dp"
                        android:text="ملخص الطلب"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- Subtotal -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="المجموع الفرعي:"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                        <TextView
                            android:id="@+id/tvSubtotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            tools:text="2500 د.ع" />

                    </LinearLayout>

                    <!-- Divider -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginVertical="8dp"
                        android:background="@color/divider_color" />

                    <!-- Total Amount -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="المبلغ الإجمالي:"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvTotalAmount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primary_blue"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            tools:text="2500 د.ع" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Order Notes Card (if available) -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tvNotesLabel"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="ملاحظات الطلب"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    <TextView
                        android:id="@+id/tvOrderNotes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:visibility="gone"
                        tools:text="يرجى التسليم في المساء بعد الساعة 6" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="vertical">

                <!-- Update Status Button -->
                <Button
                    android:id="@+id/btnUpdateStatus"
                    style="@style/PrimaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:drawableStart="@drawable/ic_update"
                    android:drawableTint="@color/white"
                    android:text="تحديث الحالة" />

                <!-- Secondary Actions Row -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- Generate Invoice -->
                    <Button
                        android:id="@+id/btnGenerateInvoice"
                        style="@style/SecondaryButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:layout_weight="1"
                        android:drawableStart="@drawable/ic_receipt"
                        android:drawableTint="@color/primary_blue"
                        android:text="فاتورة"
                        android:textSize="12sp" />

                    <!-- Call Customer -->
                    <Button
                        android:id="@+id/btnCallCustomer"
                        style="@style/SecondaryButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="4dp"
                        android:layout_weight="1"
                        android:drawableStart="@drawable/ic_phone"
                        android:drawableTint="@color/primary_blue"
                        android:text="اتصال"
                        android:textSize="12sp" />

                    <!-- Edit Order -->
                    <Button
                        android:id="@+id/btnEditOrder"
                        style="@style/SecondaryButton"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:layout_weight="1"
                        android:drawableStart="@drawable/ic_edit"
                        android:drawableTint="@color/primary_blue"
                        android:text="تعديل"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
