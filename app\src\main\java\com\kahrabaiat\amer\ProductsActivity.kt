package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import com.kahrabaiat.amer.adapters.ProductAdapter
import com.kahrabaiat.amer.databinding.ActivityProductsBinding
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.CartManager
import com.kahrabaiat.amer.utils.DataManager
import kotlinx.coroutines.launch

class ProductsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityProductsBinding
    private lateinit var cartManager: CartManager
    private lateinit var dataManager: DataManager
    private lateinit var productAdapter: ProductAdapter

    private var category: String? = null
    private var categoryName: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getIntentData()
        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        loadProducts()
    }

    private fun getIntentData() {
        category = intent.getStringExtra("category")
        categoryName = intent.getStringExtra("categoryName")
    }

    private fun initializeComponents() {
        cartManager = CartManager.getInstance(this)
        dataManager = DataManager.getInstance(this)
    }

    private fun setupToolbar() {
        binding.toolbar.title = categoryName ?: "جميع المنتجات"
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        productAdapter = ProductAdapter(
            onProductClick = { product ->
                val intent = Intent(this, ProductDetailActivity::class.java)
                intent.putExtra("product", product)
                startActivity(intent)
            },
            onAddToCartClick = { product ->
                cartManager.addToCart(product)
                showAddToCartMessage(product.name)
            }
        )

        binding.rvProducts.apply {
            layoutManager = GridLayoutManager(this@ProductsActivity, 2)
            adapter = productAdapter
        }
    }

    private fun loadProducts() {
        showLoading()

        lifecycleScope.launch {
            try {
                val products = if (category != null) {
                    dataManager.getProductsByCategory(category!!)
                } else {
                    dataManager.getAllProducts()
                }

                showProducts(products)
                android.util.Log.d("ProductsActivity", "Loaded ${products.size} products for category: $category")
            } catch (e: Exception) {
                android.util.Log.e("ProductsActivity", "Error loading products", e)
                showEmpty()
            }
        }
    }

    private fun showLoading() {
        binding.layoutLoading.visibility = View.VISIBLE
        binding.rvProducts.visibility = View.GONE
        binding.layoutEmpty.visibility = View.GONE
    }

    private fun showProducts(products: List<Product>) {
        binding.layoutLoading.visibility = View.GONE

        if (products.isNotEmpty()) {
            binding.rvProducts.visibility = View.VISIBLE
            binding.layoutEmpty.visibility = View.GONE
            productAdapter.submitList(products)
        } else {
            showEmpty()
        }
    }

    private fun showEmpty() {
        binding.layoutLoading.visibility = View.GONE
        binding.rvProducts.visibility = View.GONE
        binding.layoutEmpty.visibility = View.VISIBLE
    }

    private fun showAddToCartMessage(productName: String) {
        android.widget.Toast.makeText(
            this,
            "تم إضافة $productName إلى السلة",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
}
