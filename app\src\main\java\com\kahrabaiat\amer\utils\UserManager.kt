package com.kahrabaiat.amer.utils

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.kahrabaiat.amer.models.Permission
import com.kahrabaiat.amer.models.User
import com.kahrabaiat.amer.models.UserRole
import com.kahrabaiat.amer.models.UserRolePermissions
import kotlinx.coroutines.delay
import java.util.*

class UserManager(private val context: Context) {

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()

    // Mock users list (in real app, this would be from Firebase/API)
    private val mockUsers = mutableListOf<User>()

    init {
        initializeMockUsers()
    }

    private fun initializeMockUsers() {
        if (mockUsers.isEmpty()) {
            // Add default admin user
            mockUsers.add(
                User(
                    id = "admin_001",
                    name = "أحمد المدير",
                    email = "<EMAIL>",
                    phone = "07901234567",
                    role = UserRole.ADMIN,
                    permissions = UserRolePermissions.getDefaultPermissions(UserRole.ADMIN),
                    isActive = true,
                    createdAt = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000), // 30 days ago
                    lastLoginAt = System.currentTimeMillis() - (2 * 60 * 60 * 1000) // 2 hours ago
                )
            )

            // Add sample manager
            mockUsers.add(
                User(
                    id = "manager_001",
                    name = "فاطمة المديرة",
                    email = "<EMAIL>",
                    phone = "07701234567",
                    role = UserRole.MANAGER,
                    permissions = UserRolePermissions.getDefaultPermissions(UserRole.MANAGER),
                    isActive = true,
                    createdAt = System.currentTimeMillis() - (20 * 24 * 60 * 60 * 1000), // 20 days ago
                    lastLoginAt = System.currentTimeMillis() - (1 * 24 * 60 * 60 * 1000) // 1 day ago
                )
            )

            // Add sample employees
            mockUsers.add(
                User(
                    id = "employee_001",
                    name = "محمد الموظف",
                    email = "<EMAIL>",
                    phone = "07801234567",
                    role = UserRole.EMPLOYEE,
                    permissions = UserRolePermissions.getDefaultPermissions(UserRole.EMPLOYEE),
                    isActive = true,
                    createdAt = System.currentTimeMillis() - (15 * 24 * 60 * 60 * 1000), // 15 days ago
                    lastLoginAt = System.currentTimeMillis() - (3 * 60 * 60 * 1000) // 3 hours ago
                )
            )

            mockUsers.add(
                User(
                    id = "employee_002",
                    name = "سارة الموظفة",
                    email = "<EMAIL>",
                    phone = "07601234567",
                    role = UserRole.EMPLOYEE,
                    permissions = UserRolePermissions.getDefaultPermissions(UserRole.EMPLOYEE),
                    isActive = false, // Inactive user
                    createdAt = System.currentTimeMillis() - (10 * 24 * 60 * 60 * 1000), // 10 days ago
                    lastLoginAt = System.currentTimeMillis() - (5 * 24 * 60 * 60 * 1000) // 5 days ago
                )
            )

            // Add viewer
            mockUsers.add(
                User(
                    id = "viewer_001",
                    name = "علي المشاهد",
                    email = "<EMAIL>",
                    phone = "07501234567",
                    role = UserRole.VIEWER,
                    permissions = UserRolePermissions.getDefaultPermissions(UserRole.VIEWER),
                    isActive = true,
                    createdAt = System.currentTimeMillis() - (5 * 24 * 60 * 60 * 1000), // 5 days ago
                    lastLoginAt = System.currentTimeMillis() - (6 * 60 * 60 * 1000) // 6 hours ago
                )
            )
        }
    }

    // Current user management
    fun getCurrentUser(): User? {
        val currentUserId = prefs.getString(PREF_CURRENT_USER_ID, null)
        return currentUserId?.let { id -> mockUsers.find { it.id == id } }
    }

    fun setCurrentUser(user: User) {
        prefs.edit().putString(PREF_CURRENT_USER_ID, user.id).apply()

        // Update last login time
        val updatedUser = user.copy(lastLoginAt = System.currentTimeMillis())
        // Note: In real app, this would be async
        val index = mockUsers.indexOfFirst { it.id == updatedUser.id }
        if (index != -1) {
            mockUsers[index] = updatedUser
        }
    }

    fun logout() {
        prefs.edit().remove(PREF_CURRENT_USER_ID).apply()
    }

    fun isLoggedIn(): Boolean {
        return getCurrentUser() != null
    }

    // User CRUD operations
    suspend fun getAllUsers(): List<User> {
        delay(300) // Simulate network delay
        return mockUsers.toList()
    }

    suspend fun getUserById(id: String): User? {
        delay(200)
        return mockUsers.find { it.id == id }
    }

    suspend fun getUsersByRole(role: UserRole): List<User> {
        delay(200)
        return mockUsers.filter { it.role == role }
    }

    suspend fun getActiveUsers(): List<User> {
        delay(200)
        return mockUsers.filter { it.isActive }
    }

    suspend fun createUser(user: User): Boolean {
        delay(300)
        return try {
            // Check if email or phone already exists
            val existingUser = mockUsers.find { it.email == user.email || it.phone == user.phone }
            if (existingUser != null) {
                return false // User already exists
            }

            val newUser = user.copy(
                id = generateUserId(),
                createdAt = System.currentTimeMillis()
            )
            mockUsers.add(newUser)
            true
        } catch (e: Exception) {
            false
        }
    }

    suspend fun updateUser(user: User): Boolean {
        delay(300)
        return try {
            val index = mockUsers.indexOfFirst { it.id == user.id }
            if (index != -1) {
                mockUsers[index] = user
                true
            } else {
                false
            }
        } catch (e: Exception) {
            false
        }
    }

    suspend fun deleteUser(userId: String): Boolean {
        delay(300)
        return try {
            val removed = mockUsers.removeIf { it.id == userId }
            removed
        } catch (e: Exception) {
            false
        }
    }

    // Permission checking
    fun hasPermission(permission: Permission): Boolean {
        val currentUser = getCurrentUser() ?: return false
        return currentUser.permissions.contains(permission)
    }

    fun hasAnyPermission(permissions: List<Permission>): Boolean {
        val currentUser = getCurrentUser() ?: return false
        return permissions.any { currentUser.permissions.contains(it) }
    }

    fun hasAllPermissions(permissions: List<Permission>): Boolean {
        val currentUser = getCurrentUser() ?: return false
        return permissions.all { currentUser.permissions.contains(it) }
    }

    fun isAdmin(): Boolean {
        val currentUser = getCurrentUser() ?: return false
        return currentUser.role == UserRole.ADMIN
    }

    fun isManager(): Boolean {
        val currentUser = getCurrentUser() ?: return false
        return currentUser.role == UserRole.MANAGER || currentUser.role == UserRole.ADMIN
    }

    // Authentication
    suspend fun authenticate(email: String, password: String): User? {
        delay(500) // Simulate network delay

        // In real app, this would validate against server
        // For demo, we'll just check if user exists and use a simple password
        val user = mockUsers.find { it.email == email && it.isActive }

        // Simple password check (in real app, use proper authentication)
        if (user != null && password == "123456") {
            setCurrentUser(user)
            return user
        }

        return null
    }

    // Utility methods
    private fun generateUserId(): String {
        return "user_${System.currentTimeMillis()}_${Random().nextInt(1000)}"
    }

    fun getUserStats(): UserStats {
        val totalUsers = mockUsers.size
        val activeUsers = mockUsers.count { it.isActive }
        val inactiveUsers = totalUsers - activeUsers

        val roleStats = UserRole.values().associateWith { role ->
            mockUsers.count { it.role == role }
        }

        return UserStats(
            totalUsers = totalUsers,
            activeUsers = activeUsers,
            inactiveUsers = inactiveUsers,
            roleStats = roleStats
        )
    }

    companion object {
        private const val PREFS_NAME = "user_manager_prefs"
        private const val PREF_CURRENT_USER_ID = "current_user_id"
    }
}

data class UserStats(
    val totalUsers: Int,
    val activeUsers: Int,
    val inactiveUsers: Int,
    val roleStats: Map<UserRole, Int>
)
