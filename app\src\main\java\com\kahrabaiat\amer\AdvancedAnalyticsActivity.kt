package com.kahrabaiat.amer

import android.graphics.Color
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.github.mikephil.charting.animation.Easing
import com.github.mikephil.charting.charts.*
import com.github.mikephil.charting.components.XAxis
import com.github.mikephil.charting.data.*
import com.github.mikephil.charting.formatter.IndexAxisValueFormatter
import com.github.mikephil.charting.formatter.PercentFormatter
import com.github.mikephil.charting.utils.ColorTemplate
import com.kahrabaiat.amer.databinding.ActivityAdvancedAnalyticsBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.utils.FirebaseHelper
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class AdvancedAnalyticsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdvancedAnalyticsBinding
    private lateinit var firebaseHelper: FirebaseHelper

    private var allOrders = mutableListOf<Order>()
    private var allProducts = mutableListOf<Product>()

    private val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
    private val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale("ar"))

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityAdvancedAnalyticsBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupClickListeners()
            loadData()
        } catch (e: Exception) {
            android.util.Log.e("AdvancedAnalyticsActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة التحليلات: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        firebaseHelper = FirebaseHelper()
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "التحليلات المتقدمة"
            }
        } catch (e: Exception) {
            android.util.Log.e("AdvancedAnalyticsActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupClickListeners() {
        binding.swipeRefresh.setOnRefreshListener {
            loadData()
        }

        // Tab selection
        binding.chipSalesAnalytics.setOnClickListener {
            showSalesAnalytics()
        }

        binding.chipCustomerAnalytics.setOnClickListener {
            showCustomerAnalytics()
        }

        binding.chipProductAnalytics.setOnClickListener {
            showProductAnalytics()
        }

        binding.chipTrendAnalytics.setOnClickListener {
            showTrendAnalytics()
        }
    }

    private fun loadData() {
        lifecycleScope.launch {
            try {
                binding.swipeRefresh.isRefreshing = true
                binding.progressBar.visibility = View.VISIBLE

                val orders = firebaseHelper.getAllOrders()
                val products = firebaseHelper.getAllProducts()

                allOrders.clear()
                allOrders.addAll(orders)

                allProducts.clear()
                allProducts.addAll(products)

                // Show sales analytics by default
                showSalesAnalytics()

            } catch (e: Exception) {
                android.util.Log.e("AdvancedAnalyticsActivity", "Error loading data", e)
                showToast("خطأ في تحميل البيانات: ${e.message}")
            } finally {
                binding.swipeRefresh.isRefreshing = false
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun showSalesAnalytics() {
        try {
            // Update chip selection
            updateChipSelection(binding.chipSalesAnalytics)

            // Calculate sales metrics
            calculateSalesMetrics()

            // Setup charts
            setupSalesVolumeChart()
            setupRevenueGrowthChart()
            setupSalesConversionChart()

            // Show sales section
            binding.cardSalesAnalytics.visibility = View.VISIBLE

        } catch (e: Exception) {
            android.util.Log.e("AdvancedAnalyticsActivity", "Error showing sales analytics", e)
            showToast("خطأ في عرض تحليلات المبيعات: ${e.message}")
        }
    }

    private fun showCustomerAnalytics() {
        showToast("تحليلات العملاء قيد التطوير")
    }

    private fun showProductAnalytics() {
        showToast("تحليلات المنتجات قيد التطوير")
    }

    private fun showTrendAnalytics() {
        showToast("تحليلات الاتجاهات قيد التطوير")
    }

    private fun updateChipSelection(selectedChip: com.google.android.material.chip.Chip) {
        // Reset all chips
        binding.chipSalesAnalytics.isChecked = false
        binding.chipCustomerAnalytics.isChecked = false
        binding.chipProductAnalytics.isChecked = false
        binding.chipTrendAnalytics.isChecked = false

        // Select the clicked chip
        selectedChip.isChecked = true
    }

    private fun calculateSalesMetrics() {
        val completedOrders = allOrders.filter { it.status == "delivered" }
        val totalRevenue = completedOrders.sumOf { it.total }
        val totalOrders = completedOrders.size
        val averageOrderValue = if (totalOrders > 0) totalRevenue / totalOrders else 0.0

        // Calculate growth rates (comparing last 30 days vs previous 30 days)
        val calendar = Calendar.getInstance()
        val currentDate = calendar.timeInMillis
        calendar.add(Calendar.DAY_OF_MONTH, -30)
        val thirtyDaysAgo = calendar.timeInMillis
        calendar.add(Calendar.DAY_OF_MONTH, -30)
        val sixtyDaysAgo = calendar.timeInMillis

        val recentOrders = completedOrders.filter { it.orderDate >= thirtyDaysAgo }
        val previousOrders = completedOrders.filter { it.orderDate >= sixtyDaysAgo && it.orderDate < thirtyDaysAgo }

        val recentRevenue = recentOrders.sumOf { it.total }
        val previousRevenue = previousOrders.sumOf { it.total }

        val revenueGrowth = if (previousRevenue > 0) {
            ((recentRevenue - previousRevenue) / previousRevenue * 100)
        } else 0.0

        val orderGrowth = if (previousOrders.isNotEmpty()) {
            ((recentOrders.size - previousOrders.size).toDouble() / previousOrders.size * 100)
        } else 0.0

        // Update UI
        binding.tvTotalRevenueSales.text = "${numberFormat.format(totalRevenue.toInt())} د.ع"
        binding.tvTotalOrdersSales.text = numberFormat.format(totalOrders)
        binding.tvAverageOrderValueSales.text = "${numberFormat.format(averageOrderValue.toInt())} د.ع"
        binding.tvRevenueGrowth.text = "${String.format("%.1f", revenueGrowth)}%"
        binding.tvOrderGrowth.text = "${String.format("%.1f", orderGrowth)}%"

        // Calculate conversion rate (completed vs total orders)
        val conversionRate = if (allOrders.isNotEmpty()) {
            (completedOrders.size.toDouble() / allOrders.size * 100)
        } else 0.0
        binding.tvConversionRate.text = "${String.format("%.1f", conversionRate)}%"
    }







    // Chart setup methods
    private fun setupSalesVolumeChart() {
        val lineChart = binding.lineChartSalesVolume

        // Create sample data for last 7 days
        val entries = mutableListOf<Entry>()
        val labels = mutableListOf<String>()
        val calendar = Calendar.getInstance()

        for (i in 6 downTo 0) {
            calendar.time = Date()
            calendar.add(Calendar.DAY_OF_MONTH, -i)

            val dayStart = Calendar.getInstance().apply {
                time = calendar.time
                set(Calendar.HOUR_OF_DAY, 0)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
            }.timeInMillis

            val dayEnd = Calendar.getInstance().apply {
                time = calendar.time
                set(Calendar.HOUR_OF_DAY, 23)
                set(Calendar.MINUTE, 59)
                set(Calendar.SECOND, 59)
                set(Calendar.MILLISECOND, 999)
            }.timeInMillis

            val dayOrders = allOrders.filter {
                it.status == "delivered" && it.orderDate in dayStart..dayEnd
            }.size

            entries.add(Entry((6-i).toFloat(), dayOrders.toFloat()))
            labels.add(SimpleDateFormat("dd/MM", Locale("ar")).format(calendar.time))
        }

        if (entries.isEmpty()) {
            lineChart.visibility = View.GONE
            return
        }

        val dataSet = LineDataSet(entries, "عدد الطلبات اليومية")
        dataSet.apply {
            color = Color.parseColor("#2196F3")
            setCircleColor(Color.parseColor("#2196F3"))
            lineWidth = 3f
            circleRadius = 4f
            setDrawCircleHole(false)
            valueTextSize = 10f
        }

        val data = LineData(dataSet)

        lineChart.apply {
            this.data = data
            description.isEnabled = false

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = IndexAxisValueFormatter(labels)
                granularity = 1f
                setDrawGridLines(false)
            }

            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }

            axisRight.isEnabled = false
            legend.isEnabled = true

            animateX(1000)
            invalidate()
        }
    }

    private fun setupRevenueGrowthChart() {
        val barChart = binding.barChartRevenueGrowth

        // Create monthly revenue data for last 6 months
        val entries = mutableListOf<BarEntry>()
        val labels = mutableListOf<String>()
        val calendar = Calendar.getInstance()

        for (i in 5 downTo 0) {
            calendar.time = Date()
            calendar.add(Calendar.MONTH, -i)

            val monthStart = Calendar.getInstance().apply {
                time = calendar.time
                set(Calendar.DAY_OF_MONTH, 1)
                set(Calendar.HOUR_OF_DAY, 0)
                set(Calendar.MINUTE, 0)
                set(Calendar.SECOND, 0)
                set(Calendar.MILLISECOND, 0)
            }.timeInMillis

            val monthEnd = Calendar.getInstance().apply {
                time = calendar.time
                set(Calendar.DAY_OF_MONTH, getActualMaximum(Calendar.DAY_OF_MONTH))
                set(Calendar.HOUR_OF_DAY, 23)
                set(Calendar.MINUTE, 59)
                set(Calendar.SECOND, 59)
                set(Calendar.MILLISECOND, 999)
            }.timeInMillis

            val monthRevenue = allOrders.filter {
                it.status == "delivered" && it.orderDate in monthStart..monthEnd
            }.sumOf { it.total }

            entries.add(BarEntry((5-i).toFloat(), monthRevenue.toFloat()))
            labels.add(SimpleDateFormat("MMM", Locale("ar")).format(calendar.time))
        }

        if (entries.isEmpty()) {
            barChart.visibility = View.GONE
            return
        }

        val dataSet = BarDataSet(entries, "الإيرادات الشهرية")
        dataSet.colors = ColorTemplate.MATERIAL_COLORS.toList()
        dataSet.valueTextSize = 10f

        val data = BarData(dataSet)
        data.barWidth = 0.8f

        barChart.apply {
            this.data = data
            description.isEnabled = false
            setFitBars(true)

            xAxis.apply {
                position = XAxis.XAxisPosition.BOTTOM
                valueFormatter = IndexAxisValueFormatter(labels)
                granularity = 1f
                setDrawGridLines(false)
            }

            axisLeft.apply {
                setDrawGridLines(true)
                axisMinimum = 0f
            }

            axisRight.isEnabled = false
            legend.isEnabled = true

            animateY(1000)
            invalidate()
        }
    }

    private fun setupSalesConversionChart() {
        val pieChart = binding.pieChartSalesConversion

        val completedOrders = allOrders.count { it.status == "delivered" }
        val pendingOrders = allOrders.count {
            it.status == "pending" || it.status == "confirmed" || it.status == "processing"
        }
        val cancelledOrders = allOrders.count { it.status == "cancelled" }

        val entries = mutableListOf<PieEntry>()
        if (completedOrders > 0) entries.add(PieEntry(completedOrders.toFloat(), "مكتمل"))
        if (pendingOrders > 0) entries.add(PieEntry(pendingOrders.toFloat(), "معلق"))
        if (cancelledOrders > 0) entries.add(PieEntry(cancelledOrders.toFloat(), "ملغي"))

        if (entries.isEmpty()) {
            pieChart.visibility = View.GONE
            return
        }

        val dataSet = PieDataSet(entries, "معدل التحويل")
        dataSet.colors = listOf(
            Color.parseColor("#4CAF50"), // Green for completed
            Color.parseColor("#FF9800"), // Orange for pending
            Color.parseColor("#F44336")  // Red for cancelled
        )
        dataSet.valueTextSize = 12f
        dataSet.valueTextColor = Color.WHITE

        val data = PieData(dataSet)
        data.setValueFormatter(PercentFormatter())

        pieChart.apply {
            this.data = data
            description.isEnabled = false
            isRotationEnabled = true
            setUsePercentValues(true)
            setEntryLabelColor(Color.BLACK)
            setEntryLabelTextSize(10f)
            animateY(1000, Easing.EaseInOutQuad)
            invalidate()
        }
    }



    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
