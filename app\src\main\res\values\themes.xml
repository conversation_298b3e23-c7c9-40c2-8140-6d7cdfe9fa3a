<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.KahrabaiatAmer" parent="Theme.Material3.DayNight">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_blue</item>
        <item name="colorPrimaryVariant">@color/primary_blue_variant</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>

        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/accent_teal</item>
        <item name="colorSecondaryVariant">@color/accent_purple</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Surface colors -->
        <item name="colorSurface">@color/background_card</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorSurfaceVariant">@color/background_secondary</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_secondary</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <!-- Status bar color. -->
        <item name="android:statusBarColor">@color/primary_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_primary</item>
        <item name="android:windowLightNavigationBar">true</item>

        <!-- Window background -->
        <item name="android:windowBackground">@color/background_secondary</item>

        <!-- Text colors -->
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorTertiary">@color/text_tertiary</item>

        <!-- Elevation and shadows -->
        <item name="elevationOverlayEnabled">true</item>

        <!-- Arabic Font Support -->
        <item name="android:fontFamily">sans-serif</item>
        <item name="fontFamily">sans-serif</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.KahrabaiatAmer.Splash" parent="Theme.KahrabaiatAmer">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
    </style>

    <!-- Admin Activity Theme - No ActionBar -->
    <style name="Theme.KahrabaiatAmer.NoActionBar" parent="Theme.KahrabaiatAmer">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <!-- Modern Card Styles with Beautiful Gradients -->
    <style name="CardStyle">
        <item name="android:layout_margin">12dp</item>
        <item name="cardCornerRadius">20dp</item>
        <item name="cardElevation">8dp</item>
        <item name="android:background">@drawable/card_gradient_background</item>
        <item name="strokeWidth">1dp</item>
        <item name="strokeColor">@color/category_tools_primary</item>
    </style>

    <style name="CardStyleElevated" parent="CardStyle">
        <item name="cardElevation">12dp</item>
        <item name="cardCornerRadius">20dp</item>
    </style>

    <!-- Modern Button Styles with Beautiful Gradients -->
    <style name="PrimaryButton" parent="Widget.Material3.Button">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:background">@drawable/button_primary_gradient</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:elevation">6dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="SecondaryButton" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:layout_height">56dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="strokeColor">@color/primary_blue</item>
        <item name="strokeWidth">2dp</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="cornerRadius">16dp</item>
        <item name="android:background">@color/background_card</item>
    </style>

    <style name="SuccessButton" parent="PrimaryButton">
        <item name="android:background">@drawable/button_success_gradient</item>
    </style>

    <style name="DangerButton" parent="PrimaryButton">
        <item name="backgroundTint">@color/button_danger</item>
    </style>

    <style name="FloatingButton" parent="Widget.Material3.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/primary_blue</item>
        <item name="tint">@color/text_on_primary</item>
        <item name="elevation">8dp</item>
    </style>

    <!-- Modern Text Styles -->
    <style name="HeadlineText">
        <item name="android:textSize">28sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:letterSpacing">-0.02</item>
    </style>

    <style name="TitleText">
        <item name="android:textSize">22sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:letterSpacing">-0.01</item>
    </style>

    <style name="SubtitleText">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <style name="BodyText">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:lineSpacingExtra">4dp</item>
    </style>

    <style name="PriceText">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/primary_blue</item>
        <item name="android:letterSpacing">-0.01</item>
    </style>

    <style name="DiscountText">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/discount_red</item>
    </style>

    <style name="BadgeText">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Modern Input Field Styles -->
    <style name="InputField" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="android:layout_marginBottom">20dp</item>
        <item name="boxStrokeColor">@color/primary_blue</item>
        <item name="boxStrokeWidth">2dp</item>
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="hintTextColor">@color/text_hint</item>
        <item name="android:textColorHint">@color/text_hint</item>
        <item name="boxStrokeErrorColor">@color/error_red</item>
        <item name="errorTextColor">@color/error_red</item>
    </style>

    <style name="SearchInputField" parent="InputField">
        <item name="boxBackgroundColor">@color/background_card</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeColor">@color/border_medium</item>
    </style>

</resources>
