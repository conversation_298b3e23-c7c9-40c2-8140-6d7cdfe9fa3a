package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemSecurityEventBinding
import com.kahrabaiat.amer.security.SecurityMonitor
import java.text.SimpleDateFormat
import java.util.*

class SecurityEventAdapter : RecyclerView.Adapter<SecurityEventAdapter.SecurityEventViewHolder>() {
    
    private var events = listOf<SecurityMonitor.SecurityEventLog>()
    
    fun updateEvents(newEvents: List<SecurityMonitor.SecurityEventLog>) {
        events = newEvents.sortedByDescending { it.timestamp }
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SecurityEventViewHolder {
        val binding = ItemSecurityEventBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return SecurityEventViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: SecurityEventViewHolder, position: Int) {
        holder.bind(events[position])
    }
    
    override fun getItemCount(): Int = events.size
    
    inner class SecurityEventViewHolder(
        private val binding: ItemSecurityEventBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        private val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault())
        
        fun bind(event: SecurityMonitor.SecurityEventLog) {
            binding.apply {
                // تاريخ ووقت الحدث
                tvEventTime.text = dateFormat.format(Date(event.timestamp))
                
                // نوع الحدث
                tvEventType.text = getEventTypeDisplayName(event.eventType)
                
                // وصف الحدث
                tvEventDescription.text = event.description
                
                // أيقونة ولون حسب نوع الحدث
                val (iconRes, colorRes) = getEventIcon(event.eventType)
                ivEventIcon.setImageResource(iconRes)
                ivEventIcon.setColorFilter(itemView.context.getColor(colorRes))
                
                // عرض البيانات الإضافية إذا وجدت
                if (event.metadata.isNotEmpty()) {
                    val metadataText = event.metadata.entries.joinToString("\n") { "${it.key}: ${it.value}" }
                    tvEventMetadata.text = metadataText
                    tvEventMetadata.visibility = android.view.View.VISIBLE
                } else {
                    tvEventMetadata.visibility = android.view.View.GONE
                }
                
                // تلوين الخلفية حسب خطورة الحدث
                val backgroundRes = when (event.eventType) {
                    SecurityMonitor.SecurityEvent.FAILED_LOGIN,
                    SecurityMonitor.SecurityEvent.ACCOUNT_BLOCKED,
                    SecurityMonitor.SecurityEvent.SUSPICIOUS_ACTIVITY -> R.color.error_background
                    SecurityMonitor.SecurityEvent.SUCCESSFUL_LOGIN,
                    SecurityMonitor.SecurityEvent.ACCOUNT_UNBLOCKED -> R.color.success_background
                    else -> R.color.info_background
                }
                
                root.setBackgroundColor(itemView.context.getColor(backgroundRes))
            }
        }
        
        private fun getEventTypeDisplayName(eventType: SecurityMonitor.SecurityEvent): String {
            return when (eventType) {
                SecurityMonitor.SecurityEvent.SUCCESSFUL_LOGIN -> "تسجيل دخول ناجح"
                SecurityMonitor.SecurityEvent.FAILED_LOGIN -> "محاولة دخول فاشلة"
                SecurityMonitor.SecurityEvent.ACCOUNT_BLOCKED -> "حظر الحساب"
                SecurityMonitor.SecurityEvent.ACCOUNT_UNBLOCKED -> "إلغاء حظر الحساب"
                SecurityMonitor.SecurityEvent.SUSPICIOUS_ACTIVITY -> "نشاط مشبوه"
                SecurityMonitor.SecurityEvent.LOG_CLEARED -> "مسح السجل"
                SecurityMonitor.SecurityEvent.PASSWORD_CHANGED -> "تغيير كلمة المرور"
                SecurityMonitor.SecurityEvent.UNAUTHORIZED_ACCESS_ATTEMPT -> "محاولة وصول غير مصرح"
            }
        }
        
        private fun getEventIcon(eventType: SecurityMonitor.SecurityEvent): Pair<Int, Int> {
            return when (eventType) {
                SecurityMonitor.SecurityEvent.SUCCESSFUL_LOGIN -> Pair(R.drawable.ic_check_circle, R.color.success_green)
                SecurityMonitor.SecurityEvent.FAILED_LOGIN -> Pair(R.drawable.ic_error, R.color.error_red)
                SecurityMonitor.SecurityEvent.ACCOUNT_BLOCKED -> Pair(R.drawable.ic_block, R.color.error_red)
                SecurityMonitor.SecurityEvent.ACCOUNT_UNBLOCKED -> Pair(R.drawable.ic_unlock, R.color.success_green)
                SecurityMonitor.SecurityEvent.SUSPICIOUS_ACTIVITY -> Pair(R.drawable.ic_warning, R.color.warning_yellow)
                SecurityMonitor.SecurityEvent.LOG_CLEARED -> Pair(R.drawable.ic_delete, R.color.text_secondary)
                SecurityMonitor.SecurityEvent.PASSWORD_CHANGED -> Pair(R.drawable.ic_lock, R.color.primary_blue)
                SecurityMonitor.SecurityEvent.UNAUTHORIZED_ACCESS_ATTEMPT -> Pair(R.drawable.ic_security, R.color.error_red)
            }
        }
    }
}
