<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    tools:context=".CustomReportsActivity">

    <!-- App Bar -->
    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@drawable/gradient_background"
            android:elevation="8dp"
            app:titleTextColor="@color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <!-- Main Content -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipeRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Progress Bar -->
                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="32dp"
                    android:visibility="gone" />

                <!-- Date Range Selection -->
                <androidx.cardview.widget.CardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_calendar"
                            android:drawablePadding="8dp"
                            android:text="اختيار الفترة الزمنية"
                            android:textColor="@color/primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- Custom Date Range -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="8dp"
                                    android:text="من تاريخ"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btnSelectStartDate"
                                    style="@style/Widget.Material3.Button.OutlinedButton"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="8dp"
                                    app:icon="@drawable/ic_calendar"
                                    app:iconGravity="textStart"
                                    tools:text="01/01/2025" />

                                <TextView
                                    android:id="@+id/tvStartDate"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:gravity="center"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="01/01/2025" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginBottom="8dp"
                                    android:text="إلى تاريخ"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp" />

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btnSelectEndDate"
                                    style="@style/Widget.Material3.Button.OutlinedButton"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="8dp"
                                    app:icon="@drawable/ic_calendar"
                                    app:iconGravity="textStart"
                                    tools:text="31/01/2025" />

                                <TextView
                                    android:id="@+id/tvEndDate"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginTop="4dp"
                                    android:gravity="center"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="31/01/2025" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Date Range Summary -->
                        <TextView
                            android:id="@+id/tvDateRangeSummary"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:gravity="center"
                            android:textColor="@color/accent_teal"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="الفترة المحددة: 30 يوم" />

                        <!-- Quick Date Ranges -->
                        <com.google.android.material.chip.ChipGroup
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            app:chipSpacingHorizontal="8dp"
                            app:chipSpacingVertical="4dp">

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipToday"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="اليوم"
                                app:chipIcon="@drawable/ic_today" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipYesterday"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="أمس"
                                app:chipIcon="@drawable/ic_yesterday" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipLast7Days"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="آخر 7 أيام"
                                app:chipIcon="@drawable/ic_week" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipLast30Days"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="آخر 30 يوم"
                                app:chipIcon="@drawable/ic_month" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipThisMonth"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="هذا الشهر"
                                app:chipIcon="@drawable/ic_calendar" />

                            <com.google.android.material.chip.Chip
                                android:id="@+id/chipLastMonth"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="الشهر الماضي"
                                app:chipIcon="@drawable/ic_calendar" />

                        </com.google.android.material.chip.ChipGroup>

                        <!-- Generate Report Button -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnGenerateReport"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="إنشاء التقرير"
                            app:icon="@drawable/ic_chart"
                            app:iconGravity="textStart" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Report Summary -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardReportSummary"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_summary"
                            android:drawablePadding="8dp"
                            android:text="ملخص التقرير"
                            android:textColor="@color/primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- Orders Summary -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="25" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي الطلبات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvCompletedOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="20" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="مكتملة"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvPendingOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/warning_yellow"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="3" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="معلقة"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvCancelledOrders"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/error_red"
                                    android:textSize="24sp"
                                    android:textStyle="bold"
                                    tools:text="2" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="ملغية"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Revenue Summary -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvTotalRevenue"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/success_green"
                                    android:textSize="18sp"
                                    android:textStyle="bold"
                                    tools:text="2,500,000 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إجمالي الإيرادات"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvPendingRevenue"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/warning_yellow"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="300,000 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="إيرادات معلقة"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvAverageOrderValue"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="125,000 د.ع" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="متوسط قيمة الطلب"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="vertical">

                                <TextView
                                    android:id="@+id/tvCompletionRate"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:textColor="@color/accent_teal"
                                    android:textSize="16sp"
                                    android:textStyle="bold"
                                    tools:text="80%" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="معدل الإنجاز"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Charts Section -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardCharts"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_chart"
                            android:drawablePadding="8dp"
                            android:text="المخططات البيانية"
                            android:textColor="@color/primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- Daily Revenue Chart -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="الإيرادات اليومية"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.LineChart
                            android:id="@+id/lineChartDailyRevenue"
                            android:layout_width="match_parent"
                            android:layout_height="250dp"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:id="@+id/tvNoDailyData"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:gravity="center"
                            android:text="لا توجد بيانات إيرادات للفترة المحددة"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:visibility="gone" />

                        <!-- Order Status Chart -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="توزيع حالات الطلبات"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.PieChart
                            android:id="@+id/pieChartOrderStatus"
                            android:layout_width="match_parent"
                            android:layout_height="250dp"
                            android:layout_marginBottom="16dp" />

                        <TextView
                            android:id="@+id/tvNoStatusData"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:gravity="center"
                            android:text="لا توجد طلبات للفترة المحددة"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:visibility="gone" />

                        <!-- Top Products Chart -->
                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="أكثر المنتجات مبيعاً"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.github.mikephil.charting.charts.BarChart
                            android:id="@+id/barChartTopProducts"
                            android:layout_width="match_parent"
                            android:layout_height="250dp" />

                        <TextView
                            android:id="@+id/tvNoProductData"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="لا توجد مبيعات منتجات للفترة المحددة"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:visibility="gone" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Export Options -->
                <androidx.cardview.widget.CardView
                    android:id="@+id/cardExportOptions"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="16dp"
                            android:drawableStart="@drawable/ic_export"
                            android:drawablePadding="8dp"
                            android:text="تصدير التقرير"
                            android:textColor="@color/primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnExportPdf"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="8dp"
                                android:layout_weight="1"
                                android:text="تصدير PDF"
                                app:icon="@drawable/ic_pdf"
                                app:iconGravity="textStart" />

                            <com.google.android.material.button.MaterialButton
                                android:id="@+id/btnExportExcel"
                                style="@style/Widget.Material3.Button.OutlinedButton"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:layout_weight="1"
                                android:text="تصدير Excel"
                                app:icon="@drawable/ic_excel"
                                app:iconGravity="textStart" />

                        </LinearLayout>

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
