package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.OrderItemsAdapter
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.databinding.ActivityOrderDetailsBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.utils.NotificationHelper
import com.kahrabaiat.amer.utils.PDFGenerator
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class OrderDetailsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityOrderDetailsBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var notificationHelper: NotificationHelper
    private lateinit var pdfGenerator: PDFGenerator
    private lateinit var orderItemsAdapter: OrderItemsAdapter
    
    private var currentOrder: Order? = null
    private var orderId: Int = -1

    companion object {
        const val EXTRA_ORDER_ID = "order_id"
        const val EXTRA_ORDER = "order"
        const val REQUEST_CODE_EDIT_ORDER = 1001
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityOrderDetailsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        getOrderFromIntent()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadOrderDetails()
    }

    private fun initializeComponents() {
        databaseHelper = DatabaseHelper.getInstance(this)
        notificationHelper = NotificationHelper(this)
        pdfGenerator = PDFGenerator(this)
    }

    private fun getOrderFromIntent() {
        // Try to get order directly first
        currentOrder = intent.getParcelableExtra(EXTRA_ORDER)
        
        // If not available, get order ID
        if (currentOrder == null) {
            orderId = intent.getIntExtra(EXTRA_ORDER_ID, -1)
        } else {
            orderId = currentOrder!!.id
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "تفاصيل الطلب #$orderId"
        }
    }

    private fun setupRecyclerView() {
        orderItemsAdapter = OrderItemsAdapter()
        binding.rvOrderItems.apply {
            layoutManager = LinearLayoutManager(this@OrderDetailsActivity)
            adapter = orderItemsAdapter
        }
    }

    private fun setupClickListeners() {
        binding.btnUpdateStatus.setOnClickListener {
            showStatusUpdateDialog()
        }

        binding.btnGenerateInvoice.setOnClickListener {
            generateInvoice()
        }

        binding.btnCallCustomer.setOnClickListener {
            callCustomer()
        }

        binding.btnEditOrder.setOnClickListener {
            editOrder()
        }
    }

    private fun loadOrderDetails() {
        if (currentOrder != null) {
            displayOrderDetails(currentOrder!!)
        } else if (orderId != -1) {
            lifecycleScope.launch {
                try {
                    binding.progressBar.visibility = View.VISIBLE
                    val order = databaseHelper.getAllOrders().find { it.id == orderId }
                    
                    if (order != null) {
                        currentOrder = order
                        displayOrderDetails(order)
                    } else {
                        showToast("لم يتم العثور على الطلب")
                        finish()
                    }
                } catch (e: Exception) {
                    showToast("خطأ في تحميل تفاصيل الطلب: ${e.message}")
                    finish()
                } finally {
                    binding.progressBar.visibility = View.GONE
                }
            }
        } else {
            showToast("معرف الطلب غير صحيح")
            finish()
        }
    }

    private fun displayOrderDetails(order: Order) {
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))

        with(binding) {
            // Order Header
            tvOrderNumber.text = "طلب #${order.id}"
            tvOrderDate.text = dateFormat.format(Date(order.orderDate))
            tvOrderStatus.text = getStatusDisplayName(order.status)
            updateStatusColor(order.status)

            // Customer Information
            tvCustomerName.text = order.customerName
            tvCustomerPhone.text = order.customerPhone
            tvCustomerAddress.text = order.customerAddress

            // Order Summary
            tvItemsCount.text = "${order.items.size} منتج"
            tvSubtotal.text = "${numberFormat.format(order.total.toInt())} د.ع"
            tvTotalAmount.text = "${numberFormat.format(order.total.toInt())} د.ع"

            // Order Items
            orderItemsAdapter.submitList(order.items)

            // Notes
            if (order.notes.isNotEmpty()) {
                tvOrderNotes.text = order.notes
                tvOrderNotes.visibility = View.VISIBLE
                tvNotesLabel.visibility = View.VISIBLE
            } else {
                tvOrderNotes.visibility = View.GONE
                tvNotesLabel.visibility = View.GONE
            }

            // Update button states based on status
            updateButtonStates(order.status)
        }
    }

    private fun updateStatusColor(status: String) {
        val color = when (status.lowercase()) {
            "pending" -> R.color.warning_yellow
            "confirmed" -> R.color.primary_blue
            "processing" -> R.color.accent_orange
            "shipped" -> R.color.accent_teal
            "delivered" -> R.color.success_green
            "cancelled" -> R.color.error_red
            else -> R.color.text_secondary
        }
        
        binding.tvOrderStatus.setTextColor(getColor(color))
        binding.statusIndicator.setBackgroundColor(getColor(color))
    }

    private fun updateButtonStates(status: String) {
        val isCompleted = status == "delivered" || status == "cancelled"
        
        binding.btnUpdateStatus.isEnabled = !isCompleted
        binding.btnEditOrder.isEnabled = status == "pending"
        
        if (isCompleted) {
            binding.btnUpdateStatus.alpha = 0.5f
            if (status == "cancelled") {
                binding.btnEditOrder.alpha = 0.5f
            }
        }
    }

    private fun showStatusUpdateDialog() {
        val currentOrder = this.currentOrder ?: return
        
        val statuses = arrayOf(
            "pending" to "معلق",
            "confirmed" to "مؤكد", 
            "processing" to "قيد المعالجة",
            "shipped" to "مشحون",
            "delivered" to "مسلم",
            "cancelled" to "ملغي"
        )
        
        val statusNames = statuses.map { it.second }.toTypedArray()
        val currentIndex = statuses.indexOfFirst { it.first == currentOrder.status }
        
        AlertDialog.Builder(this)
            .setTitle("تحديث حالة الطلب")
            .setSingleChoiceItems(statusNames, currentIndex) { dialog, which ->
                val newStatus = statuses[which].first
                updateOrderStatus(newStatus)
                dialog.dismiss()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun updateOrderStatus(newStatus: String) {
        val currentOrder = this.currentOrder ?: return
        
        lifecycleScope.launch {
            try {
                val success = databaseHelper.updateOrderStatus(currentOrder.id, newStatus)
                
                if (success) {
                    val oldStatus = currentOrder.status
                    <EMAIL> = currentOrder.copy(status = newStatus)
                    
                    displayOrderDetails(<EMAIL>!!)
                    showToast("تم تحديث حالة الطلب بنجاح")
                    
                    // Send notification
                    notificationHelper.sendOrderStatusUpdateNotification(
                        <EMAIL>!!,
                        oldStatus,
                        newStatus
                    )
                    
                } else {
                    showToast("فشل في تحديث حالة الطلب")
                }
            } catch (e: Exception) {
                showToast("خطأ في تحديث حالة الطلب: ${e.message}")
            }
        }
    }

    private fun generateInvoice() {
        val currentOrder = this.currentOrder ?: return
        
        lifecycleScope.launch {
            try {
                binding.btnGenerateInvoice.isEnabled = false
                binding.btnGenerateInvoice.text = "جاري الإنشاء..."
                
                try {
                    val result = pdfGenerator.generateInvoice(currentOrder)
                    showToast("تم إنشاء الفاتورة بنجاح")
                    // يمكن إضافة فتح الفاتورة هنا
                } catch (e: Exception) {
                    showToast("فشل في إنشاء الفاتورة: ${e.message}")
                }
                
            } catch (e: Exception) {
                showToast("خطأ في إنشاء الفاتورة: ${e.message}")
            } finally {
                binding.btnGenerateInvoice.isEnabled = true
                binding.btnGenerateInvoice.text = "إنشاء فاتورة"
            }
        }
    }

    private fun callCustomer() {
        val currentOrder = this.currentOrder ?: return
        
        if (currentOrder.customerPhone.isNotEmpty()) {
            val intent = Intent(Intent.ACTION_DIAL).apply {
                data = android.net.Uri.parse("tel:${currentOrder.customerPhone}")
            }
            startActivity(intent)
        } else {
            showToast("رقم الهاتف غير متوفر")
        }
    }

    private fun editOrder() {
        val currentOrder = this.currentOrder ?: return

        if (currentOrder.status != "pending" && currentOrder.status != "confirmed") {
            showToast("لا يمكن تعديل الطلبات المشحونة أو المسلمة")
            return
        }

        val intent = Intent(this, EditOrderActivity::class.java)
        intent.putExtra(EditOrderActivity.EXTRA_ORDER, currentOrder)
        startActivityForResult(intent, REQUEST_CODE_EDIT_ORDER)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == REQUEST_CODE_EDIT_ORDER && resultCode == RESULT_OK) {
            // إعادة تحميل تفاصيل الطلب بعد التعديل
            loadOrderDetails()
            showToast("تم تحديث الطلب بنجاح")
        }
    }



    private fun getStatusDisplayName(status: String): String {
        return when (status.lowercase()) {
            "pending" -> "معلق"
            "confirmed" -> "مؤكد"
            "processing" -> "قيد المعالجة"
            "shipped" -> "مشحون"
            "delivered" -> "مسلم"
            "cancelled" -> "ملغي"
            else -> "غير محدد"
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.order_details_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_share -> {
                shareOrderDetails()
                true
            }
            R.id.action_delete -> {
                showDeleteConfirmation()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun shareOrderDetails() {
        val currentOrder = this.currentOrder ?: return
        
        val details = buildString {
            append("تفاصيل الطلب #${currentOrder.id}\n")
            append("العميل: ${currentOrder.customerName}\n")
            append("الهاتف: ${currentOrder.customerPhone}\n")
            append("العنوان: ${currentOrder.customerAddress}\n")
            append("المبلغ الإجمالي: ${currentOrder.total.toInt()} د.ع\n")
            append("الحالة: ${getStatusDisplayName(currentOrder.status)}\n")
            append("التاريخ: ${SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar")).format(Date(currentOrder.orderDate))}\n\n")
            append("المنتجات:\n")
            currentOrder.items.forEach { item ->
                append("- ${item.productName} (${item.quantity}x${item.price.toInt()}) = ${item.total.toInt()} د.ع\n")
            }
        }
        
        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, details)
            putExtra(Intent.EXTRA_SUBJECT, "تفاصيل الطلب #${currentOrder.id}")
        }
        
        startActivity(Intent.createChooser(intent, "مشاركة تفاصيل الطلب"))
    }

    private fun showDeleteConfirmation() {
        AlertDialog.Builder(this)
            .setTitle("حذف الطلب")
            .setMessage("هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.")
            .setPositiveButton("حذف") { _, _ ->
                deleteOrder()
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun deleteOrder() {
        val currentOrder = this.currentOrder ?: return
        
        lifecycleScope.launch {
            try {
                val success = databaseHelper.deleteOrder(currentOrder.id)
                
                if (success) {
                    showToast("تم حذف الطلب بنجاح")
                    setResult(RESULT_OK)
                    finish()
                } else {
                    showToast("فشل في حذف الطلب")
                }
            } catch (e: Exception) {
                showToast("خطأ في حذف الطلب: ${e.message}")
            }
        }
    }
}
