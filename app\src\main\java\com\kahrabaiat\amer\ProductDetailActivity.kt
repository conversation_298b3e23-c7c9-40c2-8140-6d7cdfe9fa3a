package com.kahrabaiat.amer

import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.adapters.SimpleReviewAdapter
import com.kahrabaiat.amer.databinding.ActivityProductDetailBinding
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.models.ProductReviewStats
import com.kahrabaiat.amer.models.Review
import com.kahrabaiat.amer.utils.CartManager
import com.kahrabaiat.amer.utils.ReviewManager
import kotlinx.coroutines.launch

class ProductDetailActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityProductDetailBinding
    private lateinit var cartManager: CartManager
    private lateinit var reviewManager: ReviewManager
    private lateinit var product: Product
    private lateinit var reviewAdapter: SimpleReviewAdapter
    private var selectedQuantity = 1
    private var reviewStats: ProductReviewStats? = null
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityProductDetailBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        getIntentData()
        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        displayProductInfo()
        loadReviews()
    }
    
    private fun getIntentData() {
        product = intent.getParcelableExtra("product") ?: return finish()
    }
    
    private fun initializeComponents() {
        cartManager = CartManager.getInstance(this)
        reviewManager = ReviewManager.getInstance(this)
    }

    private fun setupRecyclerView() {
        reviewAdapter = SimpleReviewAdapter()
        binding.rvReviews.apply {
            layoutManager = LinearLayoutManager(this@ProductDetailActivity)
            adapter = reviewAdapter
        }
    }
    
    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }
    
    private fun setupClickListeners() {
        binding.btnDecrease.setOnClickListener {
            if (selectedQuantity > 1) {
                selectedQuantity--
                updateQuantityDisplay()
            }
        }
        
        binding.btnIncrease.setOnClickListener {
            if (selectedQuantity < product.stock) {
                selectedQuantity++
                updateQuantityDisplay()
            } else {
                showStockLimitMessage()
            }
        }
        
        binding.btnAddToCart.setOnClickListener {
            if (product.isInStock()) {
                cartManager.addToCart(product, selectedQuantity)
                showAddToCartMessage()
            }
        }

        binding.btnAddReview.setOnClickListener {
            val intent = Intent(this, AddReviewActivity::class.java)
            intent.putExtra("product", product)
            startActivityForResult(intent, REQUEST_ADD_REVIEW)
        }

        binding.btnViewAllReviews.setOnClickListener {
            // TODO: إنشاء ProductReviewsActivity لاحقاً
            android.widget.Toast.makeText(this, "سيتم إضافة صفحة جميع المراجعات قريباً", android.widget.Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun displayProductInfo() {
        binding.apply {
            // Product name
            tvProductName.text = product.name
            
            // Product image
            if (product.imageUrl.isNotEmpty()) {
                Glide.with(this@ProductDetailActivity)
                    .load(product.imageUrl)
                    .placeholder(R.drawable.ic_product_placeholder)
                    .error(R.drawable.ic_product_placeholder)
                    .into(ivProductImage)
            } else {
                ivProductImage.setImageResource(R.drawable.ic_product_placeholder)
            }
            
            // Price handling
            if (product.discount > 0) {
                // Show discounted price
                tvCurrentPrice.text = product.getFormattedPrice()
                tvOriginalPrice.text = product.getOriginalFormattedPrice()
                tvOriginalPrice.visibility = View.VISIBLE
                tvOriginalPrice.paintFlags = tvOriginalPrice.paintFlags or Paint.STRIKE_THRU_TEXT_FLAG
                
                // Show discount badge
                tvDiscount.text = "خصم ${product.discount}%"
                tvDiscount.visibility = View.VISIBLE
            } else {
                // Show regular price
                tvCurrentPrice.text = product.getFormattedPrice()
                tvOriginalPrice.visibility = View.GONE
                tvDiscount.visibility = View.GONE
            }
            
            // Stock status
            if (product.isInStock()) {
                tvStockStatus.text = getString(R.string.available)
                tvStockStatus.setBackgroundResource(R.drawable.stock_badge_background)
                tvStockCount.text = getString(R.string.stock_count, product.stock)
                
                // Enable quantity selection and add to cart
                cardQuantitySelection.visibility = View.VISIBLE
                btnAddToCart.isEnabled = true
                btnAddToCart.alpha = 1.0f
            } else {
                tvStockStatus.text = getString(R.string.out_of_stock)
                tvStockStatus.setBackgroundColor(getColor(R.color.out_of_stock_red))
                tvStockCount.text = "غير متوفر"
                
                // Disable quantity selection and add to cart
                cardQuantitySelection.visibility = View.GONE
                btnAddToCart.isEnabled = false
                btnAddToCart.alpha = 0.5f
            }
            
            // Product description
            tvProductDescription.text = product.description.ifEmpty { "لا يوجد وصف متاح للمنتج" }
            
            updateQuantityDisplay()
        }
    }
    
    private fun updateQuantityDisplay() {
        binding.tvQuantity.text = selectedQuantity.toString()
        
        // Update button states
        binding.btnDecrease.isEnabled = selectedQuantity > 1
        binding.btnDecrease.alpha = if (binding.btnDecrease.isEnabled) 1.0f else 0.5f
        
        binding.btnIncrease.isEnabled = selectedQuantity < product.stock
        binding.btnIncrease.alpha = if (binding.btnIncrease.isEnabled) 1.0f else 0.5f
    }
    
    private fun showStockLimitMessage() {
        android.widget.Toast.makeText(
            this,
            "لا يمكن إضافة أكثر من الكمية المتوفرة (${product.stock})",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }
    
    private fun showAddToCartMessage() {
        val message = if (selectedQuantity == 1) {
            "تم إضافة ${product.name} إلى السلة"
        } else {
            "تم إضافة $selectedQuantity من ${product.name} إلى السلة"
        }
        
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun loadReviews() {
        lifecycleScope.launch {
            try {
                // تحميل إحصائيات المراجعات
                reviewStats = reviewManager.getProductReviewStats(product.id)
                updateReviewStats()

                // تحميل المراجعات (أول 3 مراجعات فقط)
                val reviews = reviewManager.getProductReviews(product.id).take(3)
                reviewAdapter.submitList(reviews)

                // إظهار أو إخفاء قسم المراجعات
                if (reviews.isNotEmpty()) {
                    binding.layoutReviews.visibility = View.VISIBLE
                    binding.tvNoReviews.visibility = View.GONE
                } else {
                    binding.layoutReviews.visibility = View.VISIBLE
                    binding.tvNoReviews.visibility = View.VISIBLE
                    binding.rvReviews.visibility = View.GONE
                    binding.btnViewAllReviews.visibility = View.GONE
                }

            } catch (e: Exception) {
                android.util.Log.e("ProductDetail", "Error loading reviews", e)
            }
        }
    }

    private fun updateReviewStats() {
        val stats = reviewStats ?: return

        binding.apply {
            if (stats.totalReviews > 0) {
                // إظهار التقييم العام
                ratingBarOverall.rating = stats.averageRating
                tvAverageRating.text = stats.getAverageRatingText()
                tvTotalReviews.text = "(${stats.totalReviews} مراجعة)"
                tvRatingDescription.text = stats.getOverallRatingDescription()

                layoutRatingStats.visibility = View.VISIBLE

                // إظهار نسبة التقييمات الإيجابية
                val positivePercentage = stats.getPositiveRatingPercentage()
                tvPositiveRating.text = "$positivePercentage% من العملاء راضون عن هذا المنتج"

            } else {
                layoutRatingStats.visibility = View.GONE
                tvPositiveRating.text = "كن أول من يقيم هذا المنتج"
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_ADD_REVIEW && resultCode == RESULT_OK) {
            // إعادة تحميل المراجعات بعد إضافة مراجعة جديدة
            loadReviews()
        }
    }

    companion object {
        private const val REQUEST_ADD_REVIEW = 1001
    }
}
