package com.kahrabaiat.amer

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.kahrabaiat.amer.databinding.ActivityAdminLoginBinding
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class AdminLoginActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAdminLoginBinding
    private lateinit var sharedPreferences: SharedPreferences

    companion object {
        private const val PREFS_NAME = "admin_prefs"
        private const val KEY_IS_LOGGED_IN = "is_logged_in"
        private const val KEY_LOGIN_TIME = "login_time"
        private const val SESSION_DURATION = 24 * 60 * 60 * 1000L // 24 ساعة

        // بيانات الإدارة الافتراضية (يجب تشفيرها في الإنتاج)
        private const val ADMIN_USERNAME = "admin"
        private const val ADMIN_PASSWORD = "kahrabaiat2024"
        private const val OWNER_USERNAME = "amer"
        private const val OWNER_PASSWORD = "amer123456"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAdminLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE)

        // التحقق من وجود جلسة نشطة
        if (isValidSession()) {
            navigateToAdmin()
            return
        }

        setupUI()
        setupClickListeners()
    }

    private fun setupUI() {
        // تعيين القيم الافتراضية للاختبار (يجب إزالتها في الإنتاج)
        // Pre-fill for testing
        binding.etUsername.setText("admin")
        binding.etPassword.setText("kahrabaiat2024")
    }

    private fun setupClickListeners() {
        binding.btnLogin.setOnClickListener {
            val username = binding.etUsername.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()

            if (validateInput(username, password)) {
                performLogin(username, password)
            }
        }

        binding.tvBack.setOnClickListener {
            finish()
        }
    }

    private fun validateInput(username: String, password: String): Boolean {
        if (username.isEmpty()) {
            binding.etUsername.error = "يرجى إدخال اسم المستخدم"
            binding.etUsername.requestFocus()
            return false
        }

        if (password.isEmpty()) {
            binding.etPassword.error = "يرجى إدخال كلمة المرور"
            binding.etPassword.requestFocus()
            return false
        }

        if (password.length < 6) {
            binding.etPassword.error = "كلمة المرور يجب أن تكون 6 أحرف على الأقل"
            binding.etPassword.requestFocus()
            return false
        }

        // مسح الأخطاء
        binding.etUsername.error = null
        binding.etPassword.error = null

        return true
    }

    private fun performLogin(username: String, password: String) {
        // إظهار مؤشر التحميل
        binding.btnLogin.isEnabled = false
        binding.btnLogin.text = "جاري تسجيل الدخول..."

        lifecycleScope.launch {
            // محاكاة تأخير الشبكة
            delay(1500)

            // استخدام النظام الآمن الجديد
            val authManager = com.kahrabaiat.amer.utils.AuthManager.getInstance(this@AdminLoginActivity)
            val loginResult = authManager.secureLogin(username, password)

            binding.btnLogin.isEnabled = true
            binding.btnLogin.text = "تسجيل الدخول"

            when (loginResult) {
                is com.kahrabaiat.amer.utils.AuthManager.LoginResult.SUCCESS -> {
                    showSuccessMessage(loginResult.userType)
                    // تأخير قصير قبل الانتقال
                    delay(1000)
                    navigateToAdmin()
                }
                is com.kahrabaiat.amer.utils.AuthManager.LoginResult.FAILED -> {
                    showErrorMessage(loginResult.message)
                }
                is com.kahrabaiat.amer.utils.AuthManager.LoginResult.BLOCKED -> {
                    showBlockedMessage(loginResult.message)
                }
            }
        }
    }

    private fun authenticateUser(username: String, password: String): LoginResult {
        return when {
            username == ADMIN_USERNAME && password == ADMIN_PASSWORD -> {
                LoginResult(true, com.kahrabaiat.amer.utils.AuthManager.UserType.ADMIN, "")
            }
            username == OWNER_USERNAME && password == OWNER_PASSWORD -> {
                LoginResult(true, com.kahrabaiat.amer.utils.AuthManager.UserType.OWNER, "")
            }
            else -> {
                LoginResult(false, com.kahrabaiat.amer.utils.AuthManager.UserType.GUEST, "اسم المستخدم أو كلمة المرور غير صحيحة")
            }
        }
    }

    private fun saveLoginSession(userType: com.kahrabaiat.amer.utils.AuthManager.UserType) {
        sharedPreferences.edit().apply {
            putBoolean(KEY_IS_LOGGED_IN, true)
            putLong(KEY_LOGIN_TIME, System.currentTimeMillis())
            putString("user_type", userType.name)
            apply()
        }
    }

    private fun isValidSession(): Boolean {
        val isLoggedIn = sharedPreferences.getBoolean(KEY_IS_LOGGED_IN, false)
        val loginTime = sharedPreferences.getLong(KEY_LOGIN_TIME, 0)
        val currentTime = System.currentTimeMillis()

        return isLoggedIn && (currentTime - loginTime) < SESSION_DURATION
    }

    private fun showSuccessMessage(userType: com.kahrabaiat.amer.utils.AuthManager.UserType) {
        val message = when (userType) {
            com.kahrabaiat.amer.utils.AuthManager.UserType.ADMIN -> "مرحباً بك في لوحة الإدارة"
            com.kahrabaiat.amer.utils.AuthManager.UserType.OWNER -> "مرحباً بك يا أستاذ عامر"
            com.kahrabaiat.amer.utils.AuthManager.UserType.GUEST -> ""
        }
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    private fun showBlockedMessage(message: String) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("حساب محظور")
            .setMessage("تم حظر الحساب بعد محاولات دخول فاشلة متعددة.\n\n$message")
            .setIcon(R.drawable.ic_warning)
            .setPositiveButton("موافق") { dialog, _ ->
                dialog.dismiss()
                // مسح الحقول وإعادة تعيين الزر
                binding.etUsername.setText("")
                binding.etPassword.setText("")
                binding.btnLogin.isEnabled = false
            }
            .setCancelable(false)
            .show()

        // تأثير بصري للحظر
        binding.etUsername.error = "حساب محظور"
        binding.etPassword.error = "حساب محظور"
    }

    private fun showErrorMessage(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()

        // إضافة تأثير بصري للخطأ
        binding.etUsername.error = " "
        binding.etPassword.error = " "

        // انيميشن هز للخطأ
        val shakeAnimation = android.view.animation.AnimationUtils.loadAnimation(this, R.anim.shake)
        binding.etUsername.startAnimation(shakeAnimation)
        binding.etPassword.startAnimation(shakeAnimation)

        lifecycleScope.launch {
            delay(3000)
            binding.etUsername.error = null
            binding.etPassword.error = null
        }
    }

    private fun navigateToAdmin() {
        val intent = Intent(this, AdminActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }

    // Data classes
    data class LoginResult(
        val isSuccess: Boolean,
        val userType: com.kahrabaiat.amer.utils.AuthManager.UserType,
        val errorMessage: String
    )


}
