package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemCategoryBinding
import com.kahrabaiat.amer.models.Category
import com.kahrabaiat.amer.models.CategoryConstants

class CategoryAdapter(
    private val onCategoryClick: (Category) -> Unit
) : ListAdapter<Category, CategoryAdapter.CategoryViewHolder>(CategoryDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CategoryViewHolder {
        val binding = ItemCategoryBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return CategoryViewHolder(binding)
    }

    override fun onBindViewHolder(holder: CategoryViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class CategoryViewHolder(
        private val binding: ItemCategoryBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(category: Category) {
            binding.apply {
                tvCategoryName.text = category.name
                
                // Set category icon based on category type
                val iconRes = when (category.id) {
                    CategoryConstants.ELECTRICAL_APPLIANCES -> R.drawable.ic_electrical
                    CategoryConstants.HOME_APPLIANCES -> R.drawable.ic_home
                    CategoryConstants.HAND_TOOLS -> R.drawable.ic_tools
                    else -> R.drawable.ic_category_default
                }
                ivCategoryIcon.setImageResource(iconRes)
                
                // Show product count if available
                if (category.productCount > 0) {
                    tvProductCount.text = "${category.productCount} منتج"
                } else {
                    tvProductCount.text = ""
                }

                root.setOnClickListener {
                    onCategoryClick(category)
                }
            }
        }
    }

    private class CategoryDiffCallback : DiffUtil.ItemCallback<Category>() {
        override fun areItemsTheSame(oldItem: Category, newItem: Category): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Category, newItem: Category): Boolean {
            return oldItem == newItem
        }
    }
}
