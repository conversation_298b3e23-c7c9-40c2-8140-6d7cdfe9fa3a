package com.kahrabaiat.amer

import android.os.Bundle
import android.view.MenuItem
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.SecurityEventAdapter
import com.kahrabaiat.amer.databinding.ActivitySecuritySettingsBinding
import com.kahrabaiat.amer.security.AppIntegrityChecker
import com.kahrabaiat.amer.security.SecurityMonitor
import com.kahrabaiat.amer.utils.AuthManager
import kotlinx.coroutines.launch

class SecuritySettingsActivity : BaseAdminActivity() {

    private lateinit var binding: ActivitySecuritySettingsBinding

    private lateinit var securityEventAdapter: SecurityEventAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySecuritySettingsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadSecurityData()
    }

    private fun initializeComponents() {
        securityEventAdapter = SecurityEventAdapter()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "إعدادات الأمان"
        }
    }

    private fun setupRecyclerView() {
        binding.rvSecurityEvents.apply {
            layoutManager = LinearLayoutManager(this@SecuritySettingsActivity)
            adapter = securityEventAdapter
        }
    }

    private fun setupClickListeners() {
        binding.btnCheckIntegrity.setOnClickListener {
            checkAppIntegrity()
        }

        binding.btnUnblockAccount.setOnClickListener {
            unblockAccount()
        }

        binding.btnClearSecurityLog.setOnClickListener {
            clearSecurityLog()
        }

        binding.btnRefreshData.setOnClickListener {
            loadSecurityData()
        }

        binding.btnChangePassword.setOnClickListener {
            showChangePasswordDialog()
        }

        binding.btnChangePasswordMain.setOnClickListener {
            showChangePasswordDialog()
        }

        binding.btnExportReport.setOnClickListener {
            exportSecurityReport()
        }
    }

    private fun loadSecurityData() {
        lifecycleScope.launch {
            try {
                // تحميل تقرير الأمان
                val securityMonitor = SecurityMonitor.getInstance(this@SecuritySettingsActivity)
                val securityReport = securityMonitor.getSecurityReport()
                displaySecurityReport(securityReport)

                // تحميل سجل الأحداث الأمنية
                val events = securityMonitor.getSecurityEvents()
                securityEventAdapter.updateEvents(events)

                // عرض آخر تغيير لكلمة المرور (تعطيل مؤقت)
                // updatePasswordChangeInfo(events)

                showToast("تم تحديث بيانات الأمان")

            } catch (e: Exception) {
                showToast("خطأ في تحميل بيانات الأمان: ${e.message}")
            }
        }
    }



    private fun displaySecurityReport(report: SecurityMonitor.SecurityReport) {
        binding.apply {
            tvTotalEvents.text = "إجمالي الأحداث: ${report.totalEvents}"
            tvRecentEvents.text = "أحداث آخر 24 ساعة: ${report.recentEvents}"
            tvFailedLogins.text = "محاولات دخول فاشلة: ${report.failedLoginsLast24h}"
            tvSuspiciousActivities.text = "أنشطة مشبوهة: ${report.suspiciousActivitiesLast24h}"
            tvCurrentAttempts.text = "المحاولات الحالية: ${report.currentLoginAttempts}/5"

            // تلوين النصوص حسب الحالة
            tvFailedLogins.setTextColor(
                if (report.failedLoginsLast24h > 0)
                    getColor(R.color.error_red)
                else
                    getColor(R.color.success_green)
            )

            tvSuspiciousActivities.setTextColor(
                if (report.suspiciousActivitiesLast24h > 0)
                    getColor(R.color.warning_yellow)
                else
                    getColor(R.color.success_green)
            )

            // حالة الحظر
            if (report.isAccountBlocked) {
                val blockedUntil = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault())
                    .format(java.util.Date(report.blockedUntil))
                tvAccountStatus.text = "الحساب محظور حتى: $blockedUntil"
                tvAccountStatus.setTextColor(getColor(R.color.error_red))
                btnUnblockAccount.isEnabled = true
            } else {
                tvAccountStatus.text = "الحساب نشط"
                tvAccountStatus.setTextColor(getColor(R.color.success_green))
                btnUnblockAccount.isEnabled = false
            }
        }
    }

    private fun checkAppIntegrity() {
        lifecycleScope.launch {
            try {
                binding.btnCheckIntegrity.isEnabled = false
                binding.btnCheckIntegrity.text = "جاري الفحص..."

                val integrityResult = com.kahrabaiat.amer.security.AppIntegrityChecker.checkAppIntegrity(this@SecuritySettingsActivity)
                showIntegrityResults(integrityResult)

            } catch (e: Exception) {
                showToast("خطأ في فحص سلامة التطبيق: ${e.message}")
            } finally {
                binding.btnCheckIntegrity.isEnabled = true
                binding.btnCheckIntegrity.text = "فحص سلامة التطبيق"
            }
        }
    }

    private fun showIntegrityResults(result: AppIntegrityChecker.IntegrityResult) {
        val message = buildString {
            appendLine("نتيجة فحص سلامة التطبيق:")
            appendLine("الحالة العامة: ${if (result.isSecure) "آمن" else "غير آمن"}")
            appendLine("مستوى الأمان: ${getSecurityLevelText(result.securityLevel)}")
            appendLine()
            appendLine("تفاصيل الفحص:")

            result.checks.forEach { check ->
                val status = if (check.passed) "✅" else "❌"
                appendLine("$status ${check.name}: ${check.message}")
            }
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("نتائج فحص الأمان")
            .setMessage(message)
            .setPositiveButton("موافق", null)
            .show()
    }

    private fun getSecurityLevelText(level: AppIntegrityChecker.SecurityLevel): String {
        return when (level) {
            AppIntegrityChecker.SecurityLevel.SECURE -> "آمن"
            AppIntegrityChecker.SecurityLevel.LOW_RISK -> "مخاطر منخفضة"
            AppIntegrityChecker.SecurityLevel.MEDIUM_RISK -> "مخاطر متوسطة"
            AppIntegrityChecker.SecurityLevel.HIGH_RISK -> "مخاطر عالية"
            AppIntegrityChecker.SecurityLevel.CRITICAL -> "مخاطر حرجة"
        }
    }

    private fun unblockAccount() {
        if (!requireOwnerPermission()) return

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("إلغاء حظر الحساب")
            .setMessage("هل أنت متأكد من إلغاء حظر الحساب؟")
            .setPositiveButton("نعم") { _, _ ->
                try {
                    val securityMonitor = SecurityMonitor.getInstance(this@SecuritySettingsActivity)
                    securityMonitor.unblockAccount()
                    showToast("تم إلغاء حظر الحساب بنجاح")
                    loadSecurityData()
                } catch (e: Exception) {
                    showToast("فشل في إلغاء حظر الحساب")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun clearSecurityLog() {
        if (!requireOwnerPermission()) return

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("مسح سجل الأمان")
            .setMessage("هل أنت متأكد من مسح جميع سجلات الأمان؟ هذا الإجراء لا يمكن التراجع عنه.")
            .setPositiveButton("نعم") { _, _ ->
                try {
                    val securityMonitor = SecurityMonitor.getInstance(this@SecuritySettingsActivity)
                    securityMonitor.clearSecurityLog()
                    showToast("تم مسح سجل الأمان بنجاح")
                    loadSecurityData()
                } catch (e: Exception) {
                    showToast("فشل في مسح سجل الأمان")
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun showChangePasswordDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_change_password, null)
        val etOldPassword = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etOldPassword)
        val etNewPassword = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etNewPassword)
        val etConfirmPassword = dialogView.findViewById<com.google.android.material.textfield.TextInputEditText>(R.id.etConfirmPassword)

        val dialog = androidx.appcompat.app.AlertDialog.Builder(this)
            .setView(dialogView)
            .setPositiveButton("🔐 تغيير كلمة المرور") { _, _ ->
                changePassword(etOldPassword, etNewPassword, etConfirmPassword)
            }
            .setNegativeButton("إلغاء", null)
            .create()

        // تخصيص الـ dialog
        dialog.show()

        // تخصيص أزرار الـ dialog
        dialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_POSITIVE)?.apply {
            setTextColor(getColor(R.color.primary_blue))
            textSize = 16f
            setTypeface(null, android.graphics.Typeface.BOLD)
        }

        dialog.getButton(androidx.appcompat.app.AlertDialog.BUTTON_NEGATIVE)?.apply {
            setTextColor(getColor(R.color.text_secondary))
            textSize = 16f
        }
    }

    private fun changePassword(
        etOldPassword: com.google.android.material.textfield.TextInputEditText,
        etNewPassword: com.google.android.material.textfield.TextInputEditText,
        etConfirmPassword: com.google.android.material.textfield.TextInputEditText
    ) {
        val oldPassword = etOldPassword.text.toString().trim()
        val newPassword = etNewPassword.text.toString().trim()
        val confirmPassword = etConfirmPassword.text.toString().trim()

        // التحقق من صحة البيانات
        if (oldPassword.isEmpty()) {
            showToast("يرجى إدخال كلمة المرور الحالية")
            return
        }

        if (newPassword.isEmpty()) {
            showToast("يرجى إدخال كلمة المرور الجديدة")
            return
        }

        if (confirmPassword.isEmpty()) {
            showToast("يرجى تأكيد كلمة المرور الجديدة")
            return
        }

        if (newPassword != confirmPassword) {
            showToast("كلمة المرور الجديدة غير متطابقة")
            return
        }

        if (newPassword.length < 6) {
            showToast("كلمة المرور يجب أن تكون 6 أحرف على الأقل")
            return
        }

        if (oldPassword == newPassword) {
            showToast("كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية")
            return
        }

        // تغيير كلمة المرور
        try {
            val authManager = com.kahrabaiat.amer.utils.AuthManager.getInstance(this@SecuritySettingsActivity)
            val username = authManager.getCurrentUserType().name.lowercase()

            if (authManager.changePassword(oldPassword, newPassword, username)) {
                showToast("✅ تم تغيير كلمة المرور بنجاح")

                // تحديث البيانات
                loadSecurityData()
            } else {
                showToast("❌ فشل في تغيير كلمة المرور - تحقق من كلمة المرور الحالية")
            }
        } catch (e: Exception) {
            showToast("خطأ في تغيير كلمة المرور: ${e.message}")
        }
    }

    private fun exportSecurityReport() {
        lifecycleScope.launch {
            try {
                val securityMonitor = SecurityMonitor.getInstance(this@SecuritySettingsActivity)
                val report = securityMonitor.getSecurityReport()
                val events = securityMonitor.getSecurityEvents()

                val reportText = buildString {
                    appendLine("=== تقرير الأمان الشامل ===")
                    appendLine("تاريخ التقرير: ${java.text.SimpleDateFormat("dd/MM/yyyy HH:mm", java.util.Locale.getDefault()).format(java.util.Date())}")
                    appendLine()
                    appendLine("الملخص:")
                    appendLine("- إجمالي الأحداث: ${report.totalEvents}")
                    appendLine("- أحداث آخر 24 ساعة: ${report.recentEvents}")
                    appendLine("- محاولات دخول فاشلة: ${report.failedLoginsLast24h}")
                    appendLine("- أنشطة مشبوهة: ${report.suspiciousActivitiesLast24h}")
                    appendLine("- حالة الحساب: ${if (report.isAccountBlocked) "محظور" else "نشط"}")
                    appendLine()
                    appendLine("=== سجل الأحداث ===")

                    events.forEach { event ->
                        val date = java.text.SimpleDateFormat("dd/MM/yyyy HH:mm:ss", java.util.Locale.getDefault())
                            .format(java.util.Date(event.timestamp))
                        appendLine("[$date] ${event.eventType.name}: ${event.description}")
                        if (event.metadata.isNotEmpty()) {
                            event.metadata.forEach { (key, value) ->
                                appendLine("  - $key: $value")
                            }
                        }
                        appendLine()
                    }
                }

                // حفظ التقرير في ملف أو مشاركته
                shareSecurityReport(reportText)

            } catch (e: Exception) {
                showToast("خطأ في تصدير التقرير: ${e.message}")
            }
        }
    }

    private fun shareSecurityReport(reportText: String) {
        val shareIntent = android.content.Intent().apply {
            action = android.content.Intent.ACTION_SEND
            type = "text/plain"
            putExtra(android.content.Intent.EXTRA_TEXT, reportText)
            putExtra(android.content.Intent.EXTRA_SUBJECT, "تقرير الأمان - كهربائيات عامر")
        }
        startActivity(android.content.Intent.createChooser(shareIntent, "مشاركة تقرير الأمان"))
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }





    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}
