package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.OrderSummaryAdapter
import com.kahrabaiat.amer.databinding.ActivityCheckoutBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import com.kahrabaiat.amer.utils.CartManager
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.utils.NotificationHelper
import com.kahrabaiat.amer.utils.PDFGenerator
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class CheckoutActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCheckoutBinding
    private lateinit var cartManager: CartManager
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var orderSummaryAdapter: OrderSummaryAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCheckoutBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadOrderSummary()
    }

    private fun initializeComponents() {
        cartManager = CartManager.getInstance(this)
        databaseHelper = DatabaseHelper.getInstance(this)
    }

    private fun setupToolbar() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
    }

    private fun setupRecyclerView() {
        orderSummaryAdapter = OrderSummaryAdapter()

        binding.rvOrderItems.apply {
            layoutManager = LinearLayoutManager(this@CheckoutActivity)
            adapter = orderSummaryAdapter
        }
    }

    private fun setupClickListeners() {
        binding.btnPlaceOrder.setOnClickListener {
            if (validateForm()) {
                placeOrder()
            }
        }
    }

    private fun loadOrderSummary() {
        val cartItems = cartManager.getCartItems()
        orderSummaryAdapter.submitList(cartItems)
        binding.tvTotalAmount.text = cartManager.getFormattedTotal()
    }

    private fun validateForm(): Boolean {
        val name = binding.etCustomerName.text.toString().trim()
        val phone = binding.etCustomerPhone.text.toString().trim()
        val address = binding.etCustomerAddress.text.toString().trim()

        when {
            name.isEmpty() -> {
                binding.etCustomerName.error = getString(R.string.field_required)
                binding.etCustomerName.requestFocus()
                return false
            }
            phone.isEmpty() -> {
                binding.etCustomerPhone.error = getString(R.string.field_required)
                binding.etCustomerPhone.requestFocus()
                return false
            }
            phone.length < 10 -> {
                binding.etCustomerPhone.error = getString(R.string.invalid_phone)
                binding.etCustomerPhone.requestFocus()
                return false
            }
            address.isEmpty() -> {
                binding.etCustomerAddress.error = getString(R.string.field_required)
                binding.etCustomerAddress.requestFocus()
                return false
            }
            else -> return true
        }
    }

    private fun placeOrder() {
        binding.btnPlaceOrder.isEnabled = false
        binding.btnPlaceOrder.text = getString(R.string.loading)

        lifecycleScope.launch {
            try {
                val order = createOrder()
                val orderId = databaseHelper.insertOrder(order)

                if (orderId > 0) {
                    val finalOrder = order.copy(id = orderId.toInt())

                    // Generate and send invoice
                    generateAndSendInvoice(finalOrder)

                    // Send notification to admin
                    sendAdminNotification(finalOrder)

                    // Send order details to admin
                    sendOrderToAdmin(finalOrder)

                    // Clear cart
                    cartManager.clearCart()

                    // Show success message and finish
                    showSuccessDialog(finalOrder)
                } else {
                    showError("فشل في إرسال الطلب. يرجى المحاولة مرة أخرى.")
                }
            } catch (e: Exception) {
                showError("حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.")
                android.util.Log.e("CheckoutActivity", "Error placing order", e)
            } finally {
                binding.btnPlaceOrder.isEnabled = true
                binding.btnPlaceOrder.text = getString(R.string.place_order)
            }
        }
    }

    private fun createOrder(): Order {
        val name = binding.etCustomerName.text.toString().trim()
        val phone = binding.etCustomerPhone.text.toString().trim()
        val address = binding.etCustomerAddress.text.toString().trim()
        val cartItems = cartManager.getCartItems()
        val total = cartManager.getTotalAmount()

        // Convert CartItems to OrderItems
        val orderItems = cartItems.map { cartItem ->
            Order.OrderItem(
                productId = cartItem.product.id,
                productName = cartItem.product.name,
                price = cartItem.product.price,
                quantity = cartItem.quantity
            )
        }

        return Order(
            id = 0, // Database will auto-generate
            customerName = name,
            customerPhone = phone,
            customerAddress = address,
            items = orderItems,
            total = total,
            status = "pending",
            orderDate = System.currentTimeMillis(),
            notes = ""
        )
    }

    private fun generateOrderNumber(): String {
        val year = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR)
        val timestamp = System.currentTimeMillis().toString().takeLast(6)
        return "$year-$timestamp"
    }

    private fun generateAndSendInvoice(order: Order) {
        try {
            val pdfGenerator = PDFGenerator(this)
            pdfGenerator.generateInvoice(order)
        } catch (e: Exception) {
            android.util.Log.e("CheckoutActivity", "Error generating invoice", e)
        }
    }

    private fun sendOrderToAdmin(order: Order) {
        try {
            val orderDetails = buildOrderMessage(order)

            val intent = Intent(Intent.ACTION_SEND).apply {
                type = "text/plain"
                putExtra(Intent.EXTRA_TEXT, orderDetails)
                putExtra(Intent.EXTRA_SUBJECT, "طلب جديد #${order.id} - كهربائيات عامر")
            }

            // إرسال الطلب عبر التطبيقات المتاحة
            startActivity(Intent.createChooser(intent, "إرسال الطلب للمدير"))

        } catch (e: Exception) {
            android.util.Log.e("CheckoutActivity", "Error sending order to admin", e)
        }
    }

    private fun buildOrderMessage(order: Order): String {
        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))

        return buildString {
            appendLine("🛒 طلب جديد من كهربائيات عامر")
            appendLine("═══════════════════════════════════════")
            appendLine()
            appendLine("📋 معلومات الطلب:")
            appendLine("• رقم الطلب: #${order.id}")
            appendLine("• التاريخ: ${dateFormat.format(Date(order.orderDate))}")
            appendLine("• الحالة: جديد (معلق)")
            appendLine()
            appendLine("👤 معلومات العميل:")
            appendLine("• الاسم: ${order.customerName}")
            appendLine("• الهاتف: ${order.customerPhone}")
            appendLine("• العنوان: ${order.customerAddress}")
            appendLine()
            appendLine("🛍️ تفاصيل المنتجات:")
            appendLine("───────────────────────────────────────")

            order.items.forEachIndexed { index, item ->
                appendLine("${index + 1}. ${item.productName}")
                appendLine("   الكمية: ${item.quantity}")
                appendLine("   السعر: ${numberFormat.format(item.price.toInt())} د.ع")
                appendLine("   المجموع: ${numberFormat.format(item.total.toInt())} د.ع")
                appendLine()
            }

            appendLine("───────────────────────────────────────")
            appendLine("💰 ملخص الطلب:")
            appendLine("• عدد المنتجات: ${order.items.size}")
            appendLine("• إجمالي الكمية: ${order.items.sumOf { it.quantity }}")
            appendLine("• المبلغ الإجمالي: ${numberFormat.format(order.total.toInt())} د.ع")
            appendLine()
            appendLine("📱 يرجى التواصل مع العميل لتأكيد الطلب")
            appendLine("═══════════════════════════════════════")
            appendLine("كهربائيات عامر - جودة وثقة")
        }
    }

    private fun sendAdminNotification(order: Order) {
        try {
            val notificationHelper = NotificationHelper(this)
            notificationHelper.sendNewOrderNotification(order)
        } catch (e: Exception) {
            // Log error but don't fail the order
        }
    }

    private fun showSuccessDialog(order: Order) {
        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))

        val message = buildString {
            appendLine("🎉 تم إرسال طلبك بنجاح!")
            appendLine()
            appendLine("📋 تفاصيل الطلب:")
            appendLine("• رقم الطلب: #${order.id}")
            appendLine("• التاريخ: ${dateFormat.format(Date(order.orderDate))}")
            appendLine("• المبلغ الإجمالي: ${numberFormat.format(order.total.toInt())} د.ع")
            appendLine()
            appendLine("📱 سيتم التواصل معك قريباً لتأكيد الطلب")
            appendLine()
            appendLine("شكراً لتسوقك معنا! 🛒")
        }

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("✅ تم إرسال الطلب")
            .setMessage(message)
            .setPositiveButton("موافق") { _, _ ->
                finish()
            }
            .setNeutralButton("📤 مشاركة الطلب") { _, _ ->
                shareOrderDetails(order)
                finish()
            }
            .setCancelable(false)
            .show()
    }

    private fun shareOrderDetails(order: Order) {
        val orderDetails = buildCustomerOrderMessage(order)

        val intent = Intent(Intent.ACTION_SEND).apply {
            type = "text/plain"
            putExtra(Intent.EXTRA_TEXT, orderDetails)
            putExtra(Intent.EXTRA_SUBJECT, "طلبي #${order.id} - كهربائيات عامر")
        }

        startActivity(Intent.createChooser(intent, "مشاركة تفاصيل الطلب"))
    }

    private fun buildCustomerOrderMessage(order: Order): String {
        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))

        return buildString {
            appendLine("🛒 طلبي من كهربائيات عامر")
            appendLine("═══════════════════════════════════════")
            appendLine()
            appendLine("📋 رقم الطلب: #${order.id}")
            appendLine("📅 التاريخ: ${dateFormat.format(Date(order.orderDate))}")
            appendLine()
            appendLine("🛍️ المنتجات المطلوبة:")
            appendLine("───────────────────────────────────────")

            order.items.forEachIndexed { index, item ->
                appendLine("${index + 1}. ${item.productName}")
                appendLine("   الكمية: ${item.quantity} - السعر: ${numberFormat.format(item.price.toInt())} د.ع")
                appendLine()
            }

            appendLine("───────────────────────────────────────")
            appendLine("💰 المبلغ الإجمالي: ${numberFormat.format(order.total.toInt())} د.ع")
            appendLine()
            appendLine("📱 معلومات التواصل:")
            appendLine("• الاسم: ${order.customerName}")
            appendLine("• الهاتف: ${order.customerPhone}")
            appendLine("• العنوان: ${order.customerAddress}")
            appendLine()
            appendLine("═══════════════════════════════════════")
            appendLine("كهربائيات عامر - جودة وثقة")
        }
    }

    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }
}
