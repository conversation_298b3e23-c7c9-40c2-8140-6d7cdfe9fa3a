package com.kahrabaiat.amer

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.EditOrderItemsAdapter
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.databinding.ActivityEditOrderBinding
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.utils.NotificationHelper
import kotlinx.coroutines.launch
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class EditOrderActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEditOrderBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var notificationHelper: NotificationHelper
    private lateinit var editOrderItemsAdapter: EditOrderItemsAdapter
    
    private var currentOrder: Order? = null
    private var orderId: Int = -1
    private var hasChanges = false

    companion object {
        const val EXTRA_ORDER_ID = "order_id"
        const val EXTRA_ORDER = "order"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEditOrderBinding.inflate(layoutInflater)
        setContentView(binding.root)

        initializeComponents()
        getOrderFromIntent()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadOrderDetails()
    }

    private fun initializeComponents() {
        databaseHelper = DatabaseHelper.getInstance(this)
        notificationHelper = NotificationHelper(this)
    }

    private fun getOrderFromIntent() {
        currentOrder = intent.getParcelableExtra(EXTRA_ORDER)
        if (currentOrder == null) {
            orderId = intent.getIntExtra(EXTRA_ORDER_ID, -1)
        } else {
            orderId = currentOrder!!.id
        }
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "تعديل الطلب #$orderId"
        }
    }

    private fun setupRecyclerView() {
        editOrderItemsAdapter = EditOrderItemsAdapter(
            onQuantityChanged = { item, newQuantity ->
                updateItemQuantity(item, newQuantity)
            },
            onRemoveItem = { item ->
                removeOrderItem(item)
            }
        )
        
        binding.rvOrderItems.apply {
            layoutManager = LinearLayoutManager(this@EditOrderActivity)
            adapter = editOrderItemsAdapter
        }
    }

    private fun setupClickListeners() {
        binding.btnSaveChanges.setOnClickListener {
            saveOrderChanges()
        }

        binding.btnAddProduct.setOnClickListener {
            addNewProduct()
        }

        binding.btnCancelEdit.setOnClickListener {
            cancelEdit()
        }
    }

    private fun loadOrderDetails() {
        if (currentOrder != null) {
            displayOrderDetails(currentOrder!!)
        } else if (orderId != -1) {
            lifecycleScope.launch {
                try {
                    binding.progressBar.visibility = View.VISIBLE
                    val order = databaseHelper.getAllOrders().find { it.id == orderId }
                    
                    if (order != null) {
                        currentOrder = order
                        displayOrderDetails(order)
                    } else {
                        showToast("لم يتم العثور على الطلب")
                        finish()
                    }
                } catch (e: Exception) {
                    showToast("خطأ في تحميل تفاصيل الطلب: ${e.message}")
                    finish()
                } finally {
                    binding.progressBar.visibility = View.GONE
                }
            }
        } else {
            showToast("معرف الطلب غير صحيح")
            finish()
        }
    }

    private fun displayOrderDetails(order: Order) {
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
        val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))

        with(binding) {
            // Order Header
            tvOrderNumber.text = "طلب #${order.id}"
            tvOrderDate.text = dateFormat.format(Date(order.orderDate))
            tvOrderStatus.text = getStatusDisplayName(order.status)

            // Customer Information (Editable)
            etCustomerName.setText(order.customerName)
            etCustomerPhone.setText(order.customerPhone)
            etCustomerAddress.setText(order.customerAddress)
            etOrderNotes.setText(order.notes)

            // Order Items
            editOrderItemsAdapter.submitList(order.items.toMutableList())

            // Update totals
            updateOrderTotals()

            // Enable/disable editing based on order status
            val canEdit = order.status == "pending" || order.status == "confirmed"
            enableEditing(canEdit)
        }
    }

    private fun enableEditing(canEdit: Boolean) {
        binding.apply {
            etCustomerName.isEnabled = canEdit
            etCustomerPhone.isEnabled = canEdit
            etCustomerAddress.isEnabled = canEdit
            etOrderNotes.isEnabled = canEdit
            btnAddProduct.isEnabled = canEdit
            btnSaveChanges.isEnabled = canEdit
            
            if (!canEdit) {
                tvEditWarning.visibility = View.VISIBLE
                tvEditWarning.text = "⚠️ لا يمكن تعديل الطلبات المشحونة أو المسلمة"
            } else {
                tvEditWarning.visibility = View.GONE
            }
        }
        
        editOrderItemsAdapter.setEditingEnabled(canEdit)
    }

    private fun updateItemQuantity(item: Order.OrderItem, newQuantity: Int) {
        if (newQuantity <= 0) {
            removeOrderItem(item)
            return
        }

        val updatedItems = editOrderItemsAdapter.currentList.toMutableList()
        val index = updatedItems.indexOfFirst { it.productId == item.productId }
        
        if (index != -1) {
            val updatedItem = item.copy(
                quantity = newQuantity,
                total = item.price * newQuantity
            )
            updatedItems[index] = updatedItem
            editOrderItemsAdapter.submitList(updatedItems)
            updateOrderTotals()
            hasChanges = true
        }
    }

    private fun removeOrderItem(item: Order.OrderItem) {
        AlertDialog.Builder(this)
            .setTitle("حذف المنتج")
            .setMessage("هل أنت متأكد من حذف \"${item.productName}\" من الطلب؟")
            .setPositiveButton("حذف") { _, _ ->
                val updatedItems = editOrderItemsAdapter.currentList.toMutableList()
                updatedItems.removeAll { it.productId == item.productId }
                
                if (updatedItems.isEmpty()) {
                    showToast("لا يمكن حذف جميع المنتجات من الطلب")
                    return@setPositiveButton
                }
                
                editOrderItemsAdapter.submitList(updatedItems)
                updateOrderTotals()
                hasChanges = true
                showToast("تم حذف المنتج من الطلب")
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun updateOrderTotals() {
        val items = editOrderItemsAdapter.currentList
        val total = items.sumOf { it.total }
        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
        
        binding.apply {
            tvItemsCount.text = "${items.size} منتج"
            tvTotalQuantity.text = "${items.sumOf { it.quantity }} قطعة"
            tvSubtotal.text = "${numberFormat.format(total.toInt())} د.ع"
            tvTotalAmount.text = "${numberFormat.format(total.toInt())} د.ع"
        }
    }

    private fun addNewProduct() {
        // يمكن إضافة شاشة اختيار المنتجات لاحقاً
        showToast("ميزة إضافة منتجات جديدة قيد التطوير")
    }

    private fun saveOrderChanges() {
        if (!validateForm()) return

        val currentOrder = this.currentOrder ?: return
        val items = editOrderItemsAdapter.currentList

        if (items.isEmpty()) {
            showToast("لا يمكن حفظ طلب فارغ")
            return
        }

        lifecycleScope.launch {
            try {
                binding.btnSaveChanges.isEnabled = false
                binding.btnSaveChanges.text = "جاري الحفظ..."

                val updatedOrder = currentOrder.copy(
                    customerName = binding.etCustomerName.text.toString().trim(),
                    customerPhone = binding.etCustomerPhone.text.toString().trim(),
                    customerAddress = binding.etCustomerAddress.text.toString().trim(),
                    notes = binding.etOrderNotes.text.toString().trim(),
                    items = items,
                    total = items.sumOf { it.total }
                )

                val success = databaseHelper.updateOrderDetails(updatedOrder)

                if (success) {
                    <EMAIL> = updatedOrder
                    hasChanges = false
                    showToast("✅ تم حفظ التغييرات بنجاح")
                    
                    // Send notification about order update
                    try {
                        notificationHelper.sendNewOrderNotification(updatedOrder)
                    } catch (e: Exception) {
                        // Log error but don't fail the update
                    }
                    
                    setResult(RESULT_OK)
                    finish()
                } else {
                    showToast("❌ فشل في حفظ التغييرات")
                }

            } catch (e: Exception) {
                showToast("خطأ في حفظ التغييرات: ${e.message}")
            } finally {
                binding.btnSaveChanges.isEnabled = true
                binding.btnSaveChanges.text = "💾 حفظ التغييرات"
            }
        }
    }

    private fun validateForm(): Boolean {
        val name = binding.etCustomerName.text.toString().trim()
        val phone = binding.etCustomerPhone.text.toString().trim()
        val address = binding.etCustomerAddress.text.toString().trim()

        when {
            name.isEmpty() -> {
                binding.etCustomerName.error = "اسم العميل مطلوب"
                binding.etCustomerName.requestFocus()
                return false
            }
            phone.isEmpty() -> {
                binding.etCustomerPhone.error = "رقم الهاتف مطلوب"
                binding.etCustomerPhone.requestFocus()
                return false
            }
            phone.length < 10 -> {
                binding.etCustomerPhone.error = "رقم هاتف غير صحيح"
                binding.etCustomerPhone.requestFocus()
                return false
            }
            address.isEmpty() -> {
                binding.etCustomerAddress.error = "العنوان مطلوب"
                binding.etCustomerAddress.requestFocus()
                return false
            }
            else -> return true
        }
    }

    private fun cancelEdit() {
        if (hasChanges) {
            AlertDialog.Builder(this)
                .setTitle("إلغاء التعديل")
                .setMessage("هل أنت متأكد من إلغاء التعديل؟ سيتم فقدان جميع التغييرات غير المحفوظة.")
                .setPositiveButton("نعم، إلغاء") { _, _ ->
                    finish()
                }
                .setNegativeButton("لا، متابعة التعديل", null)
                .show()
        } else {
            finish()
        }
    }

    private fun getStatusDisplayName(status: String): String {
        return when (status.lowercase()) {
            "pending" -> "معلق"
            "confirmed" -> "مؤكد"
            "processing" -> "قيد المعالجة"
            "shipped" -> "مشحون"
            "delivered" -> "مسلم"
            "cancelled" -> "ملغي"
            else -> "غير محدد"
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.edit_order_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                cancelEdit()
                true
            }
            R.id.action_save -> {
                saveOrderChanges()
                true
            }
            R.id.action_cancel -> {
                cancelEdit()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onBackPressed() {
        cancelEdit()
    }
}
