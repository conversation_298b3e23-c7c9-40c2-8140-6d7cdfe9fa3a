# 🔍 نظام الفحص الشامل لتطبيق كهربائيات عامر
# يجب تشغيل هذا السكريبت قبل كل تعديل أو إضافة كود

Write-Host "🔍 بدء الفحص الشامل للتطبيق..." -ForegroundColor Green

# 1. فحص بنية المشروع
Write-Host "`n📁 فحص بنية المشروع..." -ForegroundColor Yellow
$requiredFiles = @(
    "app/src/main/AndroidManifest.xml",
    "app/src/main/java/com/kahrabaiat/amer/MainActivity.kt",
    "app/src/main/res/layout/activity_main.xml",
    "app/src/main/res/values/strings.xml",
    "app/src/main/res/values/colors.xml",
    "app/src/main/res/values/themes.xml",
    "app/build.gradle",
    "build.gradle"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file موجود" -ForegroundColor Green
    } else {
        Write-Host "❌ $file مفقود!" -ForegroundColor Red
        exit 1
    }
}

# 2. فحص الموارد المطلوبة
Write-Host "`n🎨 فحص الموارد المطلوبة..." -ForegroundColor Yellow
$requiredDrawables = @(
    "app/src/main/res/drawable/ic_shopping_cart.xml",
    "app/src/main/res/drawable/ic_admin.xml",
    "app/src/main/res/drawable/gradient_background.xml"
)

foreach ($drawable in $requiredDrawables) {
    if (Test-Path $drawable) {
        Write-Host "✅ $drawable موجود" -ForegroundColor Green
    } else {
        Write-Host "⚠️ $drawable مفقود - سيتم إنشاؤه" -ForegroundColor Yellow
    }
}

# 3. تنظيف المشروع
Write-Host "`n🧹 تنظيف المشروع..." -ForegroundColor Yellow
& .\gradlew.bat clean
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تنظيف المشروع!" -ForegroundColor Red
    exit 1
}

# 4. بناء المشروع
Write-Host "`n🔨 بناء المشروع..." -ForegroundColor Yellow
& .\gradlew.bat assembleDebug
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في بناء المشروع!" -ForegroundColor Red
    exit 1
}

# 5. تثبيت التطبيق
Write-Host "`n📱 تثبيت التطبيق..." -ForegroundColor Yellow
& .\gradlew.bat installDebug
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ فشل في تثبيت التطبيق!" -ForegroundColor Red
    exit 1
}

# 6. فحص تشغيل التطبيق
Write-Host "`n🚀 فحص تشغيل التطبيق..." -ForegroundColor Yellow
Write-Host "يرجى فتح التطبيق على الهاتف والتأكد من عمله..." -ForegroundColor Cyan
Write-Host "اضغط Enter بعد التأكد من عمل التطبيق، أو Ctrl+C إذا كان هناك مشكلة" -ForegroundColor Cyan
Read-Host

Write-Host "`n🎉 تم الفحص الشامل بنجاح!" -ForegroundColor Green
Write-Host "التطبيق جاهز للاستخدام!" -ForegroundColor Green
