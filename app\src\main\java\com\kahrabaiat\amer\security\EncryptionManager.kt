package com.kahrabaiat.amer.security

import android.content.Context
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties
import android.util.Base64
import android.util.Log
import java.security.KeyStore
import javax.crypto.Cipher
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.GCMParameterSpec

class EncryptionManager private constructor(private val context: Context) {
    
    companion object {
        private const val TAG = "EncryptionManager"
        private const val ANDROID_KEYSTORE = "AndroidKeyStore"
        private const val KEY_ALIAS = "KahrabaiatAmerSecretKey"
        private const val TRANSFORMATION = "AES/GCM/NoPadding"
        private const val GCM_IV_LENGTH = 12
        private const val GCM_TAG_LENGTH = 16
        
        @Volatile
        private var INSTANCE: EncryptionManager? = null
        
        fun getInstance(context: Context): EncryptionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: EncryptionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val keyStore: KeyStore by lazy {
        KeyStore.getInstance(ANDROID_KEYSTORE).apply {
            load(null)
        }
    }
    
    init {
        generateSecretKey()
    }
    
    /**
     * إنشاء مفتاح تشفير آمن
     */
    private fun generateSecretKey() {
        try {
            if (!keyStore.containsAlias(KEY_ALIAS)) {
                val keyGenerator = KeyGenerator.getInstance(KeyProperties.KEY_ALGORITHM_AES, ANDROID_KEYSTORE)
                
                val keyGenParameterSpec = KeyGenParameterSpec.Builder(
                    KEY_ALIAS,
                    KeyProperties.PURPOSE_ENCRYPT or KeyProperties.PURPOSE_DECRYPT
                )
                    .setBlockModes(KeyProperties.BLOCK_MODE_GCM)
                    .setEncryptionPaddings(KeyProperties.ENCRYPTION_PADDING_NONE)
                    .setUserAuthenticationRequired(false)
                    .setRandomizedEncryptionRequired(true)
                    .build()
                
                keyGenerator.init(keyGenParameterSpec)
                keyGenerator.generateKey()
                
                Log.d(TAG, "Secret key generated successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error generating secret key", e)
        }
    }
    
    /**
     * تشفير النص
     */
    fun encrypt(plainText: String): String? {
        try {
            val secretKey = keyStore.getKey(KEY_ALIAS, null) as SecretKey
            val cipher = Cipher.getInstance(TRANSFORMATION)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey)
            
            val iv = cipher.iv
            val encryptedData = cipher.doFinal(plainText.toByteArray(Charsets.UTF_8))
            
            // دمج IV مع البيانات المشفرة
            val encryptedWithIv = ByteArray(GCM_IV_LENGTH + encryptedData.size)
            System.arraycopy(iv, 0, encryptedWithIv, 0, GCM_IV_LENGTH)
            System.arraycopy(encryptedData, 0, encryptedWithIv, GCM_IV_LENGTH, encryptedData.size)
            
            return Base64.encodeToString(encryptedWithIv, Base64.DEFAULT)
        } catch (e: Exception) {
            Log.e(TAG, "Error encrypting data", e)
            return null
        }
    }
    
    /**
     * فك تشفير النص
     */
    fun decrypt(encryptedText: String): String? {
        try {
            val encryptedWithIv = Base64.decode(encryptedText, Base64.DEFAULT)
            
            // استخراج IV والبيانات المشفرة
            val iv = ByteArray(GCM_IV_LENGTH)
            val encryptedData = ByteArray(encryptedWithIv.size - GCM_IV_LENGTH)
            
            System.arraycopy(encryptedWithIv, 0, iv, 0, GCM_IV_LENGTH)
            System.arraycopy(encryptedWithIv, GCM_IV_LENGTH, encryptedData, 0, encryptedData.size)
            
            val secretKey = keyStore.getKey(KEY_ALIAS, null) as SecretKey
            val cipher = Cipher.getInstance(TRANSFORMATION)
            val spec = GCMParameterSpec(GCM_TAG_LENGTH * 8, iv)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, spec)
            
            val decryptedData = cipher.doFinal(encryptedData)
            return String(decryptedData, Charsets.UTF_8)
        } catch (e: Exception) {
            Log.e(TAG, "Error decrypting data", e)
            return null
        }
    }
    
    /**
     * تشفير كلمة المرور
     */
    fun encryptPassword(password: String): String? {
        return encrypt(password)
    }
    
    /**
     * فك تشفير كلمة المرور
     */
    fun decryptPassword(encryptedPassword: String): String? {
        return decrypt(encryptedPassword)
    }
    
    /**
     * إنشاء hash آمن للكلمات السرية
     */
    fun hashPassword(password: String, salt: String = generateSalt()): String {
        return try {
            val messageDigest = java.security.MessageDigest.getInstance("SHA-256")
            val saltedPassword = "$password$salt"
            val hashBytes = messageDigest.digest(saltedPassword.toByteArray(Charsets.UTF_8))
            val hash = Base64.encodeToString(hashBytes, Base64.NO_WRAP)
            "$salt:$hash" // حفظ الـ salt مع الـ hash
        } catch (e: Exception) {
            Log.e(TAG, "Error hashing password", e)
            password // في حالة الخطأ، إرجاع كلمة المرور كما هي
        }
    }
    
    /**
     * التحقق من كلمة المرور
     */
    fun verifyPassword(password: String, hashedPassword: String): Boolean {
        return try {
            val parts = hashedPassword.split(":")
            if (parts.size != 2) return false
            
            val salt = parts[0]
            val hash = parts[1]
            
            val messageDigest = java.security.MessageDigest.getInstance("SHA-256")
            val saltedPassword = "$password$salt"
            val hashBytes = messageDigest.digest(saltedPassword.toByteArray(Charsets.UTF_8))
            val newHash = Base64.encodeToString(hashBytes, Base64.NO_WRAP)
            
            hash == newHash
        } catch (e: Exception) {
            Log.e(TAG, "Error verifying password", e)
            false
        }
    }
    
    /**
     * إنشاء salt عشوائي
     */
    private fun generateSalt(): String {
        val random = java.security.SecureRandom()
        val salt = ByteArray(16)
        random.nextBytes(salt)
        return Base64.encodeToString(salt, Base64.NO_WRAP)
    }
    
    /**
     * تشفير البيانات الحساسة
     */
    fun encryptSensitiveData(data: Map<String, String>): String? {
        return try {
            val jsonString = com.google.gson.Gson().toJson(data)
            encrypt(jsonString)
        } catch (e: Exception) {
            Log.e(TAG, "Error encrypting sensitive data", e)
            null
        }
    }
    
    /**
     * فك تشفير البيانات الحساسة
     */
    fun decryptSensitiveData(encryptedData: String): Map<String, String>? {
        return try {
            val jsonString = decrypt(encryptedData)
            jsonString?.let {
                com.google.gson.Gson().fromJson(it, Map::class.java) as Map<String, String>
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error decrypting sensitive data", e)
            null
        }
    }
    
    /**
     * مسح المفتاح (للطوارئ فقط)
     */
    fun clearKey() {
        try {
            keyStore.deleteEntry(KEY_ALIAS)
            Log.d(TAG, "Encryption key cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Error clearing key", e)
        }
    }
}
