package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.UsersAdapter
import com.kahrabaiat.amer.databinding.ActivityUserManagementBinding
import com.kahrabaiat.amer.models.User
import com.kahrabaiat.amer.models.UserRole
import com.kahrabaiat.amer.utils.UserManager
import kotlinx.coroutines.launch

class UserManagementActivity : AppCompatActivity() {

    private lateinit var binding: ActivityUserManagementBinding
    private lateinit var userManager: UserManager
    private lateinit var usersAdapter: UsersAdapter
    private var usersList = mutableListOf<User>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityUserManagementBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            setupToolbar()
            setupRecyclerView()
            setupClickListeners()
            loadUsers()
        } catch (e: Exception) {
            android.util.Log.e("UserManagementActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة إدارة المستخدمين: ${e.message}")
            finish()
        }
    }

    private fun initializeComponents() {
        userManager = UserManager(this)
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = "إدارة المستخدمين"
            }
        } catch (e: Exception) {
            android.util.Log.e("UserManagementActivity", "Error setting up toolbar", e)
            binding.toolbar.visibility = View.GONE
        }
    }

    private fun setupRecyclerView() {
        usersAdapter = UsersAdapter(
            users = usersList,
            onEditClick = { user -> editUser(user) },
            onDeleteClick = { user -> confirmDeleteUser(user) },
            onToggleStatusClick = { user -> toggleUserStatus(user) }
        )

        binding.recyclerViewUsers.apply {
            layoutManager = LinearLayoutManager(this@UserManagementActivity)
            adapter = usersAdapter
        }
    }

    private fun setupClickListeners() {
        binding.fabAddUser.setOnClickListener {
            addNewUser()
        }

        binding.swipeRefresh.setOnRefreshListener {
            loadUsers()
        }

        // Role filter chips
        binding.chipAllUsers.setOnClickListener {
            filterUsers(null)
        }

        binding.chipAdmins.setOnClickListener {
            filterUsers(UserRole.ADMIN)
        }

        binding.chipManagers.setOnClickListener {
            filterUsers(UserRole.MANAGER)
        }

        binding.chipEmployees.setOnClickListener {
            filterUsers(UserRole.EMPLOYEE)
        }
    }

    private fun loadUsers() {
        lifecycleScope.launch {
            try {
                binding.swipeRefresh.isRefreshing = true
                binding.progressBar.visibility = View.VISIBLE

                val users = userManager.getAllUsers()
                usersList.clear()
                usersList.addAll(users)

                usersAdapter.notifyDataSetChanged()
                updateEmptyState()

            } catch (e: Exception) {
                android.util.Log.e("UserManagementActivity", "Error loading users", e)
                showToast("خطأ في تحميل المستخدمين: ${e.message}")
            } finally {
                binding.swipeRefresh.isRefreshing = false
                binding.progressBar.visibility = View.GONE
            }
        }
    }

    private fun filterUsers(role: UserRole?) {
        lifecycleScope.launch {
            val filteredUsers = if (role == null) {
                userManager.getAllUsers()
            } else {
                userManager.getUsersByRole(role)
            }

            usersList.clear()
            usersList.addAll(filteredUsers)
            usersAdapter.notifyDataSetChanged()
            updateEmptyState()

            // Update chip selection
            binding.chipAllUsers.isChecked = role == null
            binding.chipAdmins.isChecked = role == UserRole.ADMIN
            binding.chipManagers.isChecked = role == UserRole.MANAGER
            binding.chipEmployees.isChecked = role == UserRole.EMPLOYEE
        }
    }

    private fun addNewUser() {
        showToast("إضافة مستخدم جديد قيد التطوير")
    }

    private fun editUser(user: User) {
        showToast("تعديل المستخدم قيد التطوير")
    }

    private fun confirmDeleteUser(user: User) {
        AlertDialog.Builder(this)
            .setTitle("حذف المستخدم")
            .setMessage("هل أنت متأكد من حذف المستخدم ${user.name}؟")
            .setPositiveButton("حذف") { _, _ ->
                deleteUser(user)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun deleteUser(user: User) {
        lifecycleScope.launch {
            try {
                val success = userManager.deleteUser(user.id)
                if (success) {
                    showToast("تم حذف المستخدم بنجاح")
                    loadUsers()
                } else {
                    showToast("فشل في حذف المستخدم")
                }
            } catch (e: Exception) {
                showToast("خطأ في حذف المستخدم: ${e.message}")
            }
        }
    }

    private fun toggleUserStatus(user: User) {
        lifecycleScope.launch {
            try {
                val updatedUser = user.copy(isActive = !user.isActive)
                val success = userManager.updateUser(updatedUser)
                if (success) {
                    val status = if (updatedUser.isActive) "تفعيل" else "إلغاء تفعيل"
                    showToast("تم $status المستخدم بنجاح")
                    loadUsers()
                } else {
                    showToast("فشل في تحديث حالة المستخدم")
                }
            } catch (e: Exception) {
                showToast("خطأ في تحديث حالة المستخدم: ${e.message}")
            }
        }
    }

    private fun updateEmptyState() {
        if (usersList.isEmpty()) {
            binding.recyclerViewUsers.visibility = View.GONE
            binding.tvEmptyState.visibility = View.VISIBLE
        } else {
            binding.recyclerViewUsers.visibility = View.VISIBLE
            binding.tvEmptyState.visibility = View.GONE
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK) {
            when (requestCode) {
                REQUEST_ADD_USER, REQUEST_EDIT_USER -> {
                    loadUsers()
                }
            }
        }
    }

    private fun showToast(message: String) {
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    companion object {
        private const val REQUEST_ADD_USER = 1001
        private const val REQUEST_EDIT_USER = 1002
    }
}
