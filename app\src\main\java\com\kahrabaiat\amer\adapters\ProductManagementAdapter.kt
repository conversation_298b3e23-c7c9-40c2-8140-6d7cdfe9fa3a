package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemProductManagementBinding
import com.kahrabaiat.amer.models.CategoryConstants
import com.kahrabaiat.amer.models.Product
import java.text.NumberFormat
import java.util.*

class ProductManagementAdapter(
    private val onEditClick: (Product) -> Unit,
    private val onDeleteClick: (Product) -> Unit,
    private val onToggleAvailability: (Product) -> Unit
) : RecyclerView.Adapter<ProductManagementAdapter.ProductViewHolder>() {

    private var products = mutableListOf<Product>()

    fun updateProducts(newProducts: List<Product>) {
        products.clear()
        products.addAll(newProducts)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val binding = ItemProductManagementBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ProductViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        holder.bind(products[position])
    }

    override fun getItemCount(): Int = products.size

    inner class ProductViewHolder(
        private val binding: ItemProductManagementBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(product: Product) {
            with(binding) {
                // Product basic info
                tvProductName.text = product.name
                tvCategory.text = getCategoryDisplayName(product.category)
                
                // Price formatting
                val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
                tvPrice.text = "${numberFormat.format(product.price.toInt())} د.ع"
                
                // Stock info
                tvStock.text = "المخزون: ${product.stock}"
                
                // Availability status
                updateAvailabilityStatus(product.available)
                
                // Discount badge
                if (product.discount > 0) {
                    tvDiscount.visibility = View.VISIBLE
                    tvDiscount.text = "خصم ${product.discount}%"
                } else {
                    tvDiscount.visibility = View.GONE
                }
                
                // Product image
                loadProductImage(product.imageUrl)
                
                // Click listeners
                btnEdit.setOnClickListener { onEditClick(product) }
                btnDelete.setOnClickListener { onDeleteClick(product) }
                btnToggleAvailability.setOnClickListener {
                    // إضافة تأثير بصري
                    it.isEnabled = false
                    onToggleAvailability(product)
                    // إعادة تفعيل الزر بعد فترة قصيرة
                    it.postDelayed({ it.isEnabled = true }, 1000)
                }
                
                // Update toggle button icon
                updateToggleButton(product.available)
            }
        }

        private fun updateAvailabilityStatus(isAvailable: Boolean) {
            with(binding.tvStatus) {
                if (isAvailable) {
                    text = "متاح"
                    setBackgroundResource(R.drawable.bg_status_available)
                    setTextColor(ContextCompat.getColor(context, R.color.success_green))
                } else {
                    text = "غير متاح"
                    setBackgroundResource(R.drawable.bg_status_unavailable)
                    setTextColor(ContextCompat.getColor(context, R.color.error_red))
                }
            }
        }

        private fun updateToggleButton(isAvailable: Boolean) {
            with(binding.btnToggleAvailability) {
                if (isAvailable) {
                    setIconResource(R.drawable.ic_visibility_off)
                    contentDescription = "إخفاء المنتج"
                } else {
                    setIconResource(R.drawable.ic_visibility)
                    contentDescription = "إظهار المنتج"
                }
            }
        }

        private fun loadProductImage(imageUrl: String) {
            if (imageUrl.isNotEmpty()) {
                Glide.with(binding.ivProductImage.context)
                    .load(imageUrl)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_placeholder)
                    .centerCrop()
                    .into(binding.ivProductImage)
            } else {
                binding.ivProductImage.setImageResource(R.drawable.ic_image_placeholder)
            }
        }

        private fun getCategoryDisplayName(category: String): String {
            return when (category) {
                CategoryConstants.HOME_APPLIANCES -> "الأجهزة المنزلية"
                CategoryConstants.ELECTRICAL_APPLIANCES -> "الأجهزة الكهربائية"
                CategoryConstants.HAND_TOOLS -> "العدد اليدوية"
                else -> category
            }
        }
    }
}
