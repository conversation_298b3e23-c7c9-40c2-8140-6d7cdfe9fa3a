package com.kahrabaiat.amer

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.kahrabaiat.amer.databinding.ActivityExportBinding
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.utils.ExportHelper
import kotlinx.coroutines.launch
import java.io.File

class ExportActivity : AppCompatActivity() {

    private lateinit var binding: ActivityExportBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var exportHelper: ExportHelper
    private var isExporting = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityExportBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupToolbar()
        initializeComponents()
        setupClickListeners()
        updateUI()
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = "تصدير البيانات"
        }
    }

    private fun initializeComponents() {
        databaseHelper = DatabaseHelper.getInstance(this)
        exportHelper = ExportHelper(this)
    }

    private fun setupClickListeners() {
        // تصدير المنتجات
        binding.btnExportProducts.setOnClickListener {
            if (!isExporting) {
                exportProducts()
            }
        }

        // تصدير الطلبات
        binding.btnExportOrders.setOnClickListener {
            if (!isExporting) {
                exportOrders()
            }
        }

        // تصدير العملاء
        binding.btnExportCustomers.setOnClickListener {
            if (!isExporting) {
                exportCustomers()
            }
        }

        // تصدير الإحصائيات
        binding.btnExportStatistics.setOnClickListener {
            if (!isExporting) {
                exportStatistics()
            }
        }

        // تصدير شامل
        binding.btnExportAll.setOnClickListener {
            if (!isExporting) {
                exportAllData()
            }
        }

        // تصدير مخصص
        binding.btnCustomExport.setOnClickListener {
            if (!isExporting) {
                showCustomExportDialog()
            }
        }
    }

    private fun updateUI() {
        lifecycleScope.launch {
            try {
                val productCount = databaseHelper.getProductCount()
                val orderCount = databaseHelper.getOrderCount()
                val customerCount = databaseHelper.getAllOrders()
                    .map { it.customerName }
                    .distinct()
                    .size

                binding.tvProductCount.text = "($productCount منتج)"
                binding.tvOrderCount.text = "($orderCount طلب)"
                binding.tvCustomerCount.text = "($customerCount عميل)"
                binding.tvStatisticsInfo.text = "(تقارير شاملة)"

            } catch (e: Exception) {
                showToast("خطأ في تحميل البيانات: ${e.message}")
            }
        }
    }

    private fun exportProducts() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير المنتجات...")

                val products = databaseHelper.getAllProducts()
                if (products.isEmpty()) {
                    showToast("لا توجد منتجات للتصدير")
                    return@launch
                }

                val result = exportHelper.exportProductsToCSV(products)
                handleExportResult(result, "المنتجات")

            } catch (e: Exception) {
                showToast("خطأ في تصدير المنتجات: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportOrders() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير الطلبات...")

                val orders = databaseHelper.getAllOrders()
                if (orders.isEmpty()) {
                    showToast("لا توجد طلبات للتصدير")
                    return@launch
                }

                val result = exportHelper.exportOrdersToCSV(orders)
                handleExportResult(result, "الطلبات")

            } catch (e: Exception) {
                showToast("خطأ في تصدير الطلبات: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportCustomers() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير بيانات العملاء...")

                val orders = databaseHelper.getAllOrders()
                val customers = orders.groupBy { it.customerName }
                    .map { (name, customerOrders) ->
                        mapOf(
                            "اسم العميل" to name,
                            "رقم الهاتف" to (customerOrders.firstOrNull()?.customerPhone ?: ""),
                            "العنوان" to (customerOrders.firstOrNull()?.customerAddress ?: ""),
                            "عدد الطلبات" to customerOrders.size.toString(),
                            "إجمالي المشتريات" to customerOrders.sumOf { it.total }.toString(),
                            "تاريخ آخر طلب" to java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale("ar"))
                                .format(java.util.Date(customerOrders.maxOf { it.orderDate }))
                        )
                    }

                if (customers.isEmpty()) {
                    showToast("لا توجد بيانات عملاء للتصدير")
                    return@launch
                }

                val result = exportHelper.exportCustomersToCSV(customers)
                handleExportResult(result, "العملاء")

            } catch (e: Exception) {
                showToast("خطأ في تصدير بيانات العملاء: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportStatistics() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير الإحصائيات...")

                val products = databaseHelper.getAllProducts()
                val orders = databaseHelper.getAllOrders()
                
                val statistics = mutableListOf<Map<String, String>>()
                
                // إحصائيات عامة
                statistics.add(mapOf(
                    "النوع" to "إحصائيات عامة",
                    "المؤشر" to "إجمالي المنتجات",
                    "القيمة" to products.size.toString(),
                    "التاريخ" to java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale("ar")).format(java.util.Date())
                ))
                
                statistics.add(mapOf(
                    "النوع" to "إحصائيات عامة",
                    "المؤشر" to "إجمالي الطلبات",
                    "القيمة" to orders.size.toString(),
                    "التاريخ" to java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale("ar")).format(java.util.Date())
                ))
                
                statistics.add(mapOf(
                    "النوع" to "إحصائيات عامة",
                    "المؤشر" to "إجمالي المبيعات",
                    "القيمة" to "${orders.sumOf { it.total }} د.ع",
                    "التاريخ" to java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale("ar")).format(java.util.Date())
                ))

                // إحصائيات الطلبات حسب الحالة
                val ordersByStatus = orders.groupBy { it.status }
                ordersByStatus.forEach { (status, statusOrders) ->
                    statistics.add(mapOf(
                        "النوع" to "إحصائيات الطلبات",
                        "المؤشر" to "طلبات $status",
                        "القيمة" to statusOrders.size.toString(),
                        "التاريخ" to java.text.SimpleDateFormat("dd/MM/yyyy", java.util.Locale("ar")).format(java.util.Date())
                    ))
                }

                val result = exportHelper.exportStatisticsToCSV(statistics)
                handleExportResult(result, "الإحصائيات")

            } catch (e: Exception) {
                showToast("خطأ في تصدير الإحصائيات: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportAllData() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير جميع البيانات...")

                val products = databaseHelper.getAllProducts()
                val orders = databaseHelper.getAllOrders()

                if (products.isEmpty() && orders.isEmpty()) {
                    showToast("لا توجد بيانات للتصدير")
                    return@launch
                }

                val results = mutableListOf<Result<File>>()

                // تصدير المنتجات
                if (products.isNotEmpty()) {
                    results.add(exportHelper.exportProductsToCSV(products))
                }

                // تصدير الطلبات
                if (orders.isNotEmpty()) {
                    results.add(exportHelper.exportOrdersToCSV(orders))
                }

                val successCount = results.count { it.isSuccess }
                val totalCount = results.size

                if (successCount == totalCount) {
                    showToast("تم تصدير جميع البيانات بنجاح ($successCount ملف)")
                } else {
                    showToast("تم تصدير $successCount من $totalCount ملف")
                }

            } catch (e: Exception) {
                showToast("خطأ في تصدير البيانات: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun showCustomExportDialog() {
        val options = arrayOf(
            "تصدير طلبات اليوم",
            "تصدير طلبات الأسبوع",
            "تصدير طلبات الشهر",
            "تصدير المنتجات منخفضة المخزون",
            "تصدير أفضل العملاء"
        )

        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("تصدير مخصص")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> exportTodayOrders()
                    1 -> exportWeekOrders()
                    2 -> exportMonthOrders()
                    3 -> exportLowStockProducts()
                    4 -> exportTopCustomers()
                }
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun exportTodayOrders() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير طلبات اليوم...")

                val today = System.currentTimeMillis()
                val startOfDay = today - (today % (24 * 60 * 60 * 1000))
                
                val todayOrders = databaseHelper.getAllOrders()
                    .filter { it.orderDate >= startOfDay }

                if (todayOrders.isEmpty()) {
                    showToast("لا توجد طلبات اليوم للتصدير")
                    return@launch
                }

                val result = exportHelper.exportOrdersToCSV(todayOrders, "طلبات_اليوم")
                handleExportResult(result, "طلبات اليوم")

            } catch (e: Exception) {
                showToast("خطأ في تصدير طلبات اليوم: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportWeekOrders() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير طلبات الأسبوع...")

                val weekAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000)
                val weekOrders = databaseHelper.getAllOrders()
                    .filter { it.orderDate >= weekAgo }

                if (weekOrders.isEmpty()) {
                    showToast("لا توجد طلبات هذا الأسبوع للتصدير")
                    return@launch
                }

                val result = exportHelper.exportOrdersToCSV(weekOrders, "طلبات_الأسبوع")
                handleExportResult(result, "طلبات الأسبوع")

            } catch (e: Exception) {
                showToast("خطأ في تصدير طلبات الأسبوع: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportMonthOrders() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير طلبات الشهر...")

                val monthAgo = System.currentTimeMillis() - (30 * 24 * 60 * 60 * 1000)
                val monthOrders = databaseHelper.getAllOrders()
                    .filter { it.orderDate >= monthAgo }

                if (monthOrders.isEmpty()) {
                    showToast("لا توجد طلبات هذا الشهر للتصدير")
                    return@launch
                }

                val result = exportHelper.exportOrdersToCSV(monthOrders, "طلبات_الشهر")
                handleExportResult(result, "طلبات الشهر")

            } catch (e: Exception) {
                showToast("خطأ في تصدير طلبات الشهر: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportLowStockProducts() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير المنتجات منخفضة المخزون...")

                val lowStockProducts = databaseHelper.getAllProducts()
                    .filter { it.stock <= 5 }

                if (lowStockProducts.isEmpty()) {
                    showToast("لا توجد منتجات منخفضة المخزون")
                    return@launch
                }

                val result = exportHelper.exportProductsToCSV(lowStockProducts, "منتجات_منخفضة_المخزون")
                handleExportResult(result, "المنتجات منخفضة المخزون")

            } catch (e: Exception) {
                showToast("خطأ في تصدير المنتجات منخفضة المخزون: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun exportTopCustomers() {
        lifecycleScope.launch {
            try {
                setExportingState(true, "جاري تصدير أفضل العملاء...")

                val orders = databaseHelper.getAllOrders()
                val topCustomers = orders.groupBy { it.customerName }
                    .map { (name, customerOrders) ->
                        mapOf(
                            "اسم العميل" to name,
                            "عدد الطلبات" to customerOrders.size.toString(),
                            "إجمالي المشتريات" to customerOrders.sumOf { it.total }.toString(),
                            "متوسط قيمة الطلب" to (customerOrders.sumOf { it.total } / customerOrders.size).toString()
                        )
                    }
                    .sortedByDescending { it["إجمالي المشتريات"]?.toDoubleOrNull() ?: 0.0 }
                    .take(10)

                if (topCustomers.isEmpty()) {
                    showToast("لا توجد بيانات عملاء")
                    return@launch
                }

                val result = exportHelper.exportCustomersToCSV(topCustomers, "أفضل_العملاء")
                handleExportResult(result, "أفضل العملاء")

            } catch (e: Exception) {
                showToast("خطأ في تصدير أفضل العملاء: ${e.message}")
            } finally {
                setExportingState(false)
            }
        }
    }

    private fun handleExportResult(result: Result<File>, dataType: String) {
        if (result.isSuccess) {
            val file = result.getOrNull()!!
            showToast("تم تصدير $dataType بنجاح")
            
            // عرض خيارات للملف
            showFileOptionsDialog(file, dataType)
        } else {
            showToast("فشل في تصدير $dataType: ${result.exceptionOrNull()?.message}")
        }
    }

    private fun showFileOptionsDialog(file: File, dataType: String) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("تم التصدير بنجاح")
            .setMessage("تم تصدير $dataType إلى:\n${file.name}")
            .setPositiveButton("فتح الملف") { _, _ ->
                openFile(file)
            }
            .setNeutralButton("مشاركة") { _, _ ->
                shareFile(file)
            }
            .setNegativeButton("موافق", null)
            .show()
    }

    private fun openFile(file: File) {
        try {
            val intent = Intent(Intent.ACTION_VIEW)
            val uri = androidx.core.content.FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )
            intent.setDataAndType(uri, "text/csv")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            startActivity(intent)
        } catch (e: Exception) {
            showToast("لا يمكن فتح الملف: ${e.message}")
        }
    }

    private fun shareFile(file: File) {
        try {
            val intent = Intent(Intent.ACTION_SEND)
            val uri = androidx.core.content.FileProvider.getUriForFile(
                this,
                "${packageName}.fileprovider",
                file
            )
            intent.type = "text/csv"
            intent.putExtra(Intent.EXTRA_STREAM, uri)
            intent.putExtra(Intent.EXTRA_SUBJECT, "بيانات كهربائيات عامر")
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            startActivity(Intent.createChooser(intent, "مشاركة الملف"))
        } catch (e: Exception) {
            showToast("لا يمكن مشاركة الملف: ${e.message}")
        }
    }

    private fun setExportingState(exporting: Boolean, message: String = "") {
        isExporting = exporting
        
        runOnUiThread {
            binding.progressBar.visibility = if (exporting) View.VISIBLE else View.GONE
            binding.tvStatus.visibility = if (exporting) View.VISIBLE else View.GONE
            binding.tvStatus.text = message
            
            // تعطيل/تفعيل الأزرار
            binding.btnExportProducts.isEnabled = !exporting
            binding.btnExportOrders.isEnabled = !exporting
            binding.btnExportCustomers.isEnabled = !exporting
            binding.btnExportStatistics.isEnabled = !exporting
            binding.btnExportAll.isEnabled = !exporting
            binding.btnCustomExport.isEnabled = !exporting
        }
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    override fun onSupportNavigateUp(): Boolean {
        finish()
        return true
    }
}
