package com.kahrabaiat.amer.utils

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.graphics.BitmapFactory
import android.media.RingtoneManager
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
// import com.google.firebase.messaging.FirebaseMessaging // تم تعطيله مؤقتاً
import com.kahrabaiat.amer.AdminActivity
import com.kahrabaiat.amer.OrdersActivity
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.models.Order
import com.kahrabaiat.amer.models.OrderStatus
import java.text.NumberFormat
import java.text.SimpleDateFormat
import java.util.*

class NotificationHelper(private val context: Context) {

    companion object {
        // Notification Channels
        const val ORDERS_CHANNEL_ID = "order_notifications"
        const val GENERAL_CHANNEL_ID = "general_notifications"
        const val URGENT_CHANNEL_ID = "urgent_notifications"
        const val INVENTORY_CHANNEL_ID = "inventory_notifications"

        // Notification IDs
        const val NEW_ORDER_NOTIFICATION_ID = 1001
        const val ORDER_STATUS_NOTIFICATION_ID = 1002
        const val GENERAL_NOTIFICATION_ID = 1003
        const val URGENT_NOTIFICATION_ID = 1004
        const val LOW_STOCK_NOTIFICATION_ID = 1005
        const val OUT_OF_STOCK_NOTIFICATION_ID = 1006

        // Topics
        const val ADMIN_TOPIC = "admin_notifications"
        const val NEW_ORDERS_TOPIC = "new_orders"
        const val INVENTORY_TOPIC = "inventory_alerts"

        // Preferences
        const val PREFS_NAME = "notification_prefs"
        const val PREF_NEW_ORDERS = "new_orders_enabled"
        const val PREF_ORDER_UPDATES = "order_updates_enabled"
        const val PREF_GENERAL = "general_enabled"
        const val PREF_INVENTORY = "inventory_enabled"
    }

    init {
        createNotificationChannels()
        subscribeToAdminTopic()
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Orders Channel
            val ordersChannel = NotificationChannel(
                ORDERS_CHANNEL_ID,
                "إشعارات الطلبات",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "إشعارات الطلبات الجديدة وتحديثات الحالة"
                enableLights(true)
                lightColor = ContextCompat.getColor(context, R.color.primary)
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 250, 250, 250)
            }

            // General Channel
            val generalChannel = NotificationChannel(
                GENERAL_CHANNEL_ID,
                "إشعارات عامة",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "إشعارات عامة وتذكيرات"
                enableLights(true)
                lightColor = ContextCompat.getColor(context, R.color.accent_teal)
            }

            // Urgent Channel
            val urgentChannel = NotificationChannel(
                URGENT_CHANNEL_ID,
                "إشعارات عاجلة",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "إشعارات عاجلة ومهمة"
                enableLights(true)
                lightColor = ContextCompat.getColor(context, R.color.error_red)
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 100, 100, 100, 100, 100)
                setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_ALARM), null)
            }

            // Inventory Channel
            val inventoryChannel = NotificationChannel(
                INVENTORY_CHANNEL_ID,
                "إشعارات المخزون",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "تنبيهات نفاد المخزون والمخزون المنخفض"
                enableLights(true)
                lightColor = ContextCompat.getColor(context, R.color.warning_yellow)
                enableVibration(true)
                vibrationPattern = longArrayOf(0, 200, 100, 200)
            }

            notificationManager.createNotificationChannels(listOf(ordersChannel, generalChannel, urgentChannel, inventoryChannel))
        }
    }

    private fun subscribeToAdminTopic() {
        // FirebaseMessaging.getInstance().subscribeToTopic(ADMIN_TOPIC) // تم تعطيله مؤقتاً
    }

    // Notification Preferences
    fun isNotificationEnabled(type: String): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return when (type) {
            PREF_NEW_ORDERS -> prefs.getBoolean(PREF_NEW_ORDERS, true)
            PREF_ORDER_UPDATES -> prefs.getBoolean(PREF_ORDER_UPDATES, true)
            PREF_GENERAL -> prefs.getBoolean(PREF_GENERAL, true)
            else -> true
        }
    }

    fun setNotificationEnabled(type: String, enabled: Boolean) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(type, enabled).apply()
    }

    // New Order Notification
    fun sendNewOrderNotification(order: Order) {
        if (!isNotificationEnabled(PREF_NEW_ORDERS)) return

        val intent = Intent(context, OrdersActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("order_id", order.id)
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            NEW_ORDER_NOTIFICATION_ID,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val numberFormat = NumberFormat.getNumberInstance(Locale("ar", "IQ"))
        val formattedAmount = "${numberFormat.format(order.total.toInt())} د.ع"

        val largeIcon = BitmapFactory.decodeResource(context.resources, R.drawable.ic_shopping_cart)

        val notification = NotificationCompat.Builder(context, ORDERS_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setLargeIcon(largeIcon)
            .setContentTitle("📦 طلب جديد!")
            .setContentText("طلب جديد من ${order.customerName}")
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("📦 طلب جديد\n" +
                            "👤 العميل: ${order.customerName}\n" +
                            "📞 الهاتف: ${order.customerPhone}\n" +
                            "💰 المبلغ: $formattedAmount\n" +
                            "📋 رقم الطلب: ${order.id}")
            )
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_MESSAGE)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setSound(RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION))
            .setVibrate(longArrayOf(0, 250, 250, 250))
            .setColor(ContextCompat.getColor(context, R.color.primary))
            .addAction(
                R.drawable.ic_visibility,
                "عرض الطلب",
                pendingIntent
            )
            .build()

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NEW_ORDER_NOTIFICATION_ID, notification)
    }

    // Order Status Update Notification
    fun sendOrderStatusUpdateNotification(order: Order, oldStatus: String, newStatus: String) {
        if (!isNotificationEnabled(PREF_ORDER_UPDATES)) return

        val intent = Intent(context, OrdersActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("order_id", order.id)
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            ORDER_STATUS_NOTIFICATION_ID,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val statusIcon = getStatusIcon(newStatus)
        val statusText = getStatusDisplayName(newStatus)
        val oldStatusText = getStatusDisplayName(oldStatus)

        val notification = NotificationCompat.Builder(context, ORDERS_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("$statusIcon تحديث حالة الطلب")
            .setContentText("طلب ${order.id} تغير إلى $statusText")
            .setStyle(
                NotificationCompat.BigTextStyle()
                    .bigText("$statusIcon تحديث حالة الطلب\n" +
                            "📋 رقم الطلب: ${order.id}\n" +
                            "👤 العميل: ${order.customerName}\n" +
                            "🔄 من: $oldStatusText\n" +
                            "✅ إلى: $statusText")
            )
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setCategory(NotificationCompat.CATEGORY_STATUS)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(getStatusColor(newStatus))
            .build()

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(ORDER_STATUS_NOTIFICATION_ID, notification)
    }

    // General Notification
    fun sendGeneralNotification(title: String, message: String, isUrgent: Boolean = false) {
        if (!isNotificationEnabled(PREF_GENERAL)) return

        val intent = Intent(context, AdminActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            if (isUrgent) URGENT_NOTIFICATION_ID else GENERAL_NOTIFICATION_ID,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val channelId = if (isUrgent) URGENT_CHANNEL_ID else GENERAL_CHANNEL_ID
        val priority = if (isUrgent) NotificationCompat.PRIORITY_HIGH else NotificationCompat.PRIORITY_DEFAULT

        val notification = NotificationCompat.Builder(context, channelId)
            .setSmallIcon(if (isUrgent) R.drawable.ic_warning else R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(priority)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(ContextCompat.getColor(context, if (isUrgent) R.color.error_red else R.color.primary))
            .build()

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notificationId = if (isUrgent) URGENT_NOTIFICATION_ID else GENERAL_NOTIFICATION_ID
        notificationManager.notify(notificationId, notification)
    }

    // Helper functions
    private fun getStatusIcon(status: String): String {
        return when (status) {
            "pending" -> "⏳"
            "confirmed" -> "✅"
            "processing" -> "🔄"
            "shipped" -> "🚚"
            "delivered" -> "🎉"
            "cancelled" -> "❌"
            else -> "📋"
        }
    }

    private fun getStatusDisplayName(status: String): String {
        return when (status) {
            "pending" -> "معلق"
            "confirmed" -> "مؤكد"
            "processing" -> "قيد المعالجة"
            "shipped" -> "مشحون"
            "delivered" -> "مسلم"
            "cancelled" -> "ملغي"
            else -> "غير محدد"
        }
    }

    private fun getStatusColor(status: String): Int {
        return ContextCompat.getColor(context, when (status) {
            "pending" -> R.color.warning_yellow
            "confirmed" -> R.color.primary
            "processing" -> R.color.accent_orange
            "shipped" -> R.color.accent_teal
            "delivered" -> R.color.success_green
            "cancelled" -> R.color.error_red
            else -> R.color.text_secondary
        })
    }

    /**
     * إرسال إشعار نفاد المخزون
     */
    fun sendOutOfStockNotification(outOfStockProducts: List<com.kahrabaiat.amer.models.Product>) {
        if (!isInventoryNotificationEnabled()) return

        val title = "تحذير: نفاد المخزون!"
        val message = when {
            outOfStockProducts.size == 1 -> "نفد مخزون ${outOfStockProducts.first().name}"
            outOfStockProducts.size <= 3 -> {
                val names = outOfStockProducts.take(3).joinToString(", ") { it.name }
                "نفد مخزون: $names"
            }
            else -> "نفد مخزون ${outOfStockProducts.size} منتج"
        }

        val intent = Intent(context, com.kahrabaiat.amer.ManageProductsActivity::class.java).apply {
            putExtra("filter_out_of_stock", true)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            OUT_OF_STOCK_NOTIFICATION_ID,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, INVENTORY_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_warning)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(buildOutOfStockMessage(outOfStockProducts)))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setColor(ContextCompat.getColor(context, R.color.error_red))
            .addAction(
                R.drawable.ic_inventory,
                "إدارة المخزون",
                pendingIntent
            )
            .build()

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(OUT_OF_STOCK_NOTIFICATION_ID, notification)
    }

    /**
     * إرسال إشعار المخزون المنخفض
     */
    fun sendLowStockNotification(lowStockProducts: List<com.kahrabaiat.amer.models.Product>) {
        if (!isInventoryNotificationEnabled()) return

        val title = "تنبيه: مخزون منخفض"
        val message = when {
            lowStockProducts.size == 1 -> {
                val product = lowStockProducts.first()
                "مخزون منخفض: ${product.name} (الكمية: ${product.stock})"
            }
            lowStockProducts.size <= 3 -> {
                val names = lowStockProducts.take(3).joinToString(", ") { "${it.name} (${it.stock})" }
                "مخزون منخفض: $names"
            }
            else -> "${lowStockProducts.size} منتج بمخزون منخفض"
        }

        val intent = Intent(context, com.kahrabaiat.amer.ManageProductsActivity::class.java).apply {
            putExtra("filter_low_stock", true)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }

        val pendingIntent = PendingIntent.getActivity(
            context,
            LOW_STOCK_NOTIFICATION_ID,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(context, INVENTORY_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_inventory)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(buildLowStockMessage(lowStockProducts)))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .setColor(ContextCompat.getColor(context, R.color.warning_yellow))
            .addAction(
                R.drawable.ic_add_shopping_cart,
                "طلب تموين",
                createRestockIntent()
            )
            .build()

        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(LOW_STOCK_NOTIFICATION_ID, notification)
    }

    private fun buildOutOfStockMessage(products: List<com.kahrabaiat.amer.models.Product>): String {
        return buildString {
            appendLine("المنتجات التي نفد مخزونها:")
            products.forEach { product ->
                appendLine("• ${product.name}")
            }
            appendLine()
            appendLine("يرجى إعادة تموين هذه المنتجات في أقرب وقت.")
        }
    }

    private fun buildLowStockMessage(products: List<com.kahrabaiat.amer.models.Product>): String {
        return buildString {
            appendLine("المنتجات ذات المخزون المنخفض:")
            products.forEach { product ->
                appendLine("• ${product.name} - الكمية: ${product.stock}")
            }
            appendLine()
            appendLine("ينصح بإعادة تموين هذه المنتجات قريباً.")
        }
    }

    private fun createRestockIntent(): PendingIntent {
        val intent = Intent(context, com.kahrabaiat.amer.AdminActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }

        return PendingIntent.getActivity(
            context,
            9999,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }

    /**
     * التحقق من تفعيل إشعارات المخزون
     */
    fun isInventoryNotificationEnabled(): Boolean {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        return prefs.getBoolean(PREF_INVENTORY, true)
    }

    /**
     * تفعيل/إلغاء تفعيل إشعارات المخزون
     */
    fun setInventoryNotificationEnabled(enabled: Boolean) {
        val prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        prefs.edit().putBoolean(PREF_INVENTORY, enabled).apply()
    }

    // Clear all notifications
    fun clearAllNotifications() {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.cancelAll()
    }
}
