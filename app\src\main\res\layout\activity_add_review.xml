<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/primary_blue"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Product Info Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="المنتج المراد مراجعته"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/tvProductName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="اسم المنتج"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/tvProductPrice"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="السعر"
                        android:textColor="@color/primary_blue"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Review Form Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="مراجعتك"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Customer Name -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="اسمك الكريم"
                        app:boxStrokeColor="@color/primary_blue"
                        app:hintTextColor="@color/primary_blue">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCustomerName"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="16sp"
                            android:inputType="textPersonName"
                            android:maxLength="50" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Customer Phone -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="رقم الهاتف"
                        app:boxStrokeColor="@color/primary_blue"
                        app:hintTextColor="@color/primary_blue">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etCustomerPhone"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="16sp"
                            android:inputType="phone"
                            android:maxLength="15" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- Rating Section -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تقييمك للمنتج"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold"
                        android:layout_marginBottom="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="8dp">

                        <RatingBar
                            android:id="@+id/ratingBar"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:numStars="5"
                            android:rating="5"
                            android:stepSize="0.5"
                            android:progressTint="@color/accent_orange"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:id="@+id/tvRatingText"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="ممتاز (5.0)"
                            android:textColor="@color/success_green"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:fontFamily="@font/cairo_bold" />

                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="انقر على النجوم لتحديد تقييمك"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        android:layout_marginBottom="20dp" />

                    <!-- Comment -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="20dp"
                        android:hint="اكتب مراجعتك هنا..."
                        app:boxStrokeColor="@color/primary_blue"
                        app:hintTextColor="@color/primary_blue"
                        app:counterEnabled="true"
                        app:counterMaxLength="500">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/etComment"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:fontFamily="@font/cairo_regular"
                            android:textSize="16sp"
                            android:inputType="textMultiLine"
                            android:lines="4"
                            android:maxLines="6"
                            android:maxLength="500"
                            android:gravity="start|top" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💡 نصيحة: اكتب مراجعة مفصلة لمساعدة العملاء الآخرين"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular"
                        android:background="@drawable/rounded_background"
                        android:padding="12dp"
                        android:layout_marginBottom="20dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnCancel"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    android:text="إلغاء"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:fontFamily="@font/cairo_bold"
                    app:cornerRadius="12dp"
                    app:strokeColor="@color/text_secondary" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnSubmitReview"
                    android:layout_width="0dp"
                    android:layout_height="56dp"
                    android:layout_weight="2"
                    android:layout_marginStart="8dp"
                    android:text="إرسال المراجعة"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:fontFamily="@font/cairo_bold"
                    android:background="@drawable/button_primary_gradient"
                    app:cornerRadius="12dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
