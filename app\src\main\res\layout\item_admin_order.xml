<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <!-- Order Header -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvOrderNumber"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="طلب #2025-001234" />

            <TextView
                android:id="@+id/tvOrderStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/status_badge_background"
                android:padding="4dp"
                android:textColor="@color/white"
                android:textSize="10sp"
                tools:text="جديد" />

        </LinearLayout>

        <!-- Customer Info -->
        <TextView
            android:id="@+id/tvCustomerName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            tools:text="العميل: أحمد محمد" />

        <!-- Order Details -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvOrderDate"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_secondary"
                android:textSize="11sp"
                tools:text="15/01/2025 14:30" />

            <TextView
                android:id="@+id/tvOrderTotal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_blue"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="2500 د.ع" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
