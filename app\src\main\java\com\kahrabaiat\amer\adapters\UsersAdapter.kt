package com.kahrabaiat.amer.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.kahrabaiat.amer.R
import com.kahrabaiat.amer.databinding.ItemUserBinding
import com.kahrabaiat.amer.models.User
import com.kahrabaiat.amer.models.UserRole
import java.text.SimpleDateFormat
import java.util.*

class UsersAdapter(
    private val users: List<User>,
    private val onEditClick: (User) -> Unit,
    private val onDeleteClick: (User) -> Unit,
    private val onToggleStatusClick: (User) -> Unit
) : RecyclerView.Adapter<UsersAdapter.UserViewHolder>() {

    private val dateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): UserViewHolder {
        val binding = ItemUserBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return UserViewHolder(binding)
    }

    override fun onBindViewHolder(holder: UserViewHolder, position: Int) {
        holder.bind(users[position])
    }

    override fun getItemCount(): Int = users.size

    inner class UserViewHolder(private val binding: ItemUserBinding) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(user: User) {
            binding.apply {
                // User basic info
                tvUserName.text = user.name
                tvUserEmail.text = user.email
                tvUserPhone.text = user.phone
                
                // Role
                tvUserRole.text = user.role.displayName
                tvUserRole.setBackgroundColor(getRoleColor(user.role))
                
                // Status
                if (user.isActive) {
                    tvUserStatus.text = "نشط"
                    tvUserStatus.setTextColor(ContextCompat.getColor(itemView.context, R.color.success_green))
                    btnToggleStatus.text = "إلغاء التفعيل"
                    btnToggleStatus.setBackgroundColor(ContextCompat.getColor(itemView.context, R.color.warning_yellow))
                } else {
                    tvUserStatus.text = "غير نشط"
                    tvUserStatus.setTextColor(ContextCompat.getColor(itemView.context, R.color.error_red))
                    btnToggleStatus.text = "تفعيل"
                    btnToggleStatus.setBackgroundColor(ContextCompat.getColor(itemView.context, R.color.success_green))
                }
                
                // Dates
                tvCreatedAt.text = "تاريخ الإنشاء: ${dateFormat.format(Date(user.createdAt))}"
                if (user.lastLoginAt > 0) {
                    tvLastLogin.text = "آخر دخول: ${dateFormat.format(Date(user.lastLoginAt))}"
                } else {
                    tvLastLogin.text = "لم يسجل دخول بعد"
                }
                
                // Permissions count
                tvPermissionsCount.text = "الصلاحيات: ${user.permissions.size}"
                
                // Click listeners
                btnEdit.setOnClickListener { onEditClick(user) }
                btnDelete.setOnClickListener { onDeleteClick(user) }
                btnToggleStatus.setOnClickListener { onToggleStatusClick(user) }
                
                // Card click for details
                root.setOnClickListener { onEditClick(user) }
            }
        }
        
        private fun getRoleColor(role: UserRole): Int {
            return ContextCompat.getColor(itemView.context, when (role) {
                UserRole.ADMIN -> R.color.error_red
                UserRole.MANAGER -> R.color.primary
                UserRole.EMPLOYEE -> R.color.accent_teal
                UserRole.VIEWER -> R.color.text_secondary
            })
        }
    }
}
