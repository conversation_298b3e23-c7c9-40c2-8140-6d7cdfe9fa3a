<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:orientation="vertical"
    tools:context=".CartActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_background"
        android:elevation="8dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white"
        app:title="@string/cart"
        app:titleTextColor="@color/white" />

    <!-- Content -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- Cart Items RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCartItems"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp"
            tools:listitem="@layout/item_cart" />

        <!-- Empty Cart View -->
        <LinearLayout
            android:id="@+id/layoutEmptyCart"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="32dp"
            android:visibility="gone">

            <ImageView
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_marginBottom="16dp"
                android:alpha="0.5"
                android:src="@drawable/ic_shopping_cart"
                android:tint="@color/medium_gray" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/cart_empty"
                android:textColor="@color/text_primary"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:text="ابدأ بإضافة المنتجات إلى سلة المشتريات"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <Button
                android:id="@+id/btnStartShopping"
                style="@style/PrimaryButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ابدأ التسوق" />

        </LinearLayout>

    </FrameLayout>

    <!-- Bottom Section -->
    <LinearLayout
        android:id="@+id/layoutCartSummary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="8dp"
        android:orientation="vertical"
        android:padding="16dp"
        android:visibility="gone">

        <!-- Subtotal -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="المجموع الفرعي:"
                android:textColor="@color/text_secondary"
                android:textSize="16sp" />

            <TextView
                android:id="@+id/tvSubtotal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                tools:text="2500 دينار" />

        </LinearLayout>

        <!-- Coupon Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical">

            <!-- Coupon Input -->
            <LinearLayout
                android:id="@+id/layoutCouponInput"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:boxBackgroundMode="outline"
                    app:boxCornerRadiusBottomEnd="8dp"
                    app:boxCornerRadiusBottomStart="8dp"
                    app:boxCornerRadiusTopEnd="8dp"
                    app:boxCornerRadiusTopStart="8dp">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etCouponCode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="كود الكوبون"
                        android:inputType="textCapCharacters"
                        android:maxLines="1" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnApplyCoupon"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="48dp"
                    android:text="تطبيق"
                    android:textSize="12sp"
                    app:cornerRadius="8dp" />

            </LinearLayout>

            <!-- View Coupons Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnViewCoupons"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="match_parent"
                android:layout_height="36dp"
                android:text="🎫 عرض الكوبونات المتاحة"
                android:textColor="@color/accent_orange"
                android:textSize="12sp"
                android:fontFamily="@font/cairo_regular"
                app:cornerRadius="8dp" />

            <!-- Applied Coupon -->
            <LinearLayout
                android:id="@+id/layoutAppliedCoupon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="12dp"
                android:background="@drawable/rounded_background"
                android:backgroundTint="@color/success_light"
                android:layout_marginTop="8dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="🎫"
                    android:textSize="20sp"
                    android:layout_marginEnd="8dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvCouponName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="كوبون الخصم"
                        android:textColor="@color/success_green"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:fontFamily="@font/cairo_bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="تم تطبيق الكوبون بنجاح"
                        android:textColor="@color/success_green"
                        android:textSize="12sp"
                        android:fontFamily="@font/cairo_regular" />

                </LinearLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btnRemoveCoupon"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="32dp"
                    android:text="إزالة"
                    android:textColor="@color/error_red"
                    android:textSize="12sp"
                    app:cornerRadius="16dp" />

            </LinearLayout>

            <!-- Coupon Discount -->
            <LinearLayout
                android:id="@+id/layoutCouponDiscount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="خصم الكوبون:"
                    android:textColor="@color/success_green"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/tvCouponDiscount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/success_green"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="-50 دينار" />

            </LinearLayout>

        </LinearLayout>

        <!-- Total Amount -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="12dp"
            android:background="@drawable/rounded_background"
            android:backgroundTint="@color/primary_light">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="المجموع الكلي:"
                android:textColor="@color/primary_blue"
                android:textSize="18sp"
                android:textStyle="bold"
                android:fontFamily="@font/cairo_bold" />

            <TextView
                android:id="@+id/tvTotalAmount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_blue"
                android:textSize="20sp"
                android:textStyle="bold"
                android:fontFamily="@font/cairo_bold"
                tools:text="2450 دينار" />

        </LinearLayout>

        <!-- Checkout Button -->
        <Button
            android:id="@+id/btnCheckout"
            style="@style/PrimaryButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/checkout" />

    </LinearLayout>

</LinearLayout>
