package com.kahrabaiat.amer.ui

import android.app.Activity
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.ViewTreeObserver
import androidx.recyclerview.widget.RecyclerView

object PerformanceOptimizer {
    
    /**
     * تحسين أداء RecyclerView
     */
    fun optimizeRecyclerView(recyclerView: RecyclerView) {
        recyclerView.apply {
            // تفعيل تجميع العناصر المتشابهة
            setHasFixedSize(true)
            
            // تحسين الذاكرة
            setItemViewCacheSize(20)
            
            // تحسين الرسم
            isDrawingCacheEnabled = true
            drawingCacheQuality = View.DRAWING_CACHE_QUALITY_HIGH
            
            // تحسين التمرير
            isNestedScrollingEnabled = false
        }
    }
    
    /**
     * تأخير تنفيذ عملية لتحسين الأداء
     */
    fun delayExecution(delayMs: Long, action: () -> Unit) {
        Handler(Looper.getMainLooper()).postDelayed(action, delayMs)
    }
    
    /**
     * تنفيذ عملية بعد اكتمال الرسم
     */
    fun executeAfterLayout(view: View, action: () -> Unit) {
        view.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                view.viewTreeObserver.removeOnGlobalLayoutListener(this)
                action()
            }
        })
    }
    
    /**
     * تحسين استهلاك الذاكرة للصور
     */
    fun optimizeImageMemory(context: Context) {
        // تنظيف ذاكرة Glide
        ImageLoader.clearCache(context)
        
        // تشغيل garbage collector
        System.gc()
    }
    
    /**
     * تحسين أداء النشاط
     */
    fun optimizeActivity(activity: Activity) {
        // تحسين النافذة
        activity.window?.apply {
            // تفعيل تسريع الأجهزة
            setFlags(
                android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                android.view.WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
        }
    }
    
    /**
     * تحسين الانيميشن للأجهزة الضعيفة
     */
    fun optimizeAnimationsForLowEndDevices(context: Context): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        
        return if (activityManager.isLowRamDevice) {
            // تقليل مدة الانيميشن للأجهزة الضعيفة
            true
        } else {
            false
        }
    }
    
    /**
     * تحسين التمرير السلس
     */
    fun enableSmoothScrolling(recyclerView: RecyclerView) {
        recyclerView.layoutManager?.let { layoutManager ->
            if (layoutManager is androidx.recyclerview.widget.LinearLayoutManager) {
                layoutManager.isSmoothScrollbarEnabled = true
            }
        }
    }
    
    /**
     * تحسين عرض القوائم الطويلة
     */
    fun optimizeLongLists(recyclerView: RecyclerView) {
        recyclerView.apply {
            // تحسين عدد العناصر المخزنة
            setItemViewCacheSize(10)
            
            // تحسين pool المشترك
            val sharedPool = RecyclerView.RecycledViewPool()
            sharedPool.setMaxRecycledViews(0, 20)
            setRecycledViewPool(sharedPool)
        }
    }
    
    /**
     * مراقبة استهلاك الذاكرة
     */
    fun monitorMemoryUsage(context: Context): MemoryInfo {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
        val memoryInfo = android.app.ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)
        
        val runtime = Runtime.getRuntime()
        val usedMemory = runtime.totalMemory() - runtime.freeMemory()
        val maxMemory = runtime.maxMemory()
        val availableMemory = maxMemory - usedMemory
        
        return MemoryInfo(
            totalMemory = memoryInfo.totalMem,
            availableMemory = memoryInfo.availMem,
            usedMemory = usedMemory,
            maxMemory = maxMemory,
            isLowMemory = memoryInfo.lowMemory
        )
    }
    
    data class MemoryInfo(
        val totalMemory: Long,
        val availableMemory: Long,
        val usedMemory: Long,
        val maxMemory: Long,
        val isLowMemory: Boolean
    )
}
