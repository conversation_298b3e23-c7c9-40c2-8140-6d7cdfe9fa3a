<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <layer-list>
            <!-- Shadow -->
            <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="6dp">
                <shape android:shape="rectangle">
                    <solid android:color="#40FF9800" />
                    <corners android:radius="18dp" />
                </shape>
            </item>
            
            <!-- Button Background -->
            <item android:top="0dp" android:left="0dp" android:right="0dp" android:bottom="4dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:angle="135"
                        android:startColor="#E65100"
                        android:centerColor="#FF9800"
                        android:endColor="#FFC107"
                        android:type="linear" />
                    <corners android:radius="16dp" />
                    <stroke android:width="1dp" android:color="#E65100" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Normal State -->
    <item>
        <layer-list>
            <!-- Shadow -->
            <item android:top="2dp" android:left="2dp" android:right="2dp" android:bottom="6dp">
                <shape android:shape="rectangle">
                    <solid android:color="#60FF9800" />
                    <corners android:radius="18dp" />
                </shape>
            </item>
            
            <!-- Button Background -->
            <item android:top="0dp" android:left="0dp" android:right="0dp" android:bottom="4dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:angle="135"
                        android:startColor="#FF9800"
                        android:centerColor="#FFC107"
                        android:endColor="#FFEB3B"
                        android:type="linear" />
                    <corners android:radius="16dp" />
                    <stroke android:width="1dp" android:color="#FF9800" />
                </shape>
            </item>
        </layer-list>
    </item>
    
</selector>
