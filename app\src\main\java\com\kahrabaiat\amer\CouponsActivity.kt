package com.kahrabaiat.amer

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.MenuItem
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.kahrabaiat.amer.adapters.CouponAdapter
import com.kahrabaiat.amer.databinding.ActivityCouponsBinding
import com.kahrabaiat.amer.models.Coupon
import com.kahrabaiat.amer.utils.CouponManager
import kotlinx.coroutines.launch

class CouponsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCouponsBinding
    private lateinit var couponManager: CouponManager
    private lateinit var couponAdapter: CouponAdapter
    private var isAdminMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCouponsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getIntentData()
        initializeComponents()
        setupToolbar()
        setupRecyclerView()
        setupClickListeners()
        loadCoupons()
    }

    private fun getIntentData() {
        isAdminMode = intent.getBooleanExtra("admin_mode", false)
    }

    private fun initializeComponents() {
        couponManager = CouponManager.getInstance(this)
    }

    private fun setupToolbar() {
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            title = if (isAdminMode) "إدارة الكوبونات" else "الكوبونات والعروض"
        }
    }

    private fun setupRecyclerView() {
        couponAdapter = CouponAdapter(
            onCouponClick = { coupon ->
                showCouponDetails(coupon)
            },
            onCopyCode = { coupon ->
                copyCouponCode(coupon)
            },
            onToggleStatus = { coupon ->
                toggleCouponStatus(coupon)
            },
            onDeleteClick = { coupon ->
                deleteCoupon(coupon)
            },
            showAdminActions = isAdminMode
        )

        binding.rvCoupons.apply {
            layoutManager = LinearLayoutManager(this@CouponsActivity)
            adapter = couponAdapter
        }
    }

    private fun setupClickListeners() {
        binding.fabAddCoupon.setOnClickListener {
            if (isAdminMode) {
                // TODO: إنشاء CreateCouponActivity لاحقاً
                android.widget.Toast.makeText(this, "سيتم إضافة صفحة إنشاء الكوبونات قريباً", android.widget.Toast.LENGTH_SHORT).show()
            }
        }

        binding.btnRefresh.setOnClickListener {
            loadCoupons()
        }

        binding.btnFilterActive.setOnClickListener {
            filterCoupons("active")
        }

        binding.btnFilterExpired.setOnClickListener {
            filterCoupons("expired")
        }

        binding.btnFilterAll.setOnClickListener {
            filterCoupons("all")
        }

        // إظهار زر الإضافة للمدير فقط
        binding.fabAddCoupon.visibility = if (isAdminMode) View.VISIBLE else View.GONE
    }

    private fun loadCoupons() {
        showLoading()

        lifecycleScope.launch {
            try {
                val coupons = if (isAdminMode) {
                    couponManager.getAllCoupons()
                } else {
                    couponManager.getActiveCoupons()
                }
                showCoupons(coupons)
                updateStats(coupons)
            } catch (e: Exception) {
                showError("خطأ في تحميل الكوبونات: ${e.message}")
            }
        }
    }

    private fun showCoupons(coupons: List<Coupon>) {
        binding.progressBar.visibility = View.GONE
        
        if (coupons.isNotEmpty()) {
            binding.rvCoupons.visibility = View.VISIBLE
            binding.layoutEmpty.visibility = View.GONE
            couponAdapter.submitList(coupons)
        } else {
            binding.rvCoupons.visibility = View.GONE
            binding.layoutEmpty.visibility = View.VISIBLE
        }
    }

    private fun updateStats(coupons: List<Coupon>) {
        if (isAdminMode) {
            lifecycleScope.launch {
                try {
                    val stats = couponManager.getCouponStats()
                    val totalCount = stats["total"] ?: 0
                    val activeCount = stats["active"] ?: 0
                    val expiredCount = stats["expired"] ?: 0
                    
                    binding.tvCouponStats.text = "الإجمالي: $totalCount | نشط: $activeCount | منتهي: $expiredCount"
                    binding.tvCouponStats.visibility = View.VISIBLE
                    binding.layoutFilters.visibility = View.VISIBLE
                } catch (e: Exception) {
                    binding.tvCouponStats.visibility = View.GONE
                }
            }
        } else {
            binding.tvCouponStats.text = "الكوبونات المتاحة: ${coupons.size}"
            binding.tvCouponStats.visibility = View.VISIBLE
            binding.layoutFilters.visibility = View.GONE
        }
    }

    private fun filterCoupons(filter: String) {
        lifecycleScope.launch {
            try {
                val allCoupons = couponManager.getAllCoupons()
                val filteredCoupons = when (filter) {
                    "active" -> allCoupons.filter { it.isValid() }
                    "expired" -> allCoupons.filter { it.isExpired() }
                    "all" -> allCoupons
                    else -> allCoupons
                }
                showCoupons(filteredCoupons)
                
                // تحديث حالة الأزرار
                resetFilterButtons()
                when (filter) {
                    "active" -> binding.btnFilterActive.setBackgroundColor(getColor(R.color.primary_blue))
                    "expired" -> binding.btnFilterExpired.setBackgroundColor(getColor(R.color.error_red))
                    "all" -> binding.btnFilterAll.setBackgroundColor(getColor(R.color.success_green))
                }
            } catch (e: Exception) {
                showError("خطأ في فلترة الكوبونات")
            }
        }
    }

    private fun resetFilterButtons() {
        binding.btnFilterActive.setBackgroundColor(getColor(R.color.border_light))
        binding.btnFilterExpired.setBackgroundColor(getColor(R.color.border_light))
        binding.btnFilterAll.setBackgroundColor(getColor(R.color.border_light))
    }

    private fun showLoading() {
        binding.progressBar.visibility = View.VISIBLE
        binding.rvCoupons.visibility = View.GONE
        binding.layoutEmpty.visibility = View.GONE
    }

    private fun showError(message: String) {
        binding.progressBar.visibility = View.GONE
        binding.layoutEmpty.visibility = View.VISIBLE
        binding.tvEmptyMessage.text = message
        android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun showCouponDetails(coupon: Coupon) {
        // TODO: إنشاء CouponDetailActivity لاحقاً
        android.widget.Toast.makeText(this, "تفاصيل الكوبون: ${coupon.title}", android.widget.Toast.LENGTH_SHORT).show()
    }

    private fun copyCouponCode(coupon: Coupon) {
        val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clip = ClipData.newPlainText("كود الكوبون", coupon.code)
        clipboard.setPrimaryClip(clip)
        
        android.widget.Toast.makeText(
            this,
            "تم نسخ الكود: ${coupon.code}",
            android.widget.Toast.LENGTH_SHORT
        ).show()
    }

    private fun toggleCouponStatus(coupon: Coupon) {
        if (!isAdminMode) return
        
        lifecycleScope.launch {
            try {
                val newStatus = !coupon.isActive
                val success = couponManager.updateCouponStatus(coupon.id, newStatus)
                
                if (success) {
                    android.widget.Toast.makeText(
                        this@CouponsActivity,
                        if (newStatus) "تم تفعيل الكوبون" else "تم إيقاف الكوبون",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                    loadCoupons() // إعادة تحميل القائمة
                } else {
                    android.widget.Toast.makeText(
                        this@CouponsActivity,
                        "فشل في تحديث حالة الكوبون",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception) {
                android.widget.Toast.makeText(
                    this@CouponsActivity,
                    "خطأ في تحديث الكوبون",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    private fun deleteCoupon(coupon: Coupon) {
        if (!isAdminMode) return
        
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("حذف الكوبون")
            .setMessage("هل أنت متأكد من حذف كوبون \"${coupon.title}\"؟\nهذا الإجراء لا يمكن التراجع عنه.")
            .setPositiveButton("حذف") { _, _ ->
                performDeleteCoupon(coupon)
            }
            .setNegativeButton("إلغاء", null)
            .show()
    }

    private fun performDeleteCoupon(coupon: Coupon) {
        lifecycleScope.launch {
            try {
                val success = couponManager.deleteCoupon(coupon.id)
                
                if (success) {
                    android.widget.Toast.makeText(
                        this@CouponsActivity,
                        "تم حذف الكوبون بنجاح",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                    loadCoupons() // إعادة تحميل القائمة
                } else {
                    android.widget.Toast.makeText(
                        this@CouponsActivity,
                        "فشل في حذف الكوبون",
                        android.widget.Toast.LENGTH_SHORT
                    ).show()
                }
            } catch (e: Exception) {
                android.widget.Toast.makeText(
                    this@CouponsActivity,
                    "خطأ في حذف الكوبون",
                    android.widget.Toast.LENGTH_SHORT
                ).show()
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onResume() {
        super.onResume()
        // إعادة تحميل الكوبونات عند العودة للنشاط
        loadCoupons()
    }
}
