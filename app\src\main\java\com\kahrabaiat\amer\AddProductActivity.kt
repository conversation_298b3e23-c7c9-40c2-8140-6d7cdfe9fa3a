package com.kahrabaiat.amer

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.MenuItem
import android.widget.ArrayAdapter
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.kahrabaiat.amer.databinding.ActivityAddProductBinding
import com.kahrabaiat.amer.models.CategoryConstants
import com.kahrabaiat.amer.models.Product
import com.kahrabaiat.amer.database.DatabaseHelper
import com.kahrabaiat.amer.utils.ImagePickerHelper
import com.kahrabaiat.amer.utils.DataManager
import kotlinx.coroutines.launch
import java.io.File
import java.util.*

class AddProductActivity : BaseAdminActivity() {

    private lateinit var binding: ActivityAddProductBinding
    private lateinit var databaseHelper: DatabaseHelper
    private lateinit var dataManager: DataManager
    private lateinit var imagePickerHelper: ImagePickerHelper
    private var selectedImageUri: Uri? = null
    private var compressedImageFile: File? = null

    // Edit mode variables
    private var isEditMode = false
    private var editingProduct: Product? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            binding = ActivityAddProductBinding.inflate(layoutInflater)
            setContentView(binding.root)

            initializeComponents()
            checkEditMode()
            setupToolbar()
            setupCategorySpinner()
            setupClickListeners()

            if (isEditMode) {
                populateFieldsForEdit()
            }
        } catch (e: Exception) {
            android.util.Log.e("AddProductActivity", "Error in onCreate", e)
            showToast("خطأ في تحميل صفحة إضافة المنتج: ${e.message}")
            finish()
        }
    }

    private fun checkEditMode() {
        // Check if we're in edit mode
        editingProduct = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            intent.getParcelableExtra("product", Product::class.java)
        } else {
            @Suppress("DEPRECATION")
            intent.getParcelableExtra("product")
        }

        isEditMode = editingProduct != null
    }

    private fun initializeComponents() {
        databaseHelper = DatabaseHelper.getInstance(this)
        dataManager = DataManager.getInstance(this)
        imagePickerHelper = ImagePickerHelper(this)
        setupImagePickerCallback()
    }

    private fun setupToolbar() {
        try {
            setSupportActionBar(binding.toolbar)
            supportActionBar?.apply {
                setDisplayHomeAsUpEnabled(true)
                title = if (isEditMode) "تعديل المنتج" else "إضافة منتج جديد"
            }
        } catch (e: Exception) {
            android.util.Log.e("AddProductActivity", "Error setting up toolbar", e)
            // Hide toolbar if there's an error
            binding.toolbar.visibility = android.view.View.GONE
        }
    }

    private fun setupCategorySpinner() {
        try {
            val categories = listOf(
                CategoryConstants.HOME_APPLIANCES to "الأجهزة المنزلية",
                CategoryConstants.ELECTRICAL_APPLIANCES to "الأجهزة الكهربائية",
                CategoryConstants.HAND_TOOLS to "العدد اليدوية"
            )

            val adapter = ArrayAdapter(
                this,
                android.R.layout.simple_spinner_item,
                categories.map { it.second }
            )
            adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
            binding.spinnerCategory.adapter = adapter
        } catch (e: Exception) {
            android.util.Log.e("AddProductActivity", "Error setting up category spinner", e)
            showToast("خطأ في تحميل الفئات")
        }
    }

    private fun setupClickListeners() {
        binding.btnSaveProduct.setOnClickListener {
            saveProduct()
        }

        binding.btnCancel.setOnClickListener {
            finish()
        }

        binding.btnSelectImage.setOnClickListener {
            imagePickerHelper.selectImageFromGallery()
        }

        binding.btnTakePhoto.setOnClickListener {
            imagePickerHelper.takePhotoFromCamera()
        }
    }

    private fun setupImagePickerCallback() {
        imagePickerHelper.setCallback(object : ImagePickerHelper.ImagePickerCallback {
            override fun onImageSelected(imageUri: Uri, compressedFile: File) {
                selectedImageUri = imageUri
                compressedImageFile = compressedFile

                // Display the selected image
                Glide.with(this@AddProductActivity)
                    .load(imageUri)
                    .centerCrop()
                    .into(binding.ivProductImage)

                val fileSize = (compressedFile.length() / 1024).toInt() // KB
                showToast("تم اختيار الصورة بنجاح ($fileSize KB)")
            }

            override fun onError(error: String) {
                showToast("خطأ في اختيار الصورة: $error")
            }
        })
    }

    private fun populateFieldsForEdit() {
        editingProduct?.let { product ->
            // Fill basic fields
            binding.etProductName.setText(product.name)
            binding.etPrice.setText(product.price.toString())
            binding.etDescription.setText(product.description)
            binding.etStock.setText(product.stock.toString())
            binding.etDiscount.setText(if (product.discount > 0) product.discount.toString() else "")

            // Set category spinner
            val categories = listOf(
                CategoryConstants.HOME_APPLIANCES,
                CategoryConstants.ELECTRICAL_APPLIANCES,
                CategoryConstants.HAND_TOOLS
            )
            val categoryIndex = categories.indexOf(product.category)
            if (categoryIndex >= 0) {
                binding.spinnerCategory.setSelection(categoryIndex)
            }

            // Load existing image if available
            if (product.imageUrl.isNotEmpty()) {
                Glide.with(this)
                    .load(product.imageUrl)
                    .centerCrop()
                    .into(binding.ivProductImage)
            }

            // Update button text
            binding.btnSaveProduct.text = "حفظ التعديلات"
        }
    }

    private fun saveProduct() {
        if (!validateInputs()) {
            return
        }

        val product = if (isEditMode && editingProduct != null) {
            // Update existing product
            editingProduct!!.copy(
                name = binding.etProductName.text.toString().trim(),
                price = binding.etPrice.text.toString().toDouble(),
                description = binding.etDescription.text.toString().trim(),
                category = getCategoryFromSpinner(),
                stock = binding.etStock.text.toString().toInt(),
                discount = binding.etDiscount.text.toString().toIntOrNull() ?: 0
                // Keep existing imageUrl if no new image selected
            )
        } else {
            // Create new product
            Product(
                id = 0, // Database will auto-generate ID
                name = binding.etProductName.text.toString().trim(),
                price = binding.etPrice.text.toString().toDouble(),
                description = binding.etDescription.text.toString().trim(),
                imageUrl = "", // يمكن إضافة رفع الصور لاحقاً
                category = getCategoryFromSpinner(),
                stock = binding.etStock.text.toString().toInt(),
                available = true,
                discount = binding.etDiscount.text.toString().toIntOrNull() ?: 0
            )
        }

        // Disable button during save
        binding.btnSaveProduct.isEnabled = false

        // تغيير نص الزر حسب وضع التعديل ووجود صورة
        if (isEditMode) {
            if (compressedImageFile != null) {
                binding.btnSaveProduct.text = "جاري رفع الصورة وحفظ التعديلات..."
            } else {
                binding.btnSaveProduct.text = "جاري حفظ التعديلات..."
            }
        } else {
            if (compressedImageFile != null) {
                binding.btnSaveProduct.text = "جاري رفع الصورة والحفظ..."
            } else {
                binding.btnSaveProduct.text = "جاري الحفظ..."
            }
        }

        lifecycleScope.launch {
            try {
                val success = if (isEditMode) {
                    // تحديث منتج موجود
                    dataManager.updateProduct(product, compressedImageFile)
                } else {
                    // إضافة منتج جديد
                    dataManager.addProduct(product, compressedImageFile)
                }

                if (success) {
                    val message = if (isEditMode) "تم تحديث المنتج بنجاح" else "تم إضافة المنتج بنجاح"
                    showToast(message)
                    setResult(RESULT_OK)
                    finish()
                } else {
                    val errorMessage = if (isEditMode) "فشل في تحديث المنتج" else "فشل في إضافة المنتج"
                    showToast(errorMessage)
                    resetSaveButton()
                }
            } catch (e: Exception) {
                showToast("حدث خطأ: ${e.message}")
                resetSaveButton()
            }
        }
    }

    private fun resetSaveButton() {
        binding.btnSaveProduct.isEnabled = true
        binding.btnSaveProduct.text = if (isEditMode) "حفظ التعديلات" else "حفظ المنتج"
    }

    private fun validateInputs(): Boolean {
        var isValid = true

        // Validate product name
        if (binding.etProductName.text.toString().trim().isEmpty()) {
            binding.etProductName.error = "اسم المنتج مطلوب"
            isValid = false
        }

        // Validate price
        val priceText = binding.etPrice.text.toString().trim()
        if (priceText.isEmpty()) {
            binding.etPrice.error = "السعر مطلوب"
            isValid = false
        } else {
            try {
                val price = priceText.toDouble()
                if (price <= 0) {
                    binding.etPrice.error = "السعر يجب أن يكون أكبر من صفر"
                    isValid = false
                }
            } catch (e: NumberFormatException) {
                binding.etPrice.error = "السعر غير صحيح"
                isValid = false
            }
        }

        // Validate stock
        val stockText = binding.etStock.text.toString().trim()
        if (stockText.isEmpty()) {
            binding.etStock.error = "الكمية مطلوبة"
            isValid = false
        } else {
            try {
                val stock = stockText.toInt()
                if (stock < 0) {
                    binding.etStock.error = "الكمية لا يمكن أن تكون سالبة"
                    isValid = false
                }
            } catch (e: NumberFormatException) {
                binding.etStock.error = "الكمية غير صحيحة"
                isValid = false
            }
        }

        // Validate discount (optional)
        val discountText = binding.etDiscount.text.toString().trim()
        if (discountText.isNotEmpty()) {
            try {
                val discount = discountText.toInt()
                if (discount < 0 || discount > 100) {
                    binding.etDiscount.error = "الخصم يجب أن يكون بين 0 و 100"
                    isValid = false
                }
            } catch (e: NumberFormatException) {
                binding.etDiscount.error = "الخصم غير صحيح"
                isValid = false
            }
        }

        return isValid
    }

    private fun getCategoryFromSpinner(): String {
        val categories = listOf(
            CategoryConstants.HOME_APPLIANCES,
            CategoryConstants.ELECTRICAL_APPLIANCES,
            CategoryConstants.HAND_TOOLS
        )
        return categories[binding.spinnerCategory.selectedItemPosition]
    }

    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == ImagePickerHelper.IMAGE_PICKER_REQUEST && resultCode == Activity.RESULT_OK) {
            data?.data?.let { uri ->
                lifecycleScope.launch {
                    try {
                        val file = File(getRealPathFromURI(uri) ?: return@launch)
                        val compressedFile = imagePickerHelper.compressImage(file)

                        selectedImageUri = uri
                        compressedImageFile = compressedFile

                        // Display the selected image
                        Glide.with(this@AddProductActivity)
                            .load(uri)
                            .centerCrop()
                            .into(binding.ivProductImage)

                        showToast("تم اختيار الصورة بنجاح")
                    } catch (e: Exception) {
                        showToast("خطأ في معالجة الصورة")
                    }
                }
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        imagePickerHelper.handlePermissionResult(requestCode, grantResults)
    }

    private fun getRealPathFromURI(uri: Uri): String? {
        return try {
            val inputStream = contentResolver.openInputStream(uri)
            val file = File(cacheDir, "temp_image_${System.currentTimeMillis()}.jpg")
            inputStream?.use { input ->
                file.outputStream().use { output ->
                    input.copyTo(output)
                }
            }
            file.absolutePath
        } catch (e: Exception) {
            null
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}
