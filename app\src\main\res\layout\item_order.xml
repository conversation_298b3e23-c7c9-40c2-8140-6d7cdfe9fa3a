<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Header Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <!-- Order Number -->
            <TextView
                android:id="@+id/tvOrderNumber"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="طلب #2025-001234" />

            <!-- Status Badge -->
            <TextView
                android:id="@+id/tvStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_status_pending"
                android:paddingHorizontal="12dp"
                android:paddingVertical="4dp"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:textStyle="bold"
                tools:text="معلق" />

        </LinearLayout>

        <!-- Customer Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_person"
                android:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvCustomerName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                tools:text="أحمد محمد" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_phone"
                android:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvCustomerPhone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="07901234567" />

        </LinearLayout>

        <!-- Address -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_gravity="top"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="2dp"
                android:src="@drawable/ic_location"
                android:tint="@color/text_secondary" />

            <TextView
                android:id="@+id/tvCustomerAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxLines="2"
                android:ellipsize="end"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="بغداد - الكرادة - شارع الرشيد" />

        </LinearLayout>

        <!-- Amount and Date Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:orientation="horizontal">

            <!-- Total Amount -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_money"
                    android:tint="@color/success_green" />

                <TextView
                    android:id="@+id/tvTotalAmount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/success_green"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="1,350 د.ع" />

            </LinearLayout>

            <!-- Order Date -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/ic_calendar"
                    android:tint="@color/text_secondary" />

                <TextView
                    android:id="@+id/tvOrderDate"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp"
                    tools:text="15/01/2025" />

            </LinearLayout>

        </LinearLayout>

        <!-- Action Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <!-- View Details Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnViewDetails"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="التفاصيل"
                android:textSize="12sp"
                app:icon="@drawable/ic_info"
                app:iconGravity="textStart"
                app:iconSize="16dp" />

            <!-- Status Change Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnChangeStatus"
                style="@style/Widget.Material3.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_weight="1"
                android:text="تغيير الحالة"
                android:textSize="12sp"
                app:icon="@drawable/ic_update"
                app:iconGravity="textStart"
                app:iconSize="16dp" />

            <!-- Delete Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnDelete"
                style="@style/Widget.Material3.Button.IconButton"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:contentDescription="حذف الطلب"
                app:icon="@drawable/ic_delete"
                app:iconSize="20dp"
                app:iconTint="@color/error" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
