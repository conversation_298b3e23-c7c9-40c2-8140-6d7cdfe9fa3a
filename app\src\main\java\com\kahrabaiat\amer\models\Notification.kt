package com.kahrabaiat.amer.models

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.*

@Parcelize
data class Notification(
    val id: String = "",
    val title: String = "",
    val message: String = "",
    val type: NotificationType = NotificationType.GENERAL,
    val targetAudience: NotificationAudience = NotificationAudience.CUSTOMERS,
    val relatedId: String = "", // معرف المنتج أو الطلب المرتبط
    val imageUrl: String = "",
    val actionUrl: String = "", // رابط للانتقال عند النقر
    val isRead: Boolean = false,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val scheduledAt: Long = 0L, // للإشعارات المجدولة
    val expiresAt: Long = 0L, // تاريخ انتهاء الإشعار
    val priority: NotificationPriority = NotificationPriority.NORMAL,
    val data: Map<String, String> = emptyMap() // بيانات إضافية
) : Parcelable {

    /**
     * تنسيق تاريخ الإنشاء
     */
    fun getFormattedDate(): String {
        val date = Date(createdAt)
        val formatter = SimpleDateFormat("dd/MM/yyyy HH:mm", Locale("ar"))
        return formatter.format(date)
    }

    /**
     * تنسيق تاريخ نسبي (منذ كم من الوقت)
     */
    fun getRelativeTime(): String {
        val now = System.currentTimeMillis()
        val diff = now - createdAt
        
        return when {
            diff < 60_000 -> "الآن"
            diff < 3600_000 -> "${diff / 60_000} دقيقة"
            diff < 86400_000 -> "${diff / 3600_000} ساعة"
            diff < 604800_000 -> "${diff / 86400_000} يوم"
            else -> getFormattedDate()
        }
    }

    /**
     * التحقق من انتهاء صلاحية الإشعار
     */
    fun isExpired(): Boolean {
        return expiresAt > 0 && System.currentTimeMillis() > expiresAt
    }

    /**
     * التحقق من أن الإشعار مجدول للمستقبل
     */
    fun isScheduled(): Boolean {
        return scheduledAt > System.currentTimeMillis()
    }

    /**
     * التحقق من أن الإشعار جديد (أقل من يوم)
     */
    fun isNew(): Boolean {
        val oneDayAgo = System.currentTimeMillis() - 86400_000L
        return createdAt > oneDayAgo
    }

    /**
     * الحصول على أيقونة الإشعار حسب النوع
     */
    fun getIcon(): String {
        return when (type) {
            NotificationType.NEW_PRODUCT -> "🆕"
            NotificationType.ORDER_STATUS -> "📦"
            NotificationType.PROMOTION -> "🎫"
            NotificationType.REVIEW -> "⭐"
            NotificationType.STOCK_ALERT -> "⚠️"
            NotificationType.SYSTEM -> "🔧"
            NotificationType.GENERAL -> "📢"
        }
    }

    /**
     * الحصول على لون الإشعار حسب الأولوية
     */
    fun getColorCode(): String {
        return when (priority) {
            NotificationPriority.LOW -> "#64748B"
            NotificationPriority.NORMAL -> "#1976D2"
            NotificationPriority.HIGH -> "#FF9800"
            NotificationPriority.URGENT -> "#F44336"
        }
    }

    companion object {
        /**
         * إنشاء إشعار منتج جديد
         */
        fun createNewProductNotification(
            productName: String,
            productId: String,
            imageUrl: String = ""
        ): Notification {
            return Notification(
                id = generateNotificationId(),
                title = "منتج جديد متاح!",
                message = "تم إضافة $productName إلى المتجر",
                type = NotificationType.NEW_PRODUCT,
                targetAudience = NotificationAudience.CUSTOMERS,
                relatedId = productId,
                imageUrl = imageUrl,
                actionUrl = "product_detail/$productId",
                priority = NotificationPriority.NORMAL
            )
        }

        /**
         * إنشاء إشعار تغيير حالة الطلب
         */
        fun createOrderStatusNotification(
            orderId: String,
            customerPhone: String,
            status: String
        ): Notification {
            val statusText = when (status) {
                "confirmed" -> "تم تأكيد طلبك"
                "preparing" -> "جاري تحضير طلبك"
                "shipped" -> "تم شحن طلبك"
                "delivered" -> "تم توصيل طلبك"
                "cancelled" -> "تم إلغاء طلبك"
                else -> "تم تحديث حالة طلبك"
            }

            return Notification(
                id = generateNotificationId(),
                title = "تحديث الطلب #$orderId",
                message = statusText,
                type = NotificationType.ORDER_STATUS,
                targetAudience = NotificationAudience.SPECIFIC_CUSTOMER,
                relatedId = orderId,
                actionUrl = "order_detail/$orderId",
                priority = NotificationPriority.HIGH,
                data = mapOf("customer_phone" to customerPhone)
            )
        }

        /**
         * إنشاء إشعار عرض أو خصم
         */
        fun createPromotionNotification(
            title: String,
            message: String,
            promoCode: String = "",
            imageUrl: String = ""
        ): Notification {
            return Notification(
                id = generateNotificationId(),
                title = title,
                message = message,
                type = NotificationType.PROMOTION,
                targetAudience = NotificationAudience.CUSTOMERS,
                imageUrl = imageUrl,
                actionUrl = "promotions",
                priority = NotificationPriority.HIGH,
                data = if (promoCode.isNotEmpty()) mapOf("promo_code" to promoCode) else emptyMap(),
                expiresAt = System.currentTimeMillis() + (7 * 24 * 60 * 60 * 1000L) // ينتهي بعد أسبوع
            )
        }

        /**
         * إنشاء إشعار مراجعة جديدة (للمدير)
         */
        fun createNewReviewNotification(
            productName: String,
            customerName: String,
            rating: Float,
            reviewId: String
        ): Notification {
            return Notification(
                id = generateNotificationId(),
                title = "مراجعة جديدة",
                message = "$customerName قيّم $productName بـ $rating نجوم",
                type = NotificationType.REVIEW,
                targetAudience = NotificationAudience.ADMIN,
                relatedId = reviewId,
                actionUrl = "review_detail/$reviewId",
                priority = NotificationPriority.NORMAL
            )
        }

        /**
         * إنشاء إشعار نفاد المخزون (للمدير)
         */
        fun createStockAlertNotification(
            productName: String,
            productId: String,
            currentStock: Int
        ): Notification {
            return Notification(
                id = generateNotificationId(),
                title = "تنبيه مخزون",
                message = "المخزون منخفض: $productName ($currentStock متبقي)",
                type = NotificationType.STOCK_ALERT,
                targetAudience = NotificationAudience.ADMIN,
                relatedId = productId,
                actionUrl = "product_manage/$productId",
                priority = NotificationPriority.HIGH
            )
        }

        /**
         * إنشاء إشعار طلب جديد (للمدير)
         */
        fun createNewOrderNotification(
            orderId: String,
            customerName: String,
            total: Double
        ): Notification {
            return Notification(
                id = generateNotificationId(),
                title = "طلب جديد #$orderId",
                message = "طلب جديد من $customerName بقيمة ${total} دينار",
                type = NotificationType.ORDER_STATUS,
                targetAudience = NotificationAudience.ADMIN,
                relatedId = orderId,
                actionUrl = "order_detail/$orderId",
                priority = NotificationPriority.HIGH
            )
        }

        /**
         * توليد معرف فريد للإشعار
         */
        private fun generateNotificationId(): String {
            return "notification_${System.currentTimeMillis()}_${Random().nextInt(1000)}"
        }
    }
}

/**
 * أنواع الإشعارات
 */
enum class NotificationType {
    NEW_PRODUCT,    // منتج جديد
    ORDER_STATUS,   // حالة الطلب
    PROMOTION,      // عروض وخصومات
    REVIEW,         // مراجعات
    STOCK_ALERT,    // تنبيه مخزون
    SYSTEM,         // إشعارات النظام
    GENERAL         // إشعارات عامة
}

/**
 * الجمهور المستهدف للإشعار
 */
enum class NotificationAudience {
    ALL,                // الجميع
    CUSTOMERS,          // العملاء فقط
    ADMIN,              // المدير فقط
    SPECIFIC_CUSTOMER   // عميل محدد
}

/**
 * أولوية الإشعار
 */
enum class NotificationPriority {
    LOW,        // منخفضة
    NORMAL,     // عادية
    HIGH,       // عالية
    URGENT      // عاجلة
}

/**
 * إعدادات الإشعارات للمستخدم
 */
@Parcelize
data class NotificationSettings(
    val userId: String = "",
    val enableNewProducts: Boolean = true,
    val enableOrderUpdates: Boolean = true,
    val enablePromotions: Boolean = true,
    val enableReviews: Boolean = true,
    val enableStockAlerts: Boolean = true,
    val enableSystemNotifications: Boolean = true,
    val quietHoursStart: Int = 22, // 10 PM
    val quietHoursEnd: Int = 8,    // 8 AM
    val enableQuietHours: Boolean = false,
    val lastUpdated: Long = System.currentTimeMillis()
) : Parcelable {

    /**
     * التحقق من تفعيل نوع إشعار معين
     */
    fun isNotificationTypeEnabled(type: NotificationType): Boolean {
        return when (type) {
            NotificationType.NEW_PRODUCT -> enableNewProducts
            NotificationType.ORDER_STATUS -> enableOrderUpdates
            NotificationType.PROMOTION -> enablePromotions
            NotificationType.REVIEW -> enableReviews
            NotificationType.STOCK_ALERT -> enableStockAlerts
            NotificationType.SYSTEM -> enableSystemNotifications
            NotificationType.GENERAL -> true
        }
    }

    /**
     * التحقق من الساعات الهادئة
     */
    fun isInQuietHours(): Boolean {
        if (!enableQuietHours) return false
        
        val calendar = Calendar.getInstance()
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        
        return if (quietHoursStart < quietHoursEnd) {
            currentHour >= quietHoursStart && currentHour < quietHoursEnd
        } else {
            currentHour >= quietHoursStart || currentHour < quietHoursEnd
        }
    }
}
