<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:orientation="vertical"
    tools:context=".AdminActivity">

    <!-- Toolbar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@drawable/gradient_background"
        android:elevation="8dp"
        app:menu="@menu/admin_menu"
        app:title="@string/admin_panel"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:navigationIconTint="@color/white" />

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Statistics Cards -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <!-- Total Products Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvTotalProducts"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/primary_blue"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="25" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="إجمالي المنتجات"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

                <!-- Total Orders Card -->
                <androidx.cardview.widget.CardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:id="@+id/tvTotalOrders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/success_green"
                            android:textSize="24sp"
                            android:textStyle="bold"
                            tools:text="12" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="إجمالي الطلبات"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </androidx.cardview.widget.CardView>

            </LinearLayout>

            <!-- Sales Card -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/tvTotalSales"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/success_green"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        android:text="0 د.ع" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="إجمالي المبيعات"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:orientation="vertical">

                <!-- Add Product Button -->
                <Button
                    android:id="@+id/btnAddProduct"
                    style="@style/PrimaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_add"
                    android:drawableTint="@color/white"
                    android:text="@string/add_product" />

                <!-- Manage Products Button -->
                <Button
                    android:id="@+id/btnManageProducts"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_edit"
                    android:drawableTint="@color/primary_blue"
                    android:text="@string/manage_products" />

                <!-- View Orders Button -->
                <Button
                    android:id="@+id/btnViewOrders"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_orders"
                    android:drawableTint="@color/primary_blue"
                    android:text="@string/view_orders" />

                <!-- Statistics Button -->
                <Button
                    android:id="@+id/btnStatistics"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_chart"
                    android:drawableTint="@color/primary_blue"
                    android:text="الإحصائيات والتقارير" />

                <!-- Export Button -->
                <Button
                    android:id="@+id/btnExport"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_export"
                    android:drawableTint="@color/primary_blue"
                    android:text="تصدير البيانات" />

                <!-- Notification Settings Button -->
                <Button
                    android:id="@+id/btnNotificationSettings"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_notification"
                    android:drawableTint="@color/primary_blue"
                    android:text="إعدادات الإشعارات" />

                <!-- Custom Reports Button -->
                <Button
                    android:id="@+id/btnCustomReports"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_custom_report"
                    android:drawableTint="@color/primary_blue"
                    android:text="التقارير المخصصة" />

                <!-- Inventory Settings Button -->
                <Button
                    android:id="@+id/btnInventorySettings"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_inventory"
                    android:drawableTint="@color/warning_yellow"
                    android:text="إعدادات المخزون" />

                <!-- Security Settings Button -->
                <Button
                    android:id="@+id/btnSecuritySettings"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_security"
                    android:drawableTint="@color/error_red"
                    android:text="إعدادات الأمان" />

                <!-- Performance Monitor Button -->
                <Button
                    android:id="@+id/btnPerformanceMonitor"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_speed"
                    android:drawableTint="@color/success_green"
                    android:text="مراقبة الأداء" />

                <!-- Advanced Analytics Button -->
                <Button
                    android:id="@+id/btnAdvancedAnalytics"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_trending_up"
                    android:drawableTint="@color/primary_blue"
                    android:text="التحليلات المتقدمة" />

                <!-- Firebase Test Button -->
                <Button
                    android:id="@+id/btnFirebaseTest"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:drawableStart="@drawable/ic_network"
                    android:drawableTint="@color/error_red"
                    android:text="🔥 اختبار Firebase" />

                <!-- User Management Button -->
                <Button
                    android:id="@+id/btnUserManagement"
                    style="@style/SecondaryButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableStart="@drawable/ic_people"
                    android:drawableTint="@color/primary_blue"
                    android:text="إدارة المستخدمين" />

            </LinearLayout>

            <!-- Recent Orders Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="الطلبات الأخيرة"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/tvViewAllOrders"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="?attr/selectableItemBackground"
                            android:padding="8dp"
                            android:text="عرض الكل"
                            android:textColor="@color/primary_blue"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <!-- Recent Orders RecyclerView -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvRecentOrders"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_admin_order" />

                    <!-- Empty State -->
                    <TextView
                        android:id="@+id/tvNoOrders"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:gravity="center"
                        android:text="لا توجد طلبات حتى الآن"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:visibility="gone" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
